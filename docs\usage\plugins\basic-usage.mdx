---
title: Enhance Your LobeChat Assistant with Plugins
description: >-
  Learn how to expand your LobeChat assistant's capabilities by enabling and using various plugins. Access the Plugin Store, install plugins, and configure them to enhance your assistant's functionality.

tags:
  - LobeChat plugins
  - Plugin Store
  - Using Plugins
  - Plugin Configuration
---

# Plugin Usage

The plugin system is a key element in expanding the capabilities of assistants in LobeChat. You can enhance the assistant's abilities by enabling a variety of plugins.

Watch the following video to quickly get started with using LobeChat plugins:

<Video height={840} src="https://github.com/lobehub/lobe-chat/assets/28616219/94d4c312-1699-4e24-8782-138883678c9e" />

## Plugin Store

You can access the Plugin Store by navigating to "Extension Tools" -> "Plugin Store" in the session toolbar.

<Image alt="Plugin Store S1" height={472} src="https://github.com/lobehub/lobe-chat/assets/28616219/ab4e60d0-1293-49ac-8798-cb29b3b789e6" />

The Plugin Store allows you to directly install and use plugins within LobeChat.

<Image alt="Plugin Store S2" height={612} src="https://github.com/lobehub/lobe-chat/assets/28616219/d7a5d821-116f-4be6-8a1a-38d81a5ea0ea" />

## Using Plugins

After installing a plugin, simply enable it under the current assistant to use it.

<Image alt="Using Plugins" height={472} src="https://github.com/lobehub/lobe-chat/assets/28616219/76ab1ae7-a4f9-4285-8ebd-45b90251aba1" />

## Plugin Configuration

Some plugins may require specific configurations, such as API keys.

After installing a plugin, you can click on "Settings" to enter the plugin's settings and fill in the required configurations:

<Image alt="Plugin Configuration S1" height={472} src="https://github.com/lobehub/lobe-chat/assets/28616219/10eb3023-4528-4b06-8092-062e7b3865cc" />

<Image alt="Plugin Configuration S2" height={472} src="https://github.com/lobehub/lobe-chat/assets/28616219/ab2e4c25-4b11-431b-9266-442d8b14cb41" />
