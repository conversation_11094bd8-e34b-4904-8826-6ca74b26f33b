---
title: 在 LobeChat 中使用 OpenAI API Key
description: 学习如何在 LobeChat 中配置和使用 OpenAI API Key，支持 GPT-4o / GPT-4-turbo / GPT-4-vision
tags:
  - ChatGPT
  - GPT-4
  - GPT-4o
  - API Key
  - Web UI
---

# 在 LobeChat 中使用 OpenAI

<Image alt={'在 LobeChat 中使用 OpenAI'} cover src={'https://github.com/lobehub/lobe-chat/assets/17870709/c9e5eafc-ca22-496b-a88d-cc0ae53bf720'} />

本文档将指导你如何在 LobeChat 中使用 [OpenAI](https://openai.com/):

<Steps>
  ### 步骤一：获取 OpenAI API 密钥

  - 注册一个 [OpenAI 账户](https://platform.openai.com/signup)，你需要使用国际手机号、非大陆邮箱进行注册；

  - 注册完毕后，前往 [API Keys](https://platform.openai.com/api-keys) 页面，点击 `Create new secret key` 创建新的 API Key:

  - 打开创建窗口

  <Image
    alt={'打开创建窗口'}
    inStep
    src={
'https://github-production-user-asset-6210df.s3.amazonaws.com/28616219/296253192-ff2193dd-f125-4e58-82e8-91bc376c0d68.png'
}
  />

  - 创建 API Key

  <Image
    alt={'创建 API Key'}
    inStep
    src={
'https://github-production-user-asset-6210df.s3.amazonaws.com/28616219/296254170-803bacf0-4471-4171-ae79-0eab08d621d1.png'
}
  />

  - 获取 API Key

  <Image
    alt={'获取 API Key'}
    inStep
    src={
'https://github-production-user-asset-6210df.s3.amazonaws.com/28616219/296255167-f2745f2b-f083-4ba8-bc78-9b558e0002de.png'
}
  />

  <Callout type={'warning'}>账户注册后，一般有 5 美元的免费额度，但有效期只有三个月。</Callout>

  ### 步骤二：在 LobeChat 中配置 OpenAI

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`OpenAI`的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/3f31bc33-509f-4ad2-ba81-280c2a6ec5fa'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 OpenAI 的模型即可开始对话

  <Image alt={'选择 OpenAI 模型并开始对话'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/ff7ebacf-27f0-42d7-810b-00314499a084'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 OpenAI 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 OpenAI 提供的模型进行对话了。
