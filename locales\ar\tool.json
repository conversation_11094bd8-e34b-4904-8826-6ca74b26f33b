{"dalle": {"autoGenerate": "توليد تلقائي", "downloading": "صلاحية روابط الصور المُولَّدة بواسطة DallE3 تدوم ساعة واحدة فقط، يتم تحميل الصور إلى الجهاز المحلي...", "generate": "توليد", "generating": "جارٍ التوليد...", "images": "الصور:", "prompt": "كلمة تلميح"}, "localFiles": {"file": "مل<PERSON>", "folder": "مج<PERSON><PERSON>", "open": "فتح", "openFile": "فت<PERSON> ملف", "openFolder": "فت<PERSON> مجلد", "read": {"more": "عر<PERSON> المزيد"}, "readFile": "قراءة الملف", "readFileError": "فشل في قراءة الملف، يرجى التحقق من صحة مسار الملف", "readFiles": "قراءة الملفات", "readFilesError": "فشل في قراءة الملفات، يرجى التحقق من صحة مسار الملف"}, "search": {"createNewSearch": "إنشاء سجل بحث جديد", "emptyResult": "لم يتم العثور على نتائج، يرجى تعديل الكلمات الرئيسية والمحاولة مرة أخرى", "genAiMessage": "إنشاء رسالة مساعد", "includedTooltip": "ستدخل نتائج البحث الحالية في سياق المحادثة", "keywords": "الكلمات الرئيسية:", "scoreTooltip": "درجة الصلة، كلما كانت هذه الدرجة أعلى، كانت أكثر ارتباطًا بكلمات البحث", "searchBar": {"button": "ب<PERSON><PERSON>", "placeholder": "الكلمات الرئيسية", "tooltip": "سيتم إعادة الحصول على نتائج البحث، وإنشاء رسالة ملخص جديدة"}, "searchCategory": {"placeholder": "ابحث عن الفئة", "title": "فئة البحث:", "value": {"files": "ملفات", "general": "عام", "images": "صور", "it": "تكنولوجيا المعلومات", "map": "خريطة", "music": "موسيقى", "news": "<PERSON><PERSON><PERSON><PERSON>ر", "science": "علوم", "social_media": "وسائل التواصل الاجتماعي", "videos": "فيديوهات"}}, "searchEngine": {"placeholder": "محر<PERSON> البحث", "title": "محرك البحث:"}, "searchResult": "عد<PERSON> النتائج:", "searchTimeRange": {"title": "نطاق الوقت:", "value": {"anytime": "في أي وقت", "day": "خلال يوم واحد", "month": "خلال شهر واحد", "week": "خلال أسبوع واحد", "year": "خلال سنة واحدة"}}, "summary": "ملخص", "summaryTooltip": "تلخيص المحتوى الحالي", "viewMoreResults": "عرض المزيد من {{results}} نتيجة"}, "updateArgs": {"duplicateKeyError": "يجب أن يكون مفتاح الحقل فريدًا", "form": {"add": "إضافة عنصر", "key": "م<PERSON><PERSON><PERSON><PERSON> الحقل", "value": "قيمة الحقل"}, "formValidationFailed": "فشل التحقق من صحة النموذج، يرجى التحقق من تنسيق المعلمات", "keyRequired": "لا يمكن أن يكون مفتاح الحقل فارغًا", "stringifyError": "تعذر تسلسل المعلمات، يرجى التحقق من تنسيق المعلمات"}}