---
title: Using Cloudflare Workers AI in LobeChat
description: Learn how to integrate and utilize Cloudflare Workers AI Models in LobeChat.
tags:
  - LobeChat
  - Cloudflare
  - Workers AI
  - Provider
  - API Key
  - Web UI
---

# Using Cloudflare Workers AI in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/91fe32a8-e5f0-47ff-b8ae-d036c8a7bff1'} />

[Cloudflare Workers AI](https://www.cloudflare.com/developer-platform/products/workers-ai/) is a service that integrates AI capabilities into the Cloudflare Workers serverless computing platform. Its core functionality lies in delivering fast, scalable computing power through Cloudflare's global network, thereby reducing operational overhead.

This document will guide you on how to use Cloudflare Workers AI in LobeChat:

<Steps>
  ### Step 1: Obtain Your Cloudflare Workers AI API Key

  - Visit the [Cloudflare website](https://www.cloudflare.com/) and sign up for an account.
  - Log in to the [Cloudflare dashboard](https://dash.cloudflare.com/).
  - In the left-hand menu, locate the `AI` > `Workers AI` option.

  <Image alt={'Cloudflare Workers AI'} inStep src={'https://github.com/user-attachments/assets/4257e123-9018-4562-ac66-0f39278906f5'} />

  - In the `Using REST API` section, click the `Create Workers AI API Token` button.
  - In the drawer dialog, copy and save your `API token`.
  - Also, copy and save your `Account ID`.

  <Image alt={'Cloudflare Workers AI API Token'} inStep src={'https://github.com/user-attachments/assets/f54c912d-3ee9-4f85-b8bf-619790e51b49'} />

  <Callout type={'warning'}>
    - Please store your API token securely, as it will only be displayed once. If you accidentally
      lose it, you will need to create a new token.
  </Callout>

  ### Step 2: Configure Cloudflare Workers AI in LobeChat

  - Go to the `Settings` interface in LobeChat.
  - Under `AI Service Provider`, find the `Cloudflare` settings.

  <Image alt={'Input API Token'} inStep src={'https://github.com/user-attachments/assets/82a7ebe0-69ad-43b6-8767-1316b443fa03'} />

  - Enter the `API Token` you obtained.
  - Input your `Account ID`.
  - Choose a Cloudflare Workers AI model for your AI assistant to start the conversation.

  <Image alt={'Choose Cloudflare Workers AI Model and Start Conversation'} inStep src={'https://github.com/user-attachments/assets/09be499c-3b04-4dd6-a161-6e8ebe788354'} />

  <Callout type={'warning'}>
    You may incur charges while using the API service, please refer to Cloudflare's pricing policy for
    details.
  </Callout>
</Steps>

At this point, you can start conversing with the model provided by Cloudflare Workers AI in LobeChat.
