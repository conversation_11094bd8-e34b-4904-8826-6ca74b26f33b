---
title: 在 LobeChat 中使用火山引擎 API Key
description: 学习如何在 LobeChat 中配置和使用火山引擎 AI 模型，获取 API 密钥并开始对话。
tags:
  - LobeChat
  - 火山引擎
  - 豆包
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用火山引擎

<Image alt={'在 LobeChat 中使用火山引擎'} cover src={'https://github.com/user-attachments/assets/b9da065e-f964-44f2-8260-59e182be2729'} />

[火山引擎](https://www.volcengine.com/)是字节跳动旗下的云服务平台，通过 "火山方舟" 提供大型语言模型 (LLM) 服务，支持多个主流模型如百川智能、Mobvoi 等。

本文档将指导你如何在 LobeChat 中使用火山引擎:

<Steps>
  ### 步骤一：获取火山引擎 API 密钥

  - 首先，访问[火山引擎官网](https://www.volcengine.com/)并完成注册登录
  - 进入火山引擎控制台并导航至[火山方舟](https://console.volcengine.com/ark/)

  <Image alt={'进入火山方舟API管理页面'} inStep src={'https://github.com/user-attachments/assets/d6ace96f-0398-4847-83e1-75c3004a0e8b'} />

  - 进入 `API key 管理` 菜单，并点击 `创建 API Key`
  - 复制并保存创建好的 API Key

  ### 步骤二：在 LobeChat 中配置火山引擎

  - 访问 LobeChat 的 `应用设置` 的 `AI 服务供应商` 界面
  - 在供应商列表中找到 `火山引擎` 的设置项

  <Image alt={'填写火山引擎 API 密钥'} inStep src={'https://github.com/user-attachments/assets/237864d6-cc5d-4fe4-8a2b-c278016855c5'} />

  - 打开火山引擎服务商并填入获取的 API 密钥
  - 为你的助手选择一个火山引擎模型即可开始对话

  <Image alt={'选择火山引擎模型'} inStep src={'https://github.com/user-attachments/assets/702c191f-8250-4462-aed7-accb18b18dea'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考火山引擎的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用火山引擎提供的模型进行对话了。
