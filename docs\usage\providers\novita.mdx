---
title: Using Novita AI API Key in LobeChat
description: >-
  Learn how to integrate Novita AI's language model APIs into LobeChat. Follow the steps to register, create an Novita AI API key, configure settings, and chat with our various AI models.

tags:
  - Novita AI
  - Llama3
  - Mistral
  - uncensored
  - API key
  - Web UI
---

# Using Novita AI in LobeChat

<Image alt={'Using Novita AI in LobeChat'} cover src={'https://github.com/user-attachments/assets/b2b36128-6a43-4a1f-9c08-99fe73fb565f'} />

[Novita AI](https://novita.ai/) is an AI API platform that provides a variety of LLM and image generation APIs, supporting Llama3 (8B, 70B), Mistral, and many other cutting-edge models. We offer a variety of censored and uncensored models to meet your different needs.

This document will guide you on how to integrate Novita AI in LobeChat:

<Steps>
  ### Step 1: Register and Log in to Novita AI

  - Visit [Novita.ai](https://novita.ai/) and create an account
  - You can log in with your Google or Github account
  - Upon registration, Novita AI will provide a $0.5 credit.

  <Image alt={'Register OpenRouter'} height={457} inStep src={'https://github.com/user-attachments/assets/f3177ce2-281c-4ed4-a061-239547b466c6'} />

  ### Step 2: Obtain the API Key

  - Visit Novita AI's [key management page](https://novita.ai/dashboard/key), create and copy an API Key.

  <Image alt={'Obtain Novita AI API key'} inStep src={'https://github.com/user-attachments/assets/1e33aff2-6186-4e1f-80a8-4a2c855d8cc1'} />

  ### Step 3: Configure Novita AI in LobeChat

  - Visit the `Settings` interface in LobeChat
  - Find the setting for `novita.ai` under `AI Service Provider`

  <Image alt={'Enter Novita AI API key in LobeChat'} inStep src={'https://github.com/user-attachments/assets/00c02637-873e-4e7e-9dc3-a95085b16dd7'} />

  - Open novita.ai and enter the obtained API key
  - Choose a Novita AI model for your assistant to start the conversation

  <Image alt={'Select and use Novita AI model'} inStep src={'https://github.com/user-attachments/assets/6f9f400a-72e0-49de-94cb-5069fddf1163'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to Novita AI's pricing
    policy.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by Novita AI in LobeChat.
