---
title: 在 LobeChat 中使用无问芯穹
description: 学习如何在 LobeChat 中配置和使用无问芯穹的 API Key，实现 AI 对话交互。
tags:
  - LobeChat
  - 无问芯穹
  - API密钥
  - 大模型部署
---

# 在 LobeChat 中使用无问芯穹

[无问芯穹](https://cloud.infini-ai.com/)是基于多元芯片优化的大模型服务平台，提供高效统一的 AGI 基础设施解决方案。

本文将指导你如何在 LobeChat 中快速接入无问芯穹的 AI 能力。

<Callout type="info">
  无问芯穹的图片链接输入有白名单机制，目前已知支持阿里云 OSS / AWS S3
  等服务的图片链接。如果您在使用图片对话时遇到 400 报错，请尝试[使用 base64
  编码上传图片](/docs/self-hosting/environment-variables/s3#llm-vision-image-use-base-64)。
</Callout>

<Steps>
  ### 步骤一：获取无问芯穹 API Key

  - 登录[大模型服务平台](https://cloud.infini-ai.com/genstudio/model)
  - 在左侧导航栏选择「API KEY 管理」
  - 在新打开的页面中，点击「创建 API KEY」按钮，填入名称，点击「创建」

  ### 步骤二：配置 LobeChat 模型服务

  - 打开 LobeChat 进入「设置」界面
  - 在「语言模型」模块选择「Infini-AI」
  - 粘贴已获取的 API 密钥
</Steps>
