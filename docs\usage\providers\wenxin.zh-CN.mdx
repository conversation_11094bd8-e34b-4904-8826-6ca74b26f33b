---
title: 在 LobeChat 中使用文心千帆
description: 学习如何在 LobeChat 中配置和使用文心千帆的API Key，以便开始对话和交互。
tags:
  - LobeChat
  - 百度
  - 文心千帆
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用文心千帆

<Image cover src={'https://github.com/user-attachments/assets/e43dacf6-313e-499c-8888-f1065c53e424'} />

[文心千帆](https://qianfan.cloud.baidu.com/)是百度推出的一个人工智能大语言模型平台，支持多种应用场景，包括文学创作、商业文案生成、数理逻辑推算等。该平台具备跨模态、跨语言的深度语义理解与生成能力，广泛应用于搜索问答、内容创作和智能办公等领域。

本文将指导你如何在 LobeChat 中使用文心千帆。

<Steps>
  ### 步骤一：获得文心千帆的 API Key

  - 注册并登录 [百度智能云控制台](https://console.bce.baidu.com/)
  - 进入 `百度智能云千帆 ModelBuilder`
  - 在左侧菜单中选择 `API Key`

  <Image alt={'API Key'} inStep src={'https://github.com/user-attachments/assets/6234428d-5633-4b2f-be22-1a1772a69a55'} />

  - 点击创建 API Key
  - 在 `服务` 中选择 `千帆ModelBuilder`
  - 在 `资源` 中选择 `所有资源`
  - 点击 `确定` 按钮
  - 复制 `API Key` 并妥善保存

  <Image alt={'创建密钥'} inStep src={'https://github.com/user-attachments/assets/6d068fe0-8100-4b43-b0c3-7934f54e688f'} />

  <Image alt={'复制密钥'} inStep src={'https://github.com/user-attachments/assets/629adf4e-e9e1-40dc-b9e5-d7b908878170'} />

  ### 步骤二：在 LobeChat 中配置文心千帆

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `文心千帆` 的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/d7666e2a-0202-4b45-8338-9806ddffa44e'} />

  - 填入获得的 `API Key`
  - 为你的 AI 助手选择一个文心千帆的模型即可开始对话

  <Image alt={'选择文心千帆模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/b6e6a3eb-13c6-46f0-9c7c-69a20deae30f'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考文心千帆的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用文心千帆提供的模型进行对话了。
