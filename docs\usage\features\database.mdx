---
title: Local / Cloud Database Solutions for LobeChat
description: >-
  Explore the options of local and server-side databases for LobeChat, offering data control, privacy protection, and convenient user experiences.

tags:
  - Local Database
  - Server-Side Database
  - Data Privacy
  - Data Control
  - CRDT Technology
  - PostgreSQL
  - Dirzzle ORM
  - Clerk Authentication
---

# Local / Cloud Database

<Image alt={'Local / Cloud Database'} cover src={'https://github.com/user-attachments/assets/f1697c8b-d1fb-4dac-ba05-153c6295d91d'} />

In modern application development, the choice of data storage solution is crucial. To meet the needs of different users, LobeChat offers flexible configurations that support both local and server-side databases. Whether you prioritize data privacy and control or seek a convenient user experience, LobeChat can provide excellent solutions for you.

## Local Database: Data Control and Privacy Protection

For users who prefer more control over their data and value privacy protection, LobeChat offers support for local databases. By using IndexedDB as the storage solution and combining it with dexie as an Object-Relational Mapping (ORM) tool, LobeChat achieves efficient data management.

Additionally, we have introduced Conflict-Free Replicated Data Type (CRDT) technology to ensure a seamless multi-device synchronization experience. This experimental feature aims to provide users with greater autonomy and data security.

<Callout type={'info'}>
  LobeChat defaults to the local database solution to reduce the onboarding cost for new users.
</Callout>

Furthermore, we have attempted to introduce CRDT technology to achieve cross-device synchronization based on the local database. This experimental feature aims to provide users with greater autonomy and data security.

## Server-Side Database: Convenient and Efficient User Experience

For users who seek a convenient user experience, LobeChat supports PostgreSQL as the server-side database. By managing data with Dirzzle ORM and combining it with Clerk for authentication, LobeChat can offer users an efficient and reliable server-side data management solution.

### Server-Side Database Technology Stack

- **DB**: PostgreSQL (Neon is the default)
- **ORM**: Dirzzle ORM
- **Auth**: Clerk
- **Server Router**: tRPC

## Deployment Solution Selection Guide

### 1. Local Database

The local database solution is suitable for users who wish to have strict control over their data. With LobeChat's support for local databases, you can securely store and manage data without relying on external servers. This solution is particularly suitable for users with high requirements for data privacy.

### 2. Server-Side Database

The server-side database solution is ideal for users who want to simplify data management processes and enjoy a convenient user experience. Through server-side databases and user authentication, LobeChat can ensure the security and efficiency of data. If you want to learn how to configure a server-side database, please refer to our [detailed documentation](/docs/self-hosting/advanced/server-database).

Whether you choose a local database or a server-side database, LobeChat can provide you with an excellent user experience.
