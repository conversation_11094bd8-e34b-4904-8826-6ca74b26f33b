---
title: Using 01 AI API Key in LobeChat
description: >-
  Learn how to integrate and use 01 AI in LobeChat with step-by-step instructions. Obtain an API key, configure 01 AI, and start conversations with AI models.

tags:
  - 01.AI
  - Web UI
  - API key
  - AI models
---

# Using 01 AI in LobeChat

<Image alt={'Using 01 AI in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/********/4485fbc3-c309-4c4e-83ee-cb82392307a1'} />

[01 AI](https://www.01.ai/) is a global company dedicated to AI 2.0 large model technology and applications. Its billion-parameter Yi-Large closed-source model, when evaluated on Stanford University's English ranking AlpacaEval 2.0, is on par with GPT-4.

This document will guide you on how to use 01 AI in LobeChat:

<Steps>
  ### Step 1: Obtain 01 AI API Key

  - Register and log in to the [01 AI Large Model Open Platform](https://platform.lingyiwanwu.com/)
  - Go to the `Dashboard` and access the `API Key Management` menu
  - A system-generated API key has been created for you automatically, or you can create a new one on this interface

  <Image alt={'Create 01 AI API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/72f165f4-d529-4f01-a3ac-163c66e5ea73'} />

  - Account verification is required for first-time use

  <Image alt={'Complete Account Verification'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/e6058456-8f9d-40c1-9ae5-1e9d5eeb9476'} />

  - Click on the created API key
  - Copy and save the API key in the pop-up dialog box

  <Image alt={'Save API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/f892fe64-c734-4944-91ff-9916a41bd1c9'} />

  ### Step 2: Configure 01 AI in LobeChat

  - Access the `Settings` interface in LobeChat
  - Find the setting for `01 AI` under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/f539d104-6d64-4cc7-8781-3b36b00d32d0'} />

  - Open 01 AI and enter the obtained API key
  - Choose a 01.AI model for your AI assistant to start the conversation

  <Image alt={'Select 01.AI Model and Start Conversation'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/8bf73498-4649-4c4d-a95b-b68447599781'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider. Please refer to 01 AI's relevant fee
    policies.
  </Callout>
</Steps>

You can now use the models provided by 01 AI for conversations in LobeChat.
