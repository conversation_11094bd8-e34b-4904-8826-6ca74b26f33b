---
title: Configure Auth0 Identity Verification Service for LobeChat
description: >-
  Learn how to configure Auth0 Identity Verification Service for LobeChat for your organization, including creating applications, adding users, and configuring environment variables.

tags:
  - Auth0
  - Identity Verification
  - Single Sign-On
  - Environment Variables
  - User Management
  - SSO Integrations
  - Social Login
---

# Configure Auth0 Identity Verification Service

<Steps>
  ### Create Auth0 Application

  Register and log in to [Auth0][auth0-client-page], click on the "Applications" in the left navigation bar to switch to the application management interface, and click "Create Application" in the upper right corner to create an application.

  <Image alt="Create Auth0 Application S1" inStep src="https://github.com/lobehub/lobe-chat/assets/30863298/f068190f-0027-4d3b-8667-d632e43d5a86" />

  Fill in the application name you want to display to the organization users, choose any application type, and click "Create".

  <Image alt="Create Auth0 Application S2" inStep src="https://github.com/lobehub/lobe-chat/assets/30863298/3e0082df-9b6f-46f3-b67f-bdc79e1eb2cc" />

  After successful creation, click on the corresponding application to enter the application details page, switch to the "Settings" tab, and you can see the corresponding configuration information.

  <Image alt="Create Auth0 Application S3" inStep src="https://github.com/lobehub/lobe-chat/assets/30863298/df4cea85-616a-46f5-b2de-42725d9b82a6" />

  In the application configuration page, you also need to configure Allowed Callback URLs, where you should fill in:

  ```bash
  http(s)://your-domain/api/auth/callback/auth0
  ```

  <Image alt="Create Auth0 Application S4" inStep src="https://github.com/lobehub/lobe-chat/assets/30863298/62fbd09f-a69a-4460-949b-0f6285fa65b9" />

  <Callout type={'important'}>
    You can fill in or modify Allowed Callback URLs after deployment, but make sure the filled URL is
    consistent with the deployed URL.
  </Callout>

  ### Add Users

  Click on the "Users Management" in the left navigation bar to enter the user management interface, where you can create users for your organization to log in to LobeChat.

  <Image alt="Add Users" inStep src="https://github.com/lobehub/lobe-chat/assets/30863298/0beda150-d0b6-43cf-a9f1-fce928b83a96" />

  ### Configure Environment Variables

  When deploying LobeChat, you need to configure the following environment variables:

  | Environment Variable      | Type     | Description                                                                                                                                                                                         |
  | ------------------------- | -------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
  | `NEXT_AUTH_SECRET`        | Required | Key used to encrypt Auth.js session tokens. You can generate a key using the following command: `openssl rand -base64 32`                                                                           |
  | `NEXT_AUTH_SSO_PROVIDERS` | Required | Select the single sign-on provider for LoboChat. Use `auth0` for Auth0.                                                                                                                             |
  | `AUTH_AUTH0_ID`           | Required | Client ID of the Auth0 application                                                                                                                                                                  |
  | `AUTH_AUTH0_SECRET`       | Required | Client Secret of the Auth0 application                                                                                                                                                              |
  | `AUTH_AUTH0_ISSUER`       | Required | Domain of the Auth0 application, `https://example.auth0.com`                                                                                                                                        |
  | `NEXTAUTH_URL`            | Required | The URL is used to specify the callback address for the execution of OAuth authentication in Auth.js. It needs to be set only when the default address is incorrect. `https://example.com/api/auth` |

  <Callout type={'tip'}>
    You can refer to the related variable details at [📘Environment Variables](/docs/self-hosting/environment-variable#auth0).
  </Callout>
</Steps>

<Callout>
  After successful deployment, users will be able to authenticate and use LobeChat using the users
  configured in Auth0.
</Callout>

## Advanced Configuration

### Connecting to an Existing Single Sign-On Service

If your enterprise or organization already has a unified identity authentication infrastructure, you can connect to an existing single sign-on service in Applications -> SSO Integrations.

Auth0 supports single sign-on services such as Azure Active Directory, Slack, Google Workspace, Office 365, Zoom, and more. For a detailed list of supported services, please refer to [this link][auth0-sso-integrations].

<Image alt="Connecting to an Existing Single Sign-On Service" src="https://github.com/lobehub/lobe-chat/assets/30863298/9891347e-a338-4aa9-8714-f16c8dbcfcec" />

### Configuring Social Login

If your enterprise or organization needs to support external user logins, you can configure social login services in Authentication -> Social.

<Image alt="Configuring Social Login" src="https://github.com/lobehub/lobe-chat/assets/30863298/880749a6-5ba4-4e20-a968-b583a54de7fa" />

<Callout type={'warning'}>
  Configuring social login services by default allows anyone to authenticate, which may lead to
  LobeChat being abused by external users.
</Callout>

<Callout>
  If you need to restrict login users, be sure to configure a **blocking policy**: After enabling
  the social login option, refer to [this article][auth0-login-actions-manual] to create an Action
  to set up a blocking/allow list.
</Callout>

[auth0-client-page]: https://manage.auth0.com/dashboard
[auth0-login-actions-manual]: https://auth0.com/blog/permit-or-deny-login-requests-using-auth0-actions/
[auth0-sso-integrations]: https://marketplace.auth0.com/features/sso-integrations
