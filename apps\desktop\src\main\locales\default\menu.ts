const menu = {
  common: {
    checkUpdates: '检查更新...',
  },
  dev: {
    devPanel: '开发者面板',
    devTools: '开发者工具',
    forceReload: '强制重新加载',
    openStore: '打开存储文件',
    refreshMenu: '刷新菜单',
    reload: '重新加载',
    title: '开发',
  },
  edit: {
    copy: '复制',
    cut: '剪切',
    paste: '粘贴',
    redo: '重做',
    selectAll: '全选',
    speech: '语音',
    startSpeaking: '开始朗读',
    stopSpeaking: '停止朗读',
    title: '编辑',
    undo: '撤销',
  },
  file: {
    preferences: '首选项',
    quit: '退出',
    title: '文件',
  },
  help: {
    about: '关于',
    githubRepo: 'GitHub 仓库',
    reportIssue: '报告问题',
    title: '帮助',
    visitWebsite: '访问官网',
  },
  macOS: {
    about: '关于 {{appName}}',
    devTools: 'LobeHub 开发者工具',
    hide: '隐藏 {{appName}}',
    hideOthers: '隐藏其他',
    preferences: '偏好设置...',
    services: '服务',
    unhide: '全部显示',
  },
  tray: {
    open: '打开 {{appName}}',
    quit: '退出',
    show: '显示 {{appName}}',
  },
  view: {
    forceReload: '强制重新加载',
    reload: '重新加载',
    resetZoom: '重置缩放',
    title: '视图',
    toggleFullscreen: '切换全屏',
    zoomIn: '放大',
    zoomOut: '缩小',
  },
  window: {
    bringAllToFront: '前置所有窗口',
    close: '关闭',
    front: '前置所有窗口',
    minimize: '最小化',
    title: '窗口',
    toggleFullscreen: '切换全屏',
    zoom: '缩放',
  },
};

export default menu;
