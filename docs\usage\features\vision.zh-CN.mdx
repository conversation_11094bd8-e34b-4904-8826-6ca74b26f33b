---
title: LobeChat 支持多模态交互：视觉识别助力智能对话
description: LobeChat 支持多种具有视觉识别能力的大语言模型，用户可上传或拖拽图片，助手将识别内容并展开智能对话，打造更智能、多元化的聊天场景。
tags:
  - LobeChat
  - 多模态交互
  - 视觉识别
  - 智能对话
  - 大语言模型
---

# 模型视觉识别

<Image alt={'模型视觉识别'} borderless cover src={'https://github.com/user-attachments/assets/18574a1f-46c2-4cbc-af2c-35a86e128a07'} />

LobeChat 已经支持 OpenAI 的 [`gpt-4-vision`](https://platform.openai.com/docs/guides/vision) 、Google Gemini Pro vision、智谱 GLM-4 Vision 等具有视觉识别能力的大语言模型，这使得 LobeChat 具备了多模态交互的能力。用户可以轻松上传图片或者拖拽图片到对话框中，助手将能够识别图片内容，并在此基础上进行智能对话，构建更智能、更多元化的聊天场景。

这一特性打开了新的互动方式，使得交流不再局限于文字，而是可以涵盖丰富的视觉元素。无论是日常使用中的图片分享，还是在特定行业内的图像解读，助手都能提供出色的对话体验。
