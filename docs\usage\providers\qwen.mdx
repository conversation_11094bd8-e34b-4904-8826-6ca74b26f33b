---
title: Using Qwen2 API Key in LobeChat
description: >-
  Learn how to integrate and utilize <PERSON><PERSON> Qianwen, a powerful language model by Alibaba Cloud, in LobeChat for various tasks. Follow the steps to activate the service, obtain the API key, and configure <PERSON><PERSON> for seamless interaction.

tags:
  - <PERSON><PERSON> Qianwen
  - Alibaba Cloud
  - DashScope
  - API key
  - Web UI
---

# Using <PERSON><PERSON> in LobeChat

<Image alt={'Using <PERSON>yi Qi<PERSON>wen in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/4e057b43-1e3e-4e96-a948-7cdbff303dcb'} />

[<PERSON><PERSON>](https://tongyi.aliyun.com/) is a large-scale language model independently developed by Alibaba Cloud, with powerful natural language understanding and generation capabilities. It can answer various questions, create text content, express opinions, write code, and play a role in multiple fields.

This document will guide you on how to use <PERSON><PERSON>anwen in LobeChat:

<Steps>
  ### Step 1: Activate DashScope Model Service

  - Visit and log in to Alibaba Cloud's [DashScope](https://dashscope.console.aliyun.com/) platform.
  - If it is your first time, you need to activate the DashScope service.
  - If you have already activated it, you can skip this step.

  <Image alt={'Activate DashScope service'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/4f8d0102-7ca7-4f23-b96f-3fc5cf2cd66e'} />

  ### Step 2: Obtain DashScope API Key

  - Go to the `API-KEY` interface and create an API key.

  <Image alt={'Create Tongyi Qianwen API key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/eee046cb-189b-4635-ac94-19d50b17a18a'} />

  - Copy the API key from the pop-up dialog box and save it securely.

  <Image alt={'Copy Tongyi Qianwen API key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/cec2e032-54e1-49b1-a212-4d9736927156'} />

  <Callout type={'warning'}>
    Please store the key securely as it will only appear once. If you accidentally lose it, you will
    need to create a new key.
  </Callout>

  ### Step 3: Configure Tongyi Qianwen in LobeChat

  - Visit the `Settings` interface in LobeChat.
  - Find the setting for `Tongyi Qianwen` under `AI Service Provider`.

  <Image alt={'Enter API key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/c2e6a58b-95eb-4f40-8add-83f4316a719b'} />

  - Open Tongyi Qianwen and enter the obtained API key.
  - Choose a Qwen model for your AI assistant to start the conversation.

  <Image alt={'Select Qwen model and start conversation'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/f4a23c2a-503e-4731-bc4d-922bce0b6039'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider. Please refer to Tongyi Qianwen's
    relevant pricing policies.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by Tongyi Qianwen in LobeChat.
