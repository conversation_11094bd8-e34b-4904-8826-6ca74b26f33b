---
title: 在 LobeChat 中使用 Perplexity AI API Key
description: 学习如何在 LobeChat 中配置和使用 Perplexity AI，获取 API 密钥并选择适合的语言模型开始对话。
tags:
  - Perplexity AI
  - API key
  - Web UI
---

# 在 LobeChat 中使用 Perplexity AI

<Image alt={'在 LobeChat 中使用 Perplexity AI'} cover src={'https://github.com/lobehub/lobe-chat/assets/17870709/0c2c399f-2ed3-44b5-97c8-53e007e8c095'} />

Perplexity AI API 现在可供所有人使用，本文档将指导你如何在 LobeChat 中使用 [Perplexity AI](https://www.perplexity.ai/):

<Steps>
  ### 步骤一：获取 Perplexity AI API 密钥

  - 创建一个 [Perplexity AI](https://www.perplexity.ai/) 帐户
  - 获取您的 [API 密钥](https://www.perplexity.ai/settings/api)

  <Image alt={'创建 API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/80e22593-dc0f-482c-99bf-69acdb62d952'} />

  ### 步骤二：在 LobeChat 中配置 Perplexity AI

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`Perplexity AI`的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/51f8f8f6-5d8a-4cf0-a2e5-d96c69fe05b8'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 Perplexity AI 的模型即可开始对话

  <Image alt={'选择 Perplexity AI 模型并开始对话'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/e6a429cb-96e1-4e85-9aa3-1334ffcad8c0'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Perplexity AI 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Perplexity AI 提供的模型进行对话了。
