---
title: 在 LobeChat 中使用腾讯混元
description: 学习如何在 LobeChat 中配置和使用腾讯混元的API Key，以便开始对话和交互。
tags:
  - LobeChat
  - 腾讯混元
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用腾讯混元

<Image cover src={'https://github.com/user-attachments/assets/467bb431-ca0d-4bb4-ac17-e5e2b764a770'} />

[腾讯混元](https://hunyuan.tencent.com/)是由腾讯推出的一款大模型，旨在为用户提供智能助手服务。它能够通过自然语言处理技术，帮助用户解决问题、提供建议以及进行内容生成等任务。用户可以通过与模型的对话，快速获取所需信息，从而提高工作效率。

本文将指导你如何在 LobeChat 中使用腾讯混元。

<Steps>
  ### 步骤一：获得腾讯混元的 API Key

  - 注册并登录 [腾讯云控制台](https://console.cloud.tencent.com/hunyuan/api-key)
  - 进入 `混元大模型` 并点击 `API KEY 管理`
  - 创建一个 API 密钥

  <Image alt={'创建 API 密钥'} inStep src={'https://github.com/user-attachments/assets/5f344314-ecbc-41e6-9120-520a2d5352ff'} />

  - 点击`查看`，在弹出面板中复制 API 密钥，并妥善保存

  <Image alt={'保存密钥'} inStep src={'https://github.com/user-attachments/assets/659b5ac1-82f1-43bd-9d4b-a98491e05794'} />

  ### 步骤二：在 LobeChat 中配置腾讯混元

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `腾讯混元` 的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/796c94af-9bad-4e3c-b1c7-dbb17c215c56'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个腾讯混元的模型即可开始对话

  <Image alt={'选择腾讯混元模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/e3f44bc8-2fa5-441d-8934-943481472450'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考腾讯混元的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用腾讯混元提供的模型进行对话了。
