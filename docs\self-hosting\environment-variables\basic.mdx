---
title: Customize LobeChat Deployment with Environment Variables
description: >-
  Learn about the common environment variables used to customize LobeChat deployment, including access code, OAuth SSO, base path, and default agent configuration.

tags:
  - LobeChat
  - environment variables
  - deployment
  - access code
  - OAuth SSO
  - base path
  - default agent configuration
---

# Environment Variables

LobeChat provides some additional configuration options during deployment, which can be customized using environment variables.

## Common Variables

### `ACCESS_CODE`

- Type: Optional
- Description: Add a password to access the LobeChat service. You can set a long password to prevent brute force attacks.
- Default: -
- Example: `awCTe)re_r74` or `rtrt_ewee3@09!`

### `API_KEY_SELECT_MODE`

- Type：Optional
- Description：Controls the mode for selecting the API Key when multiple API Keys are available. Currently supports `random` and `turn`.
- Default：`random`
- Example：`random` or `turn`

When using the `random` mode, a random API Key will be selected from the available multiple API Keys.

When using the `turn` mode, the API Keys will be retrieved in a polling manner according to the specified order.

### `NEXT_PUBLIC_BASE_PATH`

- Type: Optional
- Description: Add a `basePath` for LobeChat.
- Default: -
- Example: `/test`

### `DEFAULT_AGENT_CONFIG`

- Type: Optional
- Description: Used to configure the default settings for the LobeChat default agent. It supports various data types and structures, including key-value pairs, nested fields, array values, and more.
- Default: -
- Example: `'model=gpt-4-1106-preview;params.max_tokens=300;plugins=search-engine,lobe-image-designer'`

The `DEFAULT_AGENT_CONFIG` is used to configure the default settings for the LobeChat default agent. It supports various data types and structures, including key-value pairs, nested fields, array values, and more. The table below provides detailed information on the configuration options, examples, and corresponding explanations for the `DEFAULT_AGENT_CONFIG` environment variable:

| Configuration Type      | Example                                                                                                                  | Explanation                                                                                                        |
| ----------------------- | ------------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------------------ |
| Basic Key-Value Pair    | `model=gpt-4`                                                                                                            | Set the model to `gpt-4`.                                                                                          |
| Nested Field            | `tts.sttLocale=en-US`                                                                                                    | Set the language locale for the text-to-speech service to `en-US`.                                                 |
| Array                   | `plugins=search-engine,lobe-image-designer`                                                                              | Enable the `search-engine` and `lobe-image-designer` plugins.                                                      |
| Chinese Comma           | `plugins=search-engine，lobe-image-designer`                                                                              | Same as above, demonstrating support for Chinese comma separation.                                                 |
| Multiple Configurations | `model=glm-4;provider=zhipu`                                                                                             | Set the model to `glm-4` and the model provider to `zhipu`.                                                        |
| Numeric Value           | `params.max_tokens=300`, `chatConfig.historyCount=5`                                                                     | Set the maximum tokens to `300`, Set the number of historical messages to 5.                                       |
| Boolean Value           | `chatConfig.enableAutoCreateTopic=true`, `chatConfig.enableCompressThreshold=true`, `chatConfig.enableHistoryCount=true` | Enable automatic topic creation, History length compression threshold, number of historical records.               |
| Special Characters      | `inputTemplate="Hello; I am a bot;"`                                                                                     | Set the input template to `Hello; I am a bot;`.                                                                    |
| Error Handling          | `model=gpt-4;maxToken`                                                                                                   | Ignore invalid entry `maxToken` and only parse `model=gpt-4`.                                                      |
| Value Override          | `model=gpt-4;model=gpt-4-1106-preview`                                                                                   | If a key is repeated, use the value that appears last; in this case, the value of `model` is `gpt-4-1106-preview`. |

Further reading:

- [\[RFC\] 022 - Default Assistant Parameters Configuration via Environment Variables](https://github.com/lobehub/lobe-chat/discussions/913)

### `SYSTEM_AGENT`

- Type: Optional
- Description: Used to configure models and providers for LobeChat system agents (such as topic generation, translation, etc.).
- Default value: `-`
- Example: `default=ollama/deepseek-v3` or `topic=openai/gpt-4,translation=anthropic/claude-1`

The `SYSTEM_AGENT` environment variable supports two configuration methods:

1. Use `default=provider/model` to set the same default configuration for all system agents
2. Configure specific system agents individually using the format `agent-name=provider/model`

Configuration details:

| Config Type         | Format                                          | Explanation                                                            |
| ------------------- | ----------------------------------------------- | ---------------------------------------------------------------------- |
| Default setting     | `default=ollama/deepseek-v3`                    | Set deepseek-v3 from ollama as the default model for all system agents |
| Specific setting    | `topic=openai/gpt-4`                            | Set a specific provider and model for topic generation                 |
| Mixed configuration | `default=ollama/deepseek-v3,topic=openai/gpt-4` | First set default values for all agents, then override specific agents |

Available system agents and their functions:

| System Agent        | Key Name          | Function Description                                                                                |
| ------------------- | ----------------- | --------------------------------------------------------------------------------------------------- |
| Topic Generation    | `topic`           | Automatically generates topic names and summaries based on chat content                             |
| Translation         | `translation`     | Handles text translation between multiple languages                                                 |
| Metadata Generation | `agentMeta`       | Generates descriptive information and metadata for assistants                                       |
| History Compression | `historyCompress` | Compresses and organizes history for long conversations, optimizing context management              |
| Query Rewrite       | `queryRewrite`    | Rewrites follow-up questions as standalone questions with context, improving conversation coherence |
| Thread Management   | `thread`          | Handles the creation and management of conversation threads                                         |

### `FEATURE_FLAGS`

- Type: Optional
- Description: Used to control LobeChat's feature functionalities. Supports multiple feature flags, using `+` to add a feature and `-` to disable a feature. Separate multiple feature flags with a comma `,` and enclose the entire value in quotes `"` to avoid parsing errors.
- Default: `-`
- Example: `"-welcome_suggest"`

For specific content, please refer to the [Feature Flags](/docs/self-hosting/advanced/feature-flags) documentation.

### `PROXY_URL`

- Type: Optional
- Description: Used to specify the proxy URL for connecting to external services. The value of this variable should be different in different deployment environments.
- Default: -
- Example: `http://127.0.0.1:7890` or `socks5://localhost:7891`

<Callout type="info">
  If you're using Docker Desktop on Windows or macOS, it relies on a virtual machine. In this setup,
  `localhost` / `127.0.0.1` refers to the localhost of the container itself. In such cases, please
  try using `host.docker.internal` instead of `localhost`. Use `***********************************`
  to connect to an authenticated proxy server.
</Callout>

### `ENABLE_PROXY_DNS`

- Type: Optional
- Description: Used to control whether to send DNS queries to the proxy server. When configured as `0`, all DNS queries are resolved locally. If you encounter API access failures or timeouts in your network environment, try setting this option to `1`.
- Default: `0`
- Example: `1` or `0`

### `SSRF_ALLOW_PRIVATE_IP_ADDRESS`

- Type: Optional
- Description: Allow to connect private IP address. In a trusted environment, it can be set to true to turn off SSRF protection.
- Default: `0`
- Example: `1` or `0`

### `SSRF_ALLOW_IP_ADDRESS_LIST`

- Type: Optional
- Description: Allow private IP address list, multiple IP addresses are separated by commas. Only when `SSRF_ALLOW_PRIVATE_IP_ADDRESS` is `0`, it takes effect.
- Default: -
- Example: `***********,*********`

### `ENABLE_AUTH_PROTECTION`

- Type: Optional
- Description: Controls whether to enable route protection. When set to `1`, all routes except public routes (like `/api/auth`, `/next-auth/*`, `/login`, `/signup`) will require authentication. When set to `0` or not set, only specific protected routes (like `/settings`, `/files`) will require authentication.
- Default: `0`
- Example: `1` or `0`

## Plugin Service

### `PLUGINS_INDEX_URL`

- Type: Optional
- Description: Index address of the LobeChat plugin market. If you have deployed the plugin market service on your own, you can use this variable to override the default plugin market address.
- Default: `https://chat-plugins.lobehub.com`

### `PLUGIN_SETTINGS`

- Type: Optional
- Description: Used to configure plugin settings. Use the format `plugin-name:setting-field=setting-value` to configure the settings of the plugin. Separate multiple setting fields with a semicolon `;`, and separate multiple plugin settings with a comma `,`.
- Default: `-`
- Example: `search-engine:SERPAPI_API_KEY=xxxxx,plugin-2:key1=value1;key2=value2`

The above example sets the `SERPAPI_API_KEY` of the `search-engine` plugin to `xxxxx`, and sets `key1` of `plugin-2` to `value1`, and `key2` to `value2`. The generated plugin settings configuration is as follows:

```json
{
  "plugin-2": {
    "key1": "value1",
    "key2": "value2"
  },
  "search-engine": {
    "SERPAPI_API_KEY": "xxxxx"
  }
}
```

## Assistant Market

### `AGENTS_INDEX_URL`

- Type: Optional
- Description: Index address of the LobeChat assistant market. If you have deployed the assistant market service on your own, you can use this variable to override the default market address.
- Default: `https://chat-agents.lobehub.com`
