---
title: 在 阿里云 上部署 LobeChat
description: 学习如何在阿里云上部署LobeChat应用，包括准备大模型 API Key、点击部署按钮等操作。
tags:
  - 阿里云
  - LobeChat
  - 部署流程
  - 大模型 API Key
---

# 使用 阿里云计算巢 部署

如果想在 阿里云 上部署 LobeChat，可以按照以下步骤进行操作：

## 阿里云 部署流程

<Steps>
  ### 准备好你的 API Key

  前往 [OpenAI API Key](https://platform.openai.com/account/api-keys) 获取你的 OpenAI API Key 或 前往 [通义千问 API Key](https://bailian.console.aliyun.com/?apiKey=1#/api-key) 获取你的通义千问 API Key

  ### 点击下方按钮进行部署

  [![][deploy-button-image]][deploy-link]

  ### 部署完毕后，即可开始使用
</Steps>

[deploy-button-image]: https://service-info-public.oss-cn-hangzhou.aliyuncs.com/computenest-en.svg
[deploy-link]: https://computenest.console.aliyun.com/service/instance/create/default?type=user&ServiceName=LobeChat%E7%A4%BE%E5%8C%BA%E7%89%88
