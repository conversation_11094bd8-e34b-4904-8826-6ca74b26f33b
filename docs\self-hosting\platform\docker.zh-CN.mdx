---
title: 通过 Docker 部署 LobeChat
description: 学习如何使用 Docker 部署 LobeChat 服务，包括安装 Docker 容器环境和使用指令一键启动服务。详细说明如何配置环境变量和使用代理地址。
tags:
  - Docker
  - LobeChat
  - 部署指引
  - 环境变量
  - 代理地址
  - 自动更新脚本
---

# Docker 部署指引

<div style={{display:"flex", gap: 4}}>
  [![][docker-release-shield]][docker-release-link]

  [![][docker-size-shield]][docker-size-link]

  [![][docker-pulls-shield]][docker-pulls-link]
</div>

我们提供了 [Docker 镜像][docker-release-link]，供你在自己的私有设备上部署 LobeChat 服务。

## 部署指南

<Steps>
  ### 安装 Docker 容器环境

  （如果已安装，请跳过此步）

  <Tabs items={['Ubuntu', 'CentOS']}>
    <Tab>
      ```fish
      $ apt install docker.io
      ```
    </Tab>

    <Tab>
      ```fish
      $ yum install docker
      ```
    </Tab>
  </Tabs>

  ### Docker 指令部署

  使用以下命令即可使用一键启动 LobeChat 服务：

  ```fish
  $ docker run -d -p 3210:3210 \
    -e OPENAI_API_KEY=sk-xxxx \
    -e ACCESS_CODE=lobe66 \
    --name lobe-chat \
    lobehub/lobe-chat
  ```

  指令说明：

  - 默认映射端口为 `3210`, 请确保未被占用或手动更改端口映射
  - 使用你的 OpenAI API Key 替换上述命令中的 `sk-xxxx` ，获取 API Key 的方式详见最后一节。

  <Callout type={'tip'}>
    LobeChat 支持的完整环境变量列表请参考 [📘 环境变量](/zh/docs/self-hosting/environment-variables)
    部分
  </Callout>

  <Callout>
    由于官方的 Docker
    镜像构建大约需要半小时左右，如果在更新部署后会出现「存在更新」的提示，可以等待镜像构建完成后再次部署。
  </Callout>

  <Callout type="warning">
    官方 Docker 镜像中未设定密码，强烈建议添加密码以提升安全性，否则你可能会遇到 [My API Key was
    stolen!!!](https://github.com/lobehub/lobe-chat/issues/1123) 这样的情况
  </Callout>

  <Callout type="important">
    注意，当**部署架构与镜像的不一致时**，需要对 **Sharp** 进行交叉编译，详见 [Sharp
    交叉编译](https://sharp.pixelplumbing.com/install#cross-platform)
  </Callout>

  #### 使用代理地址

  如果你需要通过代理使用 OpenAI 服务，你可以使用 `OPENAI_PROXY_URL` 环境变量来配置代理地址：

  ```fish
  $ docker run -d -p 3210:3210 \
    -e OPENAI_API_KEY=sk-xxxx \
    -e OPENAI_PROXY_URL=https://api-proxy.com/v1 \
    -e ACCESS_CODE=lobe66 \
    --name lobe-chat \
    lobehub/lobe-chat
  ```

  ### Crontab 自动更新脚本（可选）

  如果你想自动获得最新的镜像，你可以如下操作。

  首先，新建一个 `lobe.env` 配置文件，内容为各种环境变量，例如：

  ```env
  OPENAI_API_KEY=sk-xxxx
  OPENAI_PROXY_URL=https://api-proxy.com/v1
  ACCESS_CODE=arthals2333
  OPENAI_MODEL_LIST=-gpt-4,-gpt-4-32k,-gpt-3.5-turbo-16k,gpt-3.5-turbo-1106=gpt-3.5-turbo-16k,gpt-4-0125-preview=gpt-4-turbo,gpt-4-vision-preview=gpt-4-vision
  ```

  然后，你可以使用以下脚本来自动更新：

  ```bash
  #!/bin/bash
  # auto-update-lobe-chat.sh

  # 设置代理（可选）
  export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890

  # 拉取最新的镜像并将输出存储在变量中
  output=$(docker pull lobehub/lobe-chat:latest 2>&1)

  # 检查拉取命令是否成功执行
  if [ $? -ne 0 ]; then
    exit 1
  fi

  # 检查输出中是否包含特定的字符串
  echo "$output" | grep -q "Image is up to date for lobehub/lobe-chat:latest"

  # 如果镜像已经是最新的，则不执行任何操作
  if [ $? -eq 0 ]; then
    exit 0
  fi

  echo "Detected Lobe-Chat update"

  # 删除旧的容器
  echo "Removed: $(docker rm -f Lobe-Chat)"

  # 运行新的容器
  echo "Started: $(docker run -d --network=host --env-file /path/to/lobe.env --name=Lobe-Chat --restart=always lobehub/lobe-chat)"

  # 打印更新的时间和版本
  echo "Update time: $(date)"
  echo "Version: $(docker inspect lobehub/lobe-chat:latest | grep 'org.opencontainers.image.version' | awk -F'"' '{print $4}')"

  # 清理不再使用的镜像
  docker images | grep 'lobehub/lobe-chat' | grep -v 'lobehub/lobe-chat-database' | grep -v 'latest' | awk '{print $3}' | xargs -r docker rmi > /dev/null 2>&1
  echo "Removed old images."
  ```

  此脚本可以在 Crontab 中使用，但请确认你的 Crontab 可以找到正确的 Docker 命令。建议使用绝对路径。

  配置 Crontab，每 5 分钟执行一次脚本：

  ```bash
  */5 * * * * /path/to/auto-update-lobe-chat.sh >> /path/to/auto-update-lobe-chat.log 2>&1
  ```
</Steps>

## 获取 OpenAI API Key

API Key 是使用 LobeChat 进行大语言模型会话的必要信息，本节以 OpenAI 模型服务商为例，简要介绍获取 API Key 的方式。

### `A` 通过 OpenAI 官方渠道

- 注册一个 [OpenAI 账户](https://platform.openai.com/signup)，你需要使用国际手机号、非大陆邮箱进行注册；
- 注册完毕后，前往 [API Keys](https://platform.openai.com/api-keys) 页面，点击 `Create new secret key` 创建新的 API Key:

<Steps>
  #### 步骤 1：打开创建窗口

  <Image alt={'打开创建窗口'} height={600} src="https://github-production-user-asset-6210df.s3.amazonaws.com/28616219/296253192-ff2193dd-f125-4e58-82e8-91bc376c0d68.png" />

  #### 步骤 2：创建 API Key

  <Image alt={'创建 API Key'} height={600} src="https://github-production-user-asset-6210df.s3.amazonaws.com/28616219/296254170-803bacf0-4471-4171-ae79-0eab08d621d1.png" />

  #### 步骤 3：获取 API Key

  <Image alt={'获取 API Key'} height={600} src="https://github-production-user-asset-6210df.s3.amazonaws.com/28616219/296255167-f2745f2b-f083-4ba8-bc78-9b558e0002de.png" />
</Steps>

将此 API Key 填写到 LobeChat 的 API Key 配置中，即可开始使用。

<Callout type={'tip'}>
  账户注册后，一般有 5 美元的免费额度，但有效期只有三个月。如果你希望长期使用你的 API
  Key，你需要完成支付的信用卡绑定。由于 OpenAI
  只支持外币信用卡，因此你需要找到合适的支付渠道，此处不再详细展开。
</Callout>

### `B` 通过 OpenAI 第三方代理商

如果你发现注册 OpenAI 账户或者绑定外币信用卡比较麻烦，可以考虑借助一些知名的 OpenAI 第三方代理商来获取 API Key，这可以有效降低获取 OpenAI API Key 的门槛。但与此同时，一旦使用三方服务，你可能也需要承担潜在的风险，请根据你自己的实际情况自行决策。以下是常见的第三方模型代理商列表，供你参考：

| Logo                                                                                                                                              | 服务商          | 特性说明                                                     | Proxy 代理地址                | 链接                            |
| ------------------------------------------------------------------------------------------------------------------------------------------------- | ------------ | -------------------------------------------------------- | ------------------------- | ----------------------------- |
| <img src="https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/296272721-c3ac0bf3-e433-4496-89c4-ebdc20689c17.jpg" width="40" /> | **AiHubMix** | 使用 OpenAI 企业接口，全站模型价格为官方 **86 折**（含 GPT-4 、Cluade 3.5 等） | `https://aihubmix.com/v1` | [获取](https://lobe.li/CnsM6fH) |

<Callout type={'warning'}>
  **免责申明**: 在此推荐的 OpenAI API Key 由第三方代理商提供，所以我们不对 API Key 的 **有效性** 和
  **安全性** 负责，请你自行承担购买和使用 API Key 的风险。
</Callout>

[docker-pulls-link]: https://hub.docker.com/r/lobehub/lobe-chat
[docker-pulls-shield]: https://img.shields.io/docker/pulls/lobehub/lobe-chat?color=45cc11&labelColor=black&style=flat-square
[docker-release-link]: https://hub.docker.com/r/lobehub/lobe-chat
[docker-release-shield]: https://img.shields.io/docker/v/lobehub/lobe-chat?color=369eff&label=docker&labelColor=black&logo=docker&logoColor=white&style=flat-square&sort=semver
[docker-size-link]: https://hub.docker.com/r/lobehub/lobe-chat
[docker-size-shield]: https://img.shields.io/docker/image-size/lobehub/lobe-chat?color=369eff&labelColor=black&style=flat-square&sort=semver
