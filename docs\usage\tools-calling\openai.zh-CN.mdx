---
title: OpenAI GPT 系列 Tools Calling 评测
description: >-
  使用 LobeChat 测试 OpenAI GPT 系列模型（GPT 3.5-turbo / GPT-4 /GPT-4o） 的工具调用（Function Calling）能力，并展现评测结果

tags:
  - Tools Calling
  - Benchmark
  - Function Calling
  - 工具调用
  - 插件
---

# OpenAI GPT 系列工具调用（Tools Calling）

OpenAI GPT 系列模型 Tool Calling 能力一览：

| 模型            | 支持 Tool Calling | 流式 （Stream） | 并发（Parallel） | 简单指令得分 | 复杂指令 |
| ------------- | --------------- | ----------- | ------------ | ------ | ---- |
| GPT-3.5-turbo | ✅               | ✅           | ✅            | 🌟🌟🌟 | 🌟   |
| GPT-4-turbo   | ✅               | ✅           | ✅            | 🌟🌟   | 🌟🌟 |
| GPT-4o        | ✅               | ✅           | ✅            | 🌟🌟🌟 | 🌟🌟 |

<Callout type={'info'}>
  关于测试指令，详见 [工具调用 Tools Calling -
  评测任务介绍](/zh/docs/usage/tools-calling#评测任务介绍)
</Callout>

## GPT 3.5-turbo

### 简单调用指令：天气查询

测试指令：指令 ①

<Video src="https://github.com/lobehub/lobe-chat/assets/28616219/65901ee2-78b8-4f56-9e0d-6407c484f434" />

<Image alt="GPT 3.5 Turbo 简单指令的 Tool Calling" src="https://github.com/lobehub/lobe-chat/assets/28616219/1251dfc0-d1c4-4c3d-825e-dd6205793d53" />

<details>
  <summary>流式 Tool Calling 原始输出：</summary>
</details>

### 复杂调用指令：文生图

测试指令：指令 ②

<Video src="https://github.com/lobehub/lobe-chat/assets/28616219/2047665f-ab22-4da7-a390-0fb4ec5a2a14" />

<Image alt="GPT 3.5 Turbo 复杂指令的 Tool Calling" src="https://github.com/lobehub/lobe-chat/assets/28616219/125ad028-a621-4433-b5fa-321f8fd76302" />

<details>
  <summary>流式 Tool Calling 原始输出：</summary>
</details>

## GPT-4 Turbo

### 简单调用指令：天气查询

测试指令：指令 ①

GPT-4 Turbo 在调用 Tool Calling 时并没有像 GPT-3.5 Turbo 一样回复「好的」，且经过多次测试始终一样，因此在这一条复合指令的跟随中反而不如 GPT-3.5 Turbo，但剩余两项能力均不错。

当然，也有可能是因为 GPT-4 Turbo 的模型更加有 “自主意识”，认为不需要输出这一句 “好的”。

<Video src="https://github.com/lobehub/lobe-chat/assets/28616219/f865d91b-b84a-4258-ae09-9d1e15eeb43d" />

<Image alt="GPT-4 Turbo 简单指令的 Tool Calling" src="https://github.com/lobehub/lobe-chat/assets/28616219/19298693-7a9b-4b54-9e28-c46b541b4f41" />

<details>
  <summary>流式 Tool Calling 原始输出：</summary>
</details>

### 复杂调用指令：文生图

测试指令：指令 ②

<Video src="https://github.com/lobehub/lobe-chat/assets/28616219/69989faf-9b98-41ec-ba51-40cc3545d8d1" />

<Image alt="GPT-4o 复杂指令的 Tool Calling" src="https://github.com/lobehub/lobe-chat/assets/28616219/8329c1b2-5e36-4457-946c-ce3781b05afd" />

<details>
  <summary>流式 Tool Calling 原始输出：</summary>
</details>

## GPT 4o

### 简单调用指令：天气查询

测试指令：指令 ①

GPT-4o 和 3.5 一样，在简单调用指令中，能够达到非常不错的复合指令遵循能力。

<Video src="https://github.com/lobehub/lobe-chat/assets/28616219/c77b65ab-0854-4e1f-a25b-ff43275bd318" />

<Image alt="GPT-4o 简单指令的 Tool Calling" src="https://github.com/lobehub/lobe-chat/assets/28616219/e5d6214f-f628-4064-a330-cbd7c5d474ac" />

<details>
  <summary>流式 Tool Calling 原始输出：</summary>
</details>

### 复杂调用指令：文生图

测试指令：指令 ②

<Video src="https://github.com/lobehub/lobe-chat/assets/28616219/714bd86a-3b58-4941-8323-186c3fa4c6ea" />

<Image alt="GPT-4o 复杂指令的 Tool Calling" src="https://github.com/lobehub/lobe-chat/assets/28616219/8329c1b2-5e36-4457-946c-ce3781b05afd" />

<details>
  <summary>流式 Tool Calling 原始输出：</summary>

  ```yml
  ```
</details>
