{"assistants": {"addAgent": "إضافة مساعد", "addAgentAndConverse": "إضافة مساعد والدردشة", "addAgentSuccess": "تمت الإضافة بنجاح", "conversation": {"l1": "مرحبًا، أنا **{{name}}**، يمكنك أن تسألني أي سؤال وسأبذل قصارى جهدي للإجابة ~", "l2": "إليك مقدمة عن قدراتي: ", "l3": "لنبدأ المحادثة!"}, "description": "مقدمة المساعد", "detail": "تفاصيل", "details": {"capabilities": {"knowledge": {"desc": "المساعد مزود بقواعد المعرفة التالية لمساعدتك في الإجابة على المزيد من الأسئلة.", "title": "قاعدة المعرفة"}, "plugin": {"desc": "المساعد مزود بالإضافة التالية لمساعدتك في إتمام المزيد من المهام.", "title": "الإضافات المدمجة"}, "title": "قدرات المساعد"}, "overview": {"example": "عرض المساعد", "title": "نظرة عامة"}, "related": {"listTitle": "المساعدون المرتبطون", "more": "عر<PERSON> المزيد", "title": "اقتراحات ذات صلة"}, "sidebar": {"toc": "المحتوى"}, "summary": {"title": "ما الذي يمكنك فعله باستخدام هذا المساعد؟"}, "systemRole": {"openingMessage": "رسالة الافتتاح", "openingQuestions": "أسئلة الافتتاح", "title": "إعدادات المساعد"}}, "list": "قائمة المساعدين", "more": "المزيد", "plugins": "دمج الإضافات", "recentSubmits": "آخر التحديثات", "sorts": {"createdAt": "تم النشر مؤخراً", "identifier": "معرف المسا<PERSON>د", "knowledgeCount": "<PERSON><PERSON><PERSON> قواعد المعرفة", "pluginCount": "ع<PERSON><PERSON> الإضافات", "title": "اسم المساعد", "tokenUsage": "استهلاك التوكن"}, "suggestions": "اقتراحات ذات صلة", "systemRole": "إعدادات المساعد", "tokenUsage": "استهلاك توكنات تعليمات المساعد", "try": "<PERSON><PERSON><PERSON>", "withKnowledge": "هذا المساعد مزود بقاعدة معرفة", "withPlugin": "هذا المساعد مزود بالإضافة"}, "back": "عودة إلى الاكتشاف", "category": {"assistant": {"academic": "أكاديمي", "all": "الكل", "career": "مهنة", "copywriting": "كتابة نصوص", "design": "تصميم", "education": "تعليم", "emotions": "عواطف", "entertainment": "ترفيه", "games": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "general": "عام", "life": "حياة", "marketing": "تسويق", "office": "مكتب", "programming": "برمجة", "translation": "ترجمة"}, "plugin": {"all": "الكل", "gaming-entertainment": "ألعاب وترفيه", "life-style": "أسلوب حياة", "lifestyle": "نمط الحياة", "media-generate": "توليد الوسائط", "science-education": "علوم وتعليم", "social": "وسائل التواصل الاجتماعي", "stocks-finance": "أسواق مالية", "tools": "أدوات عملية", "web-search": "<PERSON><PERSON><PERSON> على الويب"}}, "cleanFilter": "<PERSON><PERSON><PERSON> الفل<PERSON>ر", "create": "إنشاء", "createGuide": {"func1": {"desc1": "ادخل إلى صفحة إعداد المساعد الذي ترغب في تقديمه من خلال الإعدادات في الزاوية العليا اليمنى من نافذة المحادثة;", "desc2": "انقر على زر التقديم إلى سوق المساعدين في الزاوية العليا اليمنى.", "tag": "الطريقة الأولى", "title": "تقديم عبر LobeChat"}, "func2": {"button": "اذهب إلى مستودع مساعدي <PERSON>ub", "desc": "إذا كنت ترغب في إضافة مساعد إلى الفهرس، يرجى استخدام agent-template.json أو agent-template-full.json لإنشاء إدخال في دليل الإضافات، كتابة وصف قصير ووضع علامات مناسبة، ثم إنشاء طلب سحب.", "tag": "الطريقة الثانية", "title": "تقديم ع<PERSON><PERSON>"}}, "dislike": "لا يعجبني", "filter": "تصفية", "filterBy": {"authorRange": {"everyone": "جميع المؤلفين", "followed": "المؤلفون المتابعون", "title": "نطاق المؤلفين"}, "contentLength": "أقل طول للسياق", "maxToken": {"title": "تعيين الحد الأقصى للطول (Token)", "unlimited": "<PERSON>ير محدود"}, "other": {"functionCall": "دعم استدعاء الوظائف", "title": "أ<PERSON><PERSON><PERSON>", "vision": "دعم التعرف البصري", "withKnowledge": "مع قاعدة المعرفة", "withTool": "مع الإضافات"}, "pricing": "أسعار النموذج", "timePeriod": {"all": "كل الوقت", "day": "آخر 24 ساعة", "month": "<PERSON><PERSON><PERSON> 30 يومًا", "title": "نطاق الوقت", "week": "آخر 7 أيام", "year": "آخر سنة"}}, "home": {"featuredAssistants": "مساعدون مميزون", "featuredModels": "نماذج مميزة", "featuredProviders": "مزودو نماذج مميزون", "featuredTools": "إضافات مميزة", "more": "اكتش<PERSON> المزيد"}, "isClaimed": "تم المطالبة", "isFeatured": "مميز", "isOfficial": "معتمد رسميًا", "like": "<PERSON><PERSON><PERSON>", "mcp": {"categories": {"all": {"description": "جميع خوادم MCP", "name": "الكل"}, "business": {"description": "الخدمات التجارية والمؤسساتية", "name": "الخدمات التجارية"}, "developer": {"description": "أدوات وخدمات متعلقة بالتطوير", "name": "أدوات التطوير"}, "gaming-entertainment": {"description": "الألعاب، الترفيه والأنشطة الترفيهية", "name": "الألعاب والترفيه"}, "health-wellness": {"description": "الصحة، اللياقة والعناية بالجسم والعقل", "name": "الصحة والعافية"}, "lifestyle": {"description": "أسلوب الحياة الشخصية، العادات والأنشطة اليومية", "name": "نمط الحياة"}, "media-generate": {"description": "إنشاء، تحرير ومعالجة الوسائط", "name": "إنشاء الوسائط"}, "news": {"description": "تجميع الأخبار، التقارير وخدمات المعلومات", "name": "الأخبار والمعلومات"}, "productivity": {"description": "إدارة المهام، الملاحظات وأدوات الكفاءة", "name": "أدوات الإنتاجية"}, "science-education": {"description": "البحث العلمي، التعلم وأدوات التعليم", "name": "العلوم والتعليم"}, "social": {"description": "الشبكات الاجتماعية والتواصل", "name": "وسائل التواصل الاجتماعي"}, "stocks-finance": {"description": "أسواق المال، التداول والاستثمار", "name": "الأسهم والمالية"}, "tools": {"description": "أدوات وخدمات عامة وعملية", "name": "أدوات عملية"}, "travel-transport": {"description": "تخطيط السفر والتنقل", "name": "السفر والنقل"}, "weather": {"description": "توقعات الطقس وخدمات الأرصاد الجوية", "name": "الطقس والأرصاد"}, "web-search": {"description": "البحث على الويب واسترجاع المعلومات", "name": "استرجاع المعلومات"}}, "details": {"connectionType": {"hybrid": {"desc": "هذه الخدمة يمكن تشغيلها محلياً أو على السحابة حسب الإعداد أو سيناريو الاستخدام، وتتمتع بقدرة تشغيل مزدوجة.", "title": "خدمة هجينة"}, "local": {"desc": "هذا الخادم يعمل فقط على جهاز العميل المحلي، ويتطلب التثبيت والاعتماد على الموارد المحلية.", "title": "خدمة محلية"}, "remote": {"desc": "هذا الخادم مستضاف ويعمل عن بُعد، لأنه يعتمد بشكل رئيسي على خدمات بعيدة ولا يعتمد على البيئة المحلية.", "title": "خدمة سحابية"}}, "deployment": {"args": "المعطيات", "checkCommand": "<PERSON><PERSON><PERSON> التحقق", "command": "الأمر", "commandLine": "اعتمادات النظام", "connection": "طريقة الاتصال", "connectionType": "نوع الاتصال", "description": "طريقة تثبيت ونشر الإضافة", "descriptionPlaceholder": "وصف اختياري", "empty": "لا توجد خيارات نشر حالياً", "env": "متغيرات البيئة", "guide": "تعليمات التثبيت", "installation": "التثبيت عبر {{method}}", "installationMethod": "طريقة التثبيت", "other": "إعدادات أخرى", "packageName": "اسم الحزمة", "platform": {"steps": {"claude": "- افتح تطبيق **Claude Desktop**\n- اذهب إلى **الإعدادات** ثم اختر **المطور**\n- اضغط على **تحرير الإعدادات**\n- افتح ملف **claude_desktop_config.json**\n- انسخ والصق إعدادات الخادم في الملف الحالي ثم احفظ", "cline": "- افتح VS Code مع إضافة Cline المثبتة\n- اضغط على أيقونة Cline في الشريط الجانبي\n- اختر **MCP Servers** من القائمة المنسدلة\n- في تبويب **الخوادم البعيدة**، أدخل اسم الخادم ورابط MCP الخاص بك\n- اضغط **Add Server** للاتصال", "cursor": "- انتقل إلى **الإعدادات** ثم إعدادات Cursor\n- اختر **MCP** من الجانب الأيسر\n- اضغط على **إضافة خادم MCP عالمي جديد** في الأعلى يمين\n- انسخ والصق إعدادات الخادم في الملف الحالي ثم احفظ", "lobeChat": "- افتح تطبيق **LobeChat لسطح المكتب**\n- اذهب إلى **الإعدادات** - **المساعد الافتراضي**\n- اختر **إعدادات الإضافة** - **إضافة مخصصة**\n- اضغط على **استيراد سريع لإعدادات JSON**\n- انسخ والصق إعدادات الخادم في مربع النص ثم ثبت", "openai": "- افتح تطبيق **OpenAI** أو بيئة التطوير الخاصة بك\n- قم بإعداد أدوات MCP في **Responses API**\n- أضف كتلة MCP إلى مصفوفة **tools** في طلب API\n- عيّن **server_url** إلى نقطة نهاية خادم MCP الخاص بك\n- أدرج رؤوس المصادقة المطلوبة (مفتاح API، رموز، إلخ)\n- استخدم معلمة `allowed_tools` لتقييد الأدوات المكشوفة\n- عيّن `require_approval` للتحكم في موافقة تنفيذ الأدوات", "vscode": "- افتح VS Code\n- افتح لوحة الأوامر (`Ctrl+Shift+P` / `Cmd+Shift+P`)\n- اكتب **MCP: Add Server** واختره\n- اختر الإضافة إلى مساحة العمل أو إعدادات المستخدم\n- انسخ والصق إعدادات الخادم"}, "title": "التثبيت على {{platform}}"}, "recommended": "موصى به", "systemDependencies": "اعتمادات النظام", "table": {"description": "الوصف", "name": "الاسم", "required": "مطلوب", "type": "النوع"}, "title": "طريقة التثبيت"}, "githubBadge": {"desc": "يقوم LobeHub بمسح مستودعات الأكواد والوثائق بانتظام من أجل:\n\n- التأكد من أن خوادم MCP تعمل بشكل صحيح.\n- استخراج ميزات الخادم مثل الأدوات، الموارد، التعليمات والمعطيات المطلوبة.\n- تساعد شارة التقييم المستخدمين على تقييم أمان الخادم، ميزاته ودليل التثبيت بسرعة.\n\nيرجى نسخ الكود التالي إلى ملف `README.md` الخاص بك:"}, "nav": {"needHelp": "هل تحتاج مساعدة؟", "reportIssue": "الإبلاغ عن مشكلة", "viewSourceCode": "عرض الشيفرة المصدرية"}, "overview": {"title": "نظرة عامة"}, "related": {"listTitle": "خوادم MCP ذات الصلة", "more": "عر<PERSON> المزيد", "title": "اقتراحات ذات صلة"}, "schema": {"mode": {"docs": "الوثائق"}, "prompts": {"arguments": "إعدادات المعطيات", "desc": "قوالب تفاعلية يتم تفعيلها باختيار المستخدم", "empty": "لا توجد تعليمات حالياً", "instructions": "تعليمات", "table": {"description": "الوصف", "name": "الاسم", "required": "مطلوب"}, "title": "قائمة التعليمات"}, "resources": {"desc": "بيانات سياقية مرفقة ومدارة من قبل العميل", "empty": "لا توجد موارد", "table": {"description": "الوصف", "mineType": "نوع MIME", "name": "الاسم", "uri": "رابط URI"}, "title": "قائمة الموارد"}, "title": "وظائف الإضافة", "tools": {"desc": "واجهات وظائف مكشوفة لنموذج اللغة الكبير (LLM) لتنفيذ العمليات", "empty": "لا توجد أدوات", "inputSchema": "وصف الإدخال", "instructions": "تعليمات", "table": {"description": "الوصف", "name": "الاسم", "required": "مطلوب", "type": "النوع"}, "title": "قائمة الأدوات"}}, "score": {"claimed": {"desc": "تمت المطالبة بهذا الخادم من قبل المالك، مما يضمن ملكيته وإدارته.", "title": "مطالب به من قبل المالك"}, "deployMoreThanManual": {"desc": "يوفر هذا الخادم طرق تثبيت أكثر سهولة من الطريقة اليدوية، مما يسمح للمستخدمين بالنشر والاستخدام بسهولة.", "title": "يوفر طرق تثبيت سهلة"}, "deployment": {"desc": "يوفر هذا الخادم على الأقل طريقة تثبيت واحدة تسمح للمستخدمين بالنشر والاستخدام.", "descWithCount": "يوفر هذا الخادم {{number}} طريقة تثبيت تسمح للمستخدمين بالنشر والاستخدام.", "title": "يوفر طريقة تثبيت واحدة على الأقل"}, "license": {"desc": "يحتوي هذا المستودع على ملف LICENSE.", "descWithlicense": "رخصة هذا المستودع هي {{license}}.", "title": "يحتوي على رخصة"}, "listTitle": "تفاصيل التقييم", "notClaimed": {"desc": "إذا كنت مالك هذا الخادم، يمكنك المطالبة به بالطرق التالية.", "title": "<PERSON>ير مطالب به من قبل المالك"}, "prompts": {"desc": "يوفر هذا الخادم تعليمات تسمح للمستخدمين بالتفاعل مع الخدمة.", "descWithCount": "يوفر هذا الخادم {{number}} تعليمات تسمح للمستخدمين بالتفاعل مع الخدمة.", "title": "يحتوي على تعليمات"}, "readme": {"desc": "يحتوي هذا المستودع على ملف README.md.", "title": "يحتوي على README"}, "resources": {"desc": "يوفر هذا الخادم موارد تسمح للمستخدمين بإرفاق وإدارة بيانات السياق.", "descWithCount": "يوفر هذا الخادم {{number}} موارد تسمح للمستخدمين بإرفاق وإدارة بيانات السياق.", "title": "يحتوي على موارد"}, "title": "التقييم", "tools": {"desc": "يج<PERSON> أن يوفر الخادم أداة واحدة على الأقل تسمح للمستخدمين بتنفيذ عمليات محددة.", "descWithCount": "يوفر هذا الخادم {{number}} أداة تسمح للمستخدمين بتنفيذ عمليات محددة.", "title": "يحتوي على أداة واحدة على الأقل"}, "validated": {"desc": "تم التحقق من هذا الخادم لضمان جودته وموثوقيته.", "title": "تم التحقق منه"}}, "scoreLevel": {"a": {"desc": "تم التحقق من هذا الخادم بدقة، ويوفر وظائف شاملة وتجربة مستخدم عالية الجودة.", "fullTitle": "إضافة ممتازة", "title": "مم<PERSON><PERSON><PERSON>"}, "b": {"desc": "يوفر هذا الخادم وظائف وتجربة مستخدم جيدة، لكنه قد يحتاج إلى تحسين في بعض الجوانب.", "fullTitle": "وظائف جيدة", "title": "<PERSON>ي<PERSON>"}, "f": {"desc": "وظائف هذا الخادم غير مكتملة أو جودته منخفضة، ينصح المستخدمون بالحذر عند الاستخدام.", "fullTitle": "جودة ضعيفة", "title": "ضعيف"}}, "settings": {"capabilities": {"prompts": "التلميحات", "resources": "الموارد", "title": "قدرات الإضافة", "tools": "الأدوات"}, "configuration": {"title": "تكوين الإضافة"}, "connection": {"args": "معاملات التشغيل", "command": "<PERSON><PERSON><PERSON> التشغيل", "title": "معلومات الاتصال", "type": "نوع الاتصال", "url": "عنوان الخدمة"}, "saveSettings": "ح<PERSON><PERSON> الإعدادات", "title": "إعدادات الإضافة"}, "sidebar": {"install": "تثبيت خادم MCP", "meta": {"homepage": "الصفحة الرئيسية", "installCount": "ع<PERSON><PERSON> التثبيتات", "language": "لغة المصدر", "license": "الرخصة", "published": "تاريخ النشر", "repo": "مستودع الشيفرة", "stars": "<PERSON><PERSON><PERSON> النجوم", "title": "معلومات مفصلة", "updated": "آخر تحديث"}, "moreServerConfig": "عرض التفاصيل", "recommendServers": "خوادم MCP ذات صلة", "serverConfig": "إعدادات التثبيت", "toc": "المحتوى"}, "summary": {"title": "ما الذي يمكنك فعله باستخدام خادم MCP هذا؟"}, "totalScore": {"description": "الدرجة الإجمالية المحسوبة بناءً على مؤشرات متعددة", "legend": {"aGrade": "الدرجة أ ({{minPercent}}-100%)", "bGrade": "الدرجة ب ({{minPercent}}-{{maxPercent}}%)", "fGrade": "الدرجة ف (0-{{maxPercent}}%)"}, "pointsFormat": "{{score}}/{{total}} نقاط", "popover": {"completedOptional": "✅ تم إكمال الخيارات الاختيارية ({{count}} عناصر)", "completedRequired": "✅ تم إكمال العناصر المطلوبة ({{count}} عناصر)", "incompleteOptional": "⏸️ لم تكتمل الخيارات الاختيارية ({{count}} عناصر)", "incompleteRequired": "❌ لم تكتمل العناصر المطلوبة ({{count}} عناصر)", "title": "تفاصيل التقييم"}, "ratingFormat": "التقييم: {{level}}", "scoreInfo": {"items": "عناصر", "points": "نقاط", "requiredItems": "عناصر مطلوبة"}, "title": "الدرجة الإجمالية"}, "versions": {"table": {"isLatest": "<PERSON><PERSON><PERSON><PERSON> إصدار", "isValidated": "تم التحقق", "publishAt": "تاريخ النشر", "version": "الإصدار"}, "title": "سجل الإصدارات"}}, "hero": {"desc": "منصة MCP Servers مفتوحة المصدر وقابلة للنشر، تساعد أنظمة الذكاء الاصطناعي على الوصول بسهولة إلى أنظمة الملفات، قواعد البيانات، واجهات برمجة التطبيقات وغيرها من الموارد الحيوية، لتوسيع قدرات الذكاء الاصطناعي الخاصة بك بشكل شامل.", "subTitle": "مفتوح المصدر وجاهز للاستخدام", "title": "سوق MCP مفتوح المصدر للذكاء الاصطناعي"}, "sorts": {"createdAt": "أضيف مؤخراً", "installCount": "ع<PERSON><PERSON> التثبيتات", "isFeatured": "الإضافات المميزة", "isValidated": "الإضافات التي تم التحقق منها", "promptsCount": "ع<PERSON><PERSON> التعليمات", "ratingCount": "ع<PERSON><PERSON> التقييمات", "resourcesCount": "<PERSON><PERSON><PERSON> الموارد", "toolsCount": "ع<PERSON><PERSON> الأدوات", "updatedAt": "تم التحديث مؤخراً"}, "title": "سوق MCP", "unvalidated": {"desc": "هذا الخادم MCP لم يتم التحقق منه بعد", "title": "غير مُحقق"}, "validated": {"desc": "تم التحقق من هذا الخادم MCP لضمان جودته وموثوقيته.", "descWithDate": "تم التحقق من هذا الخادم MCP بتاريخ {{date}} لضمان جودته وموثوقيته.", "title": "تم التحقق"}}, "models": {"abilities": "قدرات النموذج", "chat": "بد<PERSON> المحادثة", "contentLength": "أقصى طول للسياق", "details": {"overview": {"title": "نظرة عامة"}, "related": {"listTitle": "النماذج ذات الصلة", "more": "عر<PERSON> المزيد", "title": "اقتراحات ذات صلة"}}, "free": "مجاني", "guide": "دليل الإعداد", "list": "قائمة النماذج", "more": "المزيد", "parameterList": {"defaultValue": "القيمة الافتراضية", "docs": "عرض الوثائق", "frequency_penalty": {"desc": "تقوم هذه الإعدادات بتعديل تكرار استخدام النموذج لكلمات معينة ظهرت بالفعل في المدخلات. القيم الأعلى تقلل من احتمال تكرار هذه الكلمات، بينما القيم السلبية تعزز استخدامها. عقوبة الكلمات لا تزداد مع زيادة عدد مرات الظهور. القيم السلبية ستشجع على تكرار الكلمات.", "title": "عقوبة التكرار"}, "max_tokens": {"desc": "تحدد هذه الإعدادات الحد الأقصى لطول النص الذي يمكن أن ينتجه النموذج في رد واحد. يسمح تعيين قيمة أعلى للنموذج بإنتاج ردود أطول، بينما تحدد القيمة المنخفضة طول الردود، مما يجعلها أكثر اختصارًا. يمكن أن يساعد ضبط هذه القيمة بشكل معقول وفقًا لمختلف سيناريوهات الاستخدام في تحقيق الطول والتفاصيل المتوقعة للرد.", "title": "<PERSON><PERSON> ا<PERSON><PERSON><PERSON> الواحد"}, "presence_penalty": {"desc": "تهدف هذه الإعدادات إلى التحكم في تكرار استخدام الكلمات بناءً على تكرار ظهورها في المدخلات. تحاول تقليل استخدام الكلمات التي ظهرت كثيرًا في المدخلات، حيث يتناسب تكرار استخدامها مع تكرار ظهورها. عقوبة الكلمات تزداد مع عدد مرات الظهور. القيم السلبية ستشجع على تكرار الكلمات.", "title": "جدة الموضوع"}, "range": "نطاق", "reasoning_effort": {"desc": "تُستخدم هذه الإعدادات للتحكم في شدة التفكير التي يقوم بها النموذج قبل توليد الإجابات. الشدة المنخفضة تعطي الأولوية لسرعة الاستجابة وتوفر الرموز، بينما الشدة العالية توفر تفكيرًا أكثر اكتمالًا ولكنها تستهلك المزيد من الرموز وتقلل من سرعة الاستجابة. القيمة الافتراضية هي متوسطة، مما يوازن بين دقة التفكير وسرعة الاستجابة.", "title": "شدة التفكير"}, "temperature": {"desc": "تؤثر هذه الإعدادات على تنوع استجابة النموذج. القيم المنخفضة تؤدي إلى استجابات أكثر توقعًا ونمطية، بينما القيم الأعلى تشجع على استجابات أكثر تنوعًا وغير شائعة. عندما تكون القيمة 0، يعطي النموذج نفس الاستجابة دائمًا لنفس المدخل.", "title": "عشوائية"}, "title": "معلمات النموذج", "top_p": {"desc": "تحدد هذه الإعدادات اختيار النموذج للكلمات ذات الاحتمالية الأعلى فقط: اختيار الكلمات التي تصل احتمالاتها التراكمية إلى P. القيم المنخفضة تجعل استجابات النموذج أكثر توقعًا، بينما الإعداد الافتراضي يسمح للنموذج بالاختيار من جميع نطاق الكلمات.", "title": "عينات النواة"}, "type": "نوع"}, "providerInfo": {"apiTooltip": "يدعم LobeChat استخدام مفتاح API مخصص لهذا المزود.", "input": "سعر الإدخال", "inputTooltip": "تكلفة لكل مليون Token", "latency": "زمن الاستجابة", "latencyTooltip": "متوسط زمن استجابة المزود لإرسال أول Token", "maxOutput": "أقصى طول للإخراج", "maxOutputTooltip": "ع<PERSON><PERSON> الأقصى الذي يمكن أن ينتجه هذا النقطة", "officialTooltip": "خدمة رسمية من <PERSON>", "output": "سعر الإخراج", "outputTooltip": "تكلفة لكل مليون Token", "streamCancellationTooltip": "يدعم هذا المزود ميزة إلغاء التدفق.", "throughput": "معدل النقل", "throughputTooltip": "متوسط عد<PERSON> المنقولة في الطلبات المتدفقة في الثانية"}, "sorts": {"contextWindowTokens": "طول السياق", "identifier": "معرف النموذج", "inputPrice": "سعر الإدخال", "outputPrice": "سعر الإخراج", "providerCount": "ع<PERSON><PERSON> المزودين", "releasedAt": "تم النشر مؤخراً"}, "suggestions": "نماذج ذات صلة", "supportedProviders": "مزودو الخدمة المدعومون لهذا النموذج"}, "plugins": {"community": "إضافات المجتمع", "details": {"settings": {"title": "إعدادات الإضافة"}, "summary": {"title": "ما الذي يمكنك فعله باستخدام هذه الإضافة؟"}, "tools": {"title": "أدوات الإضافة"}}, "install": "تثبيت الإضافة", "installed": "تم التثبيت", "list": "قائمة الإضافات", "meta": {"description": "وصف", "parameter": "معامل", "title": "معامل الأداة", "type": "نوع"}, "more": "المزيد", "official": "إضافات رسمية", "recentSubmits": "آخر التحديثات", "sorts": {"createdAt": "تم النشر مؤخراً", "identifier": "معرف الإضافة", "title": "اسم الإضافة"}, "suggestions": "اقتراحات ذات صلة"}, "providers": {"config": "تكوين مزود الخدمة", "details": {"guide": {"title": "دليل الاندماج"}, "overview": {"title": "نظرة عامة"}, "related": {"listTitle": "مزودو الخدمة ذات الصلة", "more": "عر<PERSON> المزيد", "title": "اقتراحات ذات صلة"}}, "list": "قائمة مزودي النماذج", "modelCount": "{{count}} نموذج", "modelName": "اسم النموذج", "modelSite": "وثائق النموذج", "more": "المزيد", "officialSite": "الموقع الرسمي", "showAllModels": "عرض جميع النماذج", "sorts": {"default": "الترتيب الافتراضي", "identifier": "معر<PERSON> المزود", "modelCount": "<PERSON><PERSON><PERSON> النماذج"}, "suggestions": "مزودو الخدمة ذوو الصلة", "supportedModels": "النماذج المدعومة"}, "publishedTime": "نشر في", "search": {"placeholder": "ابحث عن اسم أو كلمة مفتاحية...", "result": "{{count}} نتيجة بحث حول <highlight>{{keyword}}</highlight>", "searching": "جارٍ البحث..."}, "tab": {"assistant": "المساعد", "home": "الصفحة الرئيسية", "model": "النموذج", "plugin": "الإضافة", "provider": "مزو<PERSON> النموذج"}}