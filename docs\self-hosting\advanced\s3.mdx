---
title: Configuring S3 Storage Service for AI - LobeChat
description: Learn how to configure S3 storage for LobeChat's multimodal AI conversations.
tags:
  - S3 Storage
  - LobeChat
  - AI Conversations
  - Cloud Storage
  - Image Upload
---

# Configuring S3 Storage Service

LobeChat has supported multimodal AI conversations since [a long time ago](https://x.com/lobehub/status/1724289575672291782), which involves the function of uploading images to AI. In the client-side database solution, image files are stored as binary data directly in the browser's IndexedDB database. However, this solution is not feasible in the server-side database. Storing file-like data directly in Postgres would greatly waste database storage space and slow down computational performance.

The best practice in this area is to use a file storage service (S3) to store image files. S3 is also the storage solution on which the file upload/knowledge base function depends.

<Callout type={'info'}>
  In this documentation, S3 refers to a compatible S3 storage solution, which supports the Amazon S3
  API for object storage systems. Common examples include Cloudflare R2, Alibaba Cloud OSS, and
  self-deployable Minio, all of which support the S3 compatible API.
</Callout>

## Core Environment Variables

<Steps>
  ### `S3_ACCESS_KEY_ID` and `S3_SECRET_ACCESS_KEY`

  These are the two keys required by all S3 compatible storage services to access the S3 storage service, not detailed here.

  ### `S3_ENDPOINT`

  The request endpoint of the storage bucket. Note that this link should not contain the name of the storage bucket.

  <Callout type={'warning'}>
    `S3_ENDPOINT` must remove the suffix path, otherwise the uploaded files will not be accessible
  </Callout>

  For example, for Cloudflare:

  ```shell
  S3_ENDPOINT=https://0b33a03b5c993fd2f453379dc36558e5.r2.cloudflarestorage.com
  ```

  ### `S3_BUCKET` and `S3_REGION`

  The name and region of the storage bucket. `S3_BUCKET` is required to specify the name of the storage bucket. `S3_REGION` is optional and is used to specify the region of the storage bucket. Generally, it does not need to be added, but some service providers may require configuration.

  ### `S3_SET_ACL`

  Whether to set the ACL to `public-read` when uploading files. This option is enabled by default. If the service provider does not support setting individual ACLs for files (i.e., all files inherit the ACL of the storage bucket), enabling this option may cause request errors. Set `S3_SET_ACL` to `0` to disable it.

  ### `S3_PUBLIC_DOMAIN`

  The public access domain of the storage bucket, used to access files in the storage bucket. This address needs to be **publicly readable**. The reason is that when OpenAI's gpt-4o and other vision models recognize images, OpenAI will try to download this image link on their servers. Therefore, this link must be publicly accessible. If it is a private link, OpenAI will not be able to access the image and thus will not be able to recognize the image content properly.

  <Callout type={'warning'}>
    Additionally, since this access domain is often a separate URL, it needs to be configured to allow
    cross-origin access to the site. Otherwise, cross-origin issues will occur in the browser.
  </Callout>

  ### `S3_ENABLE_PATH_STYLE`

  Whether to enable the `path-style` access mode of S3. This option is disabled by default. If your S3 service provider uses `path-style`, set `S3_ENABLE_PATH_STYLE` to `1` to enable it.

  <Callout type={'info'}>
    `path-style` and `virtual-host` are different ways to access buckets and objects in S3, with different URL structures and domain name resolutions.

    Assuming the domain name of the S3 service provider is s3.example.net, the bucket is mybucket, and the object is config.env, the specific differences are as follows:

    - path-style: `s3.example.net/mybucket/config.env`
    - virtual-host: `mybucket.s3.example.net/config.env`
  </Callout>

  <Callout type={'tip'}>
    Common S3 cloud service providers often default to the `virtual-host` mode, while self-deployed services like Minio default to using `path-style`. Therefore, if you use Minio as the S3 service, you need to set `S3_ENABLE_PATH_STYLE=1`.
  </Callout>
</Steps>

## S3 Configuration Guide

Currently, the S3 configuration tutorials included in the documentation are:

<Cards>
  <Card href={'/docs/self-hosting/advanced/s3/cloudflare-r2'} title={'Cloudflare R2'} />

  <Card href={'/docs/self-hosting/advanced/s3/tencent-cloud'} title={'Tencent Cloud COS'} />
</Cards>

Click to view the tutorial for the corresponding platform. If the above tutorials do not include the S3 service provider you are using, feel free to submit a Pull Request to collectively improve the guide on S3 object storage.
