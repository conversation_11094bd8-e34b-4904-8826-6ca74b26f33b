---
title: Using Azure OpenAI API Key in LobeChat
description: >-
  Learn how to integrate and configure Azure OpenAI in LobeChat to enhance your AI assistant capabilities. Follow these steps to obtain the API key, configure the settings, and start engaging in conversations.

tags:
  - Azure OpenAI
  - AI assistant
  - API key
  - Configuration
  - Conversation models
---

# Using Azure OpenAI in LobeChat

<Image alt={'Azure OpenAI in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/********/5efa34c2-6523-43e6-9ade-70ab5d802e13'} />

This document will guide you on how to use [Azure OpenAI](https://oai.azure.com/) in LobeChat:

<Steps>
  ### Step 1: Obtain Azure OpenAI API Key

  - If you haven't registered yet, you need to create an [Azure OpenAI account](https://oai.azure.com/).

  <Image alt={'Create an Azure OpenAI account'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/a77b0fb2-87d7-4527-a804-2f7ad3634aa5'} />

  - After registration, go to the `Deployments` page and create a new deployment with your selected model.

  ![Create a new deployment with the selected model](https://github.com/lobehub/lobe-chat/assets/********/4fae3e6f-e680-4471-93c4-987c19d7170a)

  <Image alt={'Create a new deployment with your selected model'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/4fae3e6f-e680-4471-93c4-987c19d7170a'} />

  - Navigate to the `Chat` page and click on `View Code` to obtain your endpoint and key.

  <Image alt={'Go to the Chat page'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/ac10d9dd-a977-43fb-8397-b2bbdee6a1a1'} />

  <Image alt={'Get the endpoint and key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/ab94a7b5-6bc4-41e0-97bc-724ee8e315db'} />

  ### Step 2: Configure Azure OpenAI in LobeChat

  - Access the `Settings` interface in LobeChat.
  - Find the setting for `Azure OpenAI` under `AI Service Provider`.

  <Image alt={'Enter the API key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/63d9f6d4-5b78-4c65-8cd1-ff8b7f143406'} />

  - Enter the API key you obtained.
  - Choose an Azure OpenAI model for your AI assistant to start the conversation.

  <Image alt={'Select Azure OpenAI model and start the conversation'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/ddb44517-8696-4492-acd9-25b590f6069c'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider. Please refer to Azure OpenAI's
    relevant pricing policies.
  </Callout>
</Steps>

Now you can engage in conversations using the models provided by Azure OpenAI in LobeChat.
