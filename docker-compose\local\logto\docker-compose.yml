name: lobe-chat-database
services:
  network-service:
    image: alpine
    container_name: lobe-network
    ports:
      - '${MINIO_PORT}:${MINIO_PORT}' # MinIO API
      - '9001:9001' # MinIO Console
      - '${LOGTO_PORT}:${LOGTO_PORT}' # Logto
      - '3002:3002' # Logto Admin
      - '${LOBE_PORT}:3210' # LobeChat
    command: tail -f /dev/null
    networks:
      - lobe-network

  postgresql:
    image: pgvector/pgvector:pg16
    container_name: lobe-postgres
    ports:
      - '5432:5432'
    volumes:
      - './data:/var/lib/postgresql/data'
    environment:
      - 'POSTGRES_DB=${LOBE_DB_NAME}'
      - 'POSTGRES_PASSWORD=${POSTGRES_PASSWORD}'
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5
    restart: always
    networks:
      - lobe-network

  minio:
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z
    container_name: lobe-minio
    network_mode: 'service:network-service'
    volumes:
      - './s3_data:/etc/minio/data'
    environment:
      - 'MINIO_ROOT_USER=${MINIO_ROOT_USER}'
      - 'MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}'
      - 'MINIO_API_CORS_ALLOW_ORIGIN=http://localhost:${LOBE_PORT}'
    restart: always
    command: >
      server /etc/minio/data --address ":${MINIO_PORT}" --console-address ":9001"


  logto:
    image: svhd/logto
    container_name: lobe-logto
    network_mode: 'service:network-service'
    depends_on:
      postgresql:
        condition: service_healthy
    environment:
      - 'TRUST_PROXY_HEADER=1'
      - 'PORT=${LOGTO_PORT}'
      - 'DB_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgresql:5432/logto'
      - 'ENDPOINT=http://localhost:${LOGTO_PORT}'
      - 'ADMIN_ENDPOINT=http://localhost:3002'
    entrypoint: ['sh', '-c', 'npm run cli db seed -- --swe && npm start']

  lobe:
    image: lobehub/lobe-chat-database
    container_name: lobe-chat
    network_mode: 'service:network-service'
    depends_on:
      postgresql:
        condition: service_healthy
      network-service:
        condition: service_started
      minio:
        condition: service_started
      logto:
        condition: service_started

    environment:
      - 'APP_URL=http://localhost:3210'
      - 'NEXT_AUTH_SSO_PROVIDERS=logto'
      - 'KEY_VAULTS_SECRET=Kix2wcUONd4CX51E/ZPAd36BqM4wzJgKjPtz2sGztqQ='
      - 'NEXT_AUTH_SECRET=NX2kaPE923dt6BL2U8e9oSre5RfoT7hg'
      - 'NEXTAUTH_URL=http://localhost:${LOBE_PORT}/api/auth'
      - 'AUTH_LOGTO_ISSUER=http://localhost:${LOGTO_PORT}/oidc'
      - 'DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgresql:5432/${LOBE_DB_NAME}'
      - 'S3_ENDPOINT=http://localhost:${MINIO_PORT}'
      - 'S3_BUCKET=${MINIO_LOBE_BUCKET}'
      - 'S3_PUBLIC_DOMAIN=http://localhost:${MINIO_PORT}'
      - 'S3_ENABLE_PATH_STYLE=1'
    env_file:
      - .env
    restart: always

volumes:
  data:
    driver: local
  s3_data:
    driver: local

networks:
  lobe-network:
    driver: bridge
