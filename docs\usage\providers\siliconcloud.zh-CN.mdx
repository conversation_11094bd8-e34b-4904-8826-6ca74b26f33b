---
title: 在 LobeChat 中使用 SiliconCloud
description: 学习如何在 LobeChat 中配置和使用 SiliconCloud 的API Key，以便开始对话和交互。
tags:
  - LobeChat
  - SiliconCloud
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 SiliconCloud

<Image cover src={'https://github.com/user-attachments/assets/9a78bbb9-7c96-4f32-9b66-e57f92660410'} />

[SiliconCloud](https://siliconflow.cn/) 是一个基于开源基础模型的人工智能服务平台，提供多种生成式 AI（GenAI）服务。

本文将指导你如何在 LobeChat 中使用 SiliconCloud。

<Steps>
  ### 步骤一：获得 SiliconCloud 的 API Key

  - 注册并登录 [SiliconCloud](https://cloud.siliconflow.cn/account/ak)
  - 点击左侧 `API 密钥` 菜单
  - 创建一个 API 密钥并复制

  <Image alt={'创建API密钥'} inStep src={'https://github.com/user-attachments/assets/872756dc-305e-4e63-9fb7-60550280fc12'} />

  ### 步骤二：在 LobeChat 中配置 SiliconCloud

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `SiliconFlow` 的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/4c792f62-5203-4f13-8f23-df228f70d67f'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 SiliconCloud 的模型即可开始对话

  <Image alt={'选择 SiliconCloud 模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/f4dbbadb-7461-4370-a836-09c487fdd206'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 SiliconCloud 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 SiliconCloud 提供的模型进行对话了。
