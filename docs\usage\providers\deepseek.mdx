---
title: Using DeepSeek API Key in LobeChat
description: >-
  Learn how to use DeepSeek-V2 in LobeChat, obtain API keys. Get started with DeepSeek integration now!

tags:
  - DeepSeek
  - LobeChat
  - DeepSeek-V2
  - API Key
  - Web UI
---

# Using DeepSeek in LobeChat

<Image alt={'Using DeepSeek in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/b4d12904-9d5d-46de-bd66-901eeb9c8e52'} />

[DeepSeek](https://www.deepseek.com/) represents a cutting-edge open-source large language model. The latest versions, DeepSeek-V3 and DeepSeek-R1, have undergone substantial improvements in both architecture and performance, particularly shining in their inference capabilities. By leveraging innovative training methodologies and reinforcement learning, the model has effectively boosted its inference prowess, now nearly matching the pinnacle performance of OpenAI.

This document will guide you on how to use DeepSeek in LobeChat:

<Steps>
  ### Step 1: Obtain DeepSeek API Key

  - First, you need to register and log in to the [DeepSeek](https://platform.deepseek.com/) open platform.

  <Callout type={'info'}>New users will receive a free quota of 500M Tokens</Callout>

  - Go to the `API keys` menu and click on `Create API Key`.

  <Image alt={'Create Deepseek API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/5707b392-1ee6-4db6-95cb-9d6c902747d2'} />

  - Enter the API key name in the pop-up dialog box.

  <Image alt={'Enter Deepseek API Name'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/c1d1d816-6339-41a6-9bc9-e2c3b2762291'} />

  - Copy the generated API key and save it securely.

  <Image alt={'Save Deepseek API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/198217a6-84fa-441c-bcbe-8cded1106d6c'} />

  <Callout type={'warning'}>
    Please store the key securely as it will only appear once. If you accidentally lose it, you will
    need to create a new key.
  </Callout>

  ### Step 2: Configure DeepSeek in LobeChat

  - Access the `App Settings` interface in LobeChat.
  - Find the setting for `DeepSeek` under `AI Service Provider`.

  <Image alt={'Enter Deepseek API Key'} inStep src={'https://github.com/user-attachments/assets/aaa3e2c5-7f16-4cfb-86b6-2814a1aafe3a'} />

  - Open DeepSeek and enter the obtained API key.
  - Choose a DeepSeek model for your assistant to start the conversation.

  <Image alt={'Select Deepseek Model'} inStep src={'https://github.com/user-attachments/assets/84a5c971-1262-4639-b79f-c8b138530803'} />

  <Callout type={'warning'}>
    You may need to pay the API service provider during usage, please refer to DeepSeek's relevant
    pricing policies.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by Deepseek in LobeChat.
