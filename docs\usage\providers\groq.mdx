---
title: Using Groq API Key in LobeChat
description: >-
  Learn how to obtain GroqCloud API keys and configure Groq in LobeChat for optimal performance.

tags:
  - LPU Inference Engine
  - GroqCloud
  - LLAMA3
  - Qwen2
  - API keys
  - Web UI
---

# Using Groq in LobeChat

<Image alt={'Using Groq in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/17870709/1d840e27-fa74-4e71-b777-330bf41d6dff'} />

Groq's [LPU Inference Engine](https://wow.groq.com/news_press/groq-lpu-inference-engine-leads-in-first-independent-llm-benchmark/) has excelled in the latest independent Large Language Model (LLM) benchmark, redefining the standard for AI solutions with its remarkable speed and efficiency. By integrating LobeChat with Groq Cloud, you can now easily leverage Groq's technology to accelerate the operation of large language models in LobeChat.

<Callout type={'info'}>
  Groq's LPU Inference Engine achieved a sustained speed of 300 tokens per second in internal
  benchmark tests, and according to benchmark tests by ArtificialAnalysis.ai, Groq outperformed
  other providers in terms of throughput (241 tokens per second) and total time to receive 100
  output tokens (0.8 seconds).
</Callout>

This document will guide you on how to use Groq in LobeChat:

<Steps>
  ### Obtaining GroqCloud API Keys

  First, you need to obtain an API Key from the [GroqCloud Console](https://console.groq.com/).

  <Image alt={'Get GroqCloud API Key'} height={274} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/6942287e-fbb1-4a10-a1ce-caaa6663da1e'} />

  Create an API Key in the `API Keys` menu of the console.

  <Image alt={'Save GroqCloud API Key'} height={274} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/eb57ca57-4f45-4409-91ce-9fa9c7c626d6'} />

  <Callout type={'warning'}>
    Safely store the key from the pop-up as it will only appear once. If you accidentally lose it, you
    will need to create a new key.
  </Callout>

  ### Configure Groq in LobeChat

  You can find the Groq configuration option in `Settings` -> `AI Service Provider`, where you can input the API Key you just obtained.

  <Image alt={'Groq service provider settings'} height={274} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/88948a3a-6681-4a8d-9734-a464e09e4957'} />
</Steps>

Next, select a Groq-supported model in the assistant's model options, and you can experience the powerful performance of Groq in LobeChat.

<Video alt={'Select and use Groq model'} src="https://github.com/lobehub/lobe-chat/assets/28616219/b6b8226b-183f-4249-8255-663a5e9f5af4" />
