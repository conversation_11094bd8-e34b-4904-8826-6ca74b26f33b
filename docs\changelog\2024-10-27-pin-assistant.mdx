---
title: LobeChat Introduces Persistent Assistant Sidebar Feature
description: >-
  LobeChat v1.26.0 launches the persistent assistant sidebar feature, supporting quick key switching for easy access to frequently used assistants, significantly enhancing efficiency.

tags:
  - Persistent Assistant
  - Sidebar Feature
  - User Experience
  - Workflow Optimization
---

# Persistent Assistant Sidebar: Creating a More Convenient Conversation Experience

In version v1.26.0, we are excited to introduce a long-awaited new feature — the persistent assistant sidebar. This feature aims to enhance user access to frequently used assistants, making your reliable helpers easily accessible.

## Feature Highlights

- **Quick Switching**: Supports quick switching between different assistants using keyboard shortcuts, making your workflow smoother.
- **Space Optimization**: Activating the sidebar automatically hides the conversation list, providing you with a larger conversation area.
- **Intelligent Display**: Automatically syncs pinned assistants to the sidebar, ensuring that important assistants are always within view.

![Sidebar Display Effect](https://github.com/user-attachments/assets/6935e155-4a1d-4ab7-a61a-2b813d65bb7b)

![Conversation Interface Effect](https://github.com/user-attachments/assets/c68e88e4-cf2e-4122-82bc-89ba193b1eb4)

## How to Use

Currently, this feature is in the experimental stage and is disabled by default. To experience it, you can enable it by adding the environment variable `FEATURE_FLAGS=+pin_list`.

We have already enabled this feature in the Cloud version, and we welcome all users to try it out and provide feedback. You can share your experiences in [GitHub Discussions](https://github.com/lobehub/lobe-chat/discussions/4515) to help us refine this feature further.

## Design Philosophy

The core goal of this update is to optimize work efficiency. By effectively utilizing the sidebar space, we make frequently used assistants easily accessible while hiding the conversation list to expand the conversation area, providing users with a more focused dialogue experience.

We hope this new feature will significantly enhance your user experience. Welcome to upgrade to version v1.26.0 and start experiencing it!
