---
title: Resolving `UNABLE_TO_VERIFY_LEAF_SIGNATURE` Error in LobeChat When Using Proxy
description: >-
  Learn how to bypass Node.js certificate validation to resolve certificate verification errors in LobeChat when using a proxy with self-signed or untrusted certificates.

tags:
  - Node.js
  - certificate validation
  - proxy server
  - security
  - Docker
  - certificate issue
---

# Encounter `UNABLE_TO_VERIFY_LEAF_SIGNATURE` Error When Using Proxy

## Problem Description

When deploying privately and using a proxy (e.g., Surge) for network requests, you may encounter certificate verification errors.

The error message may look like this:

```bash
[TypeError: fetch failed] {
  cause: [Error: unable to verify the first certificate] {
    code: 'UNABLE_TO_VERIFY_LEAF_SIGNATURE'
  }
}
```

Or:

```json
{
  "endpoint": "https://api.openai.com/v1",
  "error": {
    "cause": {
      "code": "UNABLE_TO_VERIFY_LEAF_SIGNATURE"
    }
  }
}
```

This problem typically occurs when using a proxy server with a self-signed certificate or a man-in-the-middle certificate that is not trusted by Node.js by default.

## Solution

To resolve this issue, you can bypass Node.js certificate validation by adding an environment variable when starting the application. Specifically, you can add `NODE_TLS_REJECT_UNAUTHORIZED=0` to the startup command. For example:

```bash
NODE_TLS_REJECT_UNAUTHORIZED=0 npm run start
```

Alternatively, when running in a Docker container, you can set the environment variable in the Dockerfile or docker-compose.yml:

```dockerfile
# In the Dockerfile
ENV NODE_TLS_REJECT_UNAUTHORIZED=0
```

```yaml
# In the docker-compose.yml
environment:
  - NODE_TLS_REJECT_UNAUTHORIZED=0
```

Example Docker run command:

```bash
docker run -e NODE_TLS_REJECT_UNAUTHORIZED=0 <other parameters> <image name>
```

Please note that this method reduces security as it allows Node.js to accept unverified certificates. Therefore, it is only recommended for use in privately deployed environments with complete trust and should be reverted to the default certificate validation settings after resolving the certificate issue.

## More Secure Alternatives

If possible, it is recommended to address the certificate issue using the following methods:

1. Ensure all man-in-the-middle certificates are correctly installed on the proxy server and the corresponding clients.
2. Replace self-signed or man-in-the-middle certificates with valid certificates issued by trusted certificate authorities.
3. Properly configure the certificate chain in the code to ensure Node.js can validate to the root certificate.

Implementing these methods can resolve certificate validation issues without compromising security.
