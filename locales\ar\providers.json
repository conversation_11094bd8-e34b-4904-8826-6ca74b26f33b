{"ai21": {"description": "تقوم AI21 Labs ببناء نماذج أساسية وأنظمة ذكاء اصطناعي للشركات، مما يسرع من تطبيق الذكاء الاصطناعي التوليدي في الإنتاج."}, "ai360": {"description": "AI 360 هي منصة نماذج وخدمات الذكاء الاصطناعي التي أطلقتها شركة 360، تقدم مجموعة متنوعة من نماذج معالجة اللغة الطبيعية المتقدمة، بما في ذلك 360GPT2 Pro و360GPT Pro و360GPT Turbo و360GPT Turbo Responsibility 8K. تجمع هذه النماذج بين المعلمات الكبيرة والقدرات متعددة الوسائط، وتستخدم على نطاق واسع في توليد النصوص، وفهم المعاني، وأنظمة الحوار، وتوليد الشيفرات. من خلال استراتيجيات تسعير مرنة، تلبي AI 360 احتياجات المستخدمين المتنوعة، وتدعم المطورين في التكامل، مما يعزز الابتكار والتطوير في التطبيقات الذكية."}, "anthropic": {"description": "Anthropic هي شركة تركز على أبحاث وتطوير الذكاء الاصطناعي، وتقدم مجموعة من نماذج اللغة المتقدمة، مثل Claude 3.5 Sonnet وClaude 3 Sonnet وClaude 3 Opus وClaude 3 Haiku. تحقق هذه النماذج توازنًا مثاليًا بين الذكاء والسرعة والتكلفة، وتناسب مجموعة متنوعة من سيناريوهات التطبيقات، من أحمال العمل على مستوى المؤسسات إلى الاستجابات السريعة. يعتبر Claude 3.5 Sonnet أحدث نماذجها، وقد أظهر أداءً ممتازًا في العديد من التقييمات مع الحفاظ على نسبة تكلفة فعالة."}, "azure": {"description": "توفر Azure مجموعة متنوعة من نماذج الذكاء الاصطناعي المتقدمة، بما في ذلك GPT-3.5 وأحدث سلسلة GPT-4، تدعم أنواع بيانات متعددة ومهام معقدة، وتلتزم بحلول ذكاء اصطناعي آمنة وموثوقة ومستدامة."}, "azureai": {"description": "توفر Azure مجموعة متنوعة من نماذج الذكاء الاصطناعي المتقدمة، بما في ذلك GPT-3.5 وأحدث سلسلة GPT-4، تدعم أنواع البيانات المتعددة والمهام المعقدة، وتهدف إلى تقديم حلول ذكاء اصطناعي آمنة وموثوقة ومستدامة."}, "baichuan": {"description": "Baichuan Intelligence هي شركة تركز على تطوير نماذج الذكاء الاصطناعي الكبيرة، حيث تظهر نماذجها أداءً ممتازًا في المهام الصينية مثل الموسوعات المعرفية ومعالجة النصوص الطويلة والإبداع. تتفوق على النماذج الرئيسية الأجنبية. كما تتمتع Baichuan Intelligence بقدرات متعددة الوسائط رائدة في الصناعة، وقد أظهرت أداءً ممتازًا في العديد من التقييمات الموثوقة. تشمل نماذجها Baichuan 4 وBaichuan 3 Turbo وBaichuan 3 Turbo 128k، وكل منها مُحسّن لمشاهد تطبيق مختلفة، مما يوفر حلولًا فعالة من حيث التكلفة."}, "bedrock": {"description": "Bedrock هي خدمة تقدمها أمازون AWS، تركز على توفير نماذج لغة ورؤية متقدمة للذكاء الاصطناعي للشركات. تشمل عائلة نماذجها سلسلة Claude من Anthropic وسلسلة Llama 3.1 من Meta، وتغطي مجموعة من الخيارات من النماذج الخفيفة إلى عالية الأداء، وتدعم مهام مثل توليد النصوص، والحوار، ومعالجة الصور، مما يجعلها مناسبة لتطبيقات الشركات بمختلف أحجامها واحتياجاتها."}, "cloudflare": {"description": "تشغيل نماذج التعلم الآلي المدفوعة بوحدات معالجة الرسوميات بدون خادم على شبكة Cloudflare العالمية."}, "cohere": {"description": "تقدم Cohere أحدث نماذج متعددة اللغات، وميزات بحث متقدمة، ومساحة عمل AI مصممة خصيصًا للشركات الحديثة - كل ذلك مدمج في منصة آمنة."}, "deepseek": {"description": "DeepSeek هي شركة تركز على أبحاث وتطبيقات تقنيات الذكاء الاصطناعي، حيث يجمع نموذجها الأحدث DeepSeek-V2.5 بين قدرات الحوار العامة ومعالجة الشيفرات، وقد حقق تحسينات ملحوظة في محاذاة تفضيلات البشر، ومهام الكتابة، واتباع التعليمات."}, "fal": {"description": "منصة وسائط توليدية موجهة للمطورين"}, "fireworksai": {"description": "Fireworks AI هي شركة رائدة في تقديم خدمات نماذج اللغة المتقدمة، تركز على استدعاء الوظائف والمعالجة متعددة الوسائط. نموذجها الأحدث Firefunction V2 مبني على Llama-3، مُحسّن لاستدعاء الوظائف، والحوار، واتباع التعليمات. يدعم نموذج اللغة البصرية FireLLaVA-13B إدخال الصور والنصوص المختلطة. تشمل النماذج البارزة الأخرى سلسلة Llama وسلسلة Mixtral، مما يوفر دعمًا فعالًا لاتباع التعليمات وتوليدها بلغات متعددة."}, "giteeai": {"description": "خادم واجهات برمجة التطبيقات gitee منظمة العفو الدولية يوفر نموذج كبير المنطق API خدمة منظمة العفو الدولية للمطورين ."}, "github": {"description": "مع نماذج GitH<PERSON>، يمكن للمطورين أن يصبحوا مهندسي ذكاء اصطناعي ويبنون باستخدام نماذج الذكاء الاصطناعي الرائدة في الصناعة."}, "google": {"description": "سلسلة Gemini من Google هي نماذج الذكاء الاصطناعي الأكثر تقدمًا وشمولية، تم تطويرها بواسطة Google DeepMind، مصممة خصيصًا لتكون متعددة الوسائط، تدعم الفهم والمعالجة السلسة للنصوص، والشيفرات، والصور، والصوت، والفيديو. تناسب مجموعة متنوعة من البيئات، من مراكز البيانات إلى الأجهزة المحمولة، مما يعزز بشكل كبير كفاءة نماذج الذكاء الاصطناعي وانتشار استخدامها."}, "groq": {"description": "يتميز محرك الاستدلال LPU من Groq بأداء ممتاز في أحدث اختبارات المعايير لنماذج اللغة الكبيرة المستقلة (LLM)، حيث أعاد تعريف معايير حلول الذكاء الاصطناعي بسرعته وكفاءته المذهلة. Groq يمثل سرعة استدلال فورية، ويظهر أداءً جيدًا في النشر القائم على السحابة."}, "higress": {"description": "Higress هو بوابة API سحابية الأصل، تم تطويرها داخل علي بابا لحل مشاكل إعادة تحميل Tengine التي تؤثر سلبًا على الأعمال ذات الاتصالات الطويلة، بالإضافة إلى نقص قدرات توازن الحمل لـ gRPC/Dubbo."}, "huggingface": {"description": "تقدم واجهة برمجة التطبيقات الخاصة بـ HuggingFace طريقة سريعة ومجانية لاستكشاف الآلاف من النماذج لمجموعة متنوعة من المهام. سواء كنت تقوم بتصميم نموذج أولي لتطبيق جديد أو تحاول استكشاف إمكانيات التعلم الآلي، فإن هذه الواجهة تتيح لك الوصول الفوري إلى نماذج عالية الأداء في مجالات متعددة."}, "hunyuan": {"description": "نموذج لغة متقدم تم تطويره بواسطة Tencent، يتمتع بقدرة قوية على الإبداع باللغة الصينية، وقدرة على الاستدلال المنطقي في سياقات معقدة، بالإضافة إلى قدرة موثوقة على تنفيذ المهام."}, "infiniai": {"description": "يقدم خدمات نماذج كبيرة ذات أداء عالٍ وسهولة الاستخدام وأمان موثوق به للمطورين، تغطي كامل العملية من تطوير النماذج الكبيرة إلى نشرها كخدمات."}, "internlm": {"description": "منظمة مفتوحة المصدر مكرسة لأبحاث وتطوير أدوات النماذج الكبيرة. توفر منصة مفتوحة المصدر فعالة وسهلة الاستخدام لجميع مطوري الذكاء الاصطناعي، مما يجعل أحدث تقنيات النماذج الكبيرة والخوارزميات في متناول اليد."}, "jina": {"description": "تأسست Jina AI في عام 2020، وهي شركة رائدة في مجال الذكاء الاصطناعي للبحث. تحتوي منصتنا الأساسية للبحث على نماذج متجهة، ومعيدي ترتيب، ونماذج لغوية صغيرة، لمساعدة الشركات في بناء تطبيقات بحث موثوقة وعالية الجودة تعتمد على الذكاء الاصطناعي التوليدي ومتعددة الوسائط."}, "lmstudio": {"description": "LM Studio هو تطبيق سطح مكتب لتطوير وتجربة نماذج اللغة الكبيرة (LLMs) على جهاز الكمبيوتر الخاص بك."}, "minimax": {"description": "MiniMax هي شركة تكنولوجيا الذكاء الاصطناعي العامة التي تأسست في عام 2021، تكرس جهودها للتعاون مع المستخدمين في إنشاء الذكاء. طورت MiniMax نماذج كبيرة عامة من أوضاع مختلفة، بما في ذلك نموذج نصي MoE الذي يحتوي على تريليونات من المعلمات، ونموذج صوتي، ونموذج صور. وقد أطلقت تطبيقات مثل Conch AI."}, "mistral": {"description": "تقدم Mistral نماذج متقدمة عامة ومتخصصة وبحثية، تستخدم على نطاق واسع في الاستدلال المعقد، والمهام متعددة اللغات، وتوليد الشيفرات، من خلال واجهة استدعاء الوظائف، يمكن للمستخدمين دمج وظائف مخصصة لتحقيق تطبيقات محددة."}, "modelscope": {"description": "ModelScope هو منصة نموذج كخدمة أطلقتها علي بابا كلاود، تقدم مجموعة واسعة من نماذج الذكاء الاصطناعي وخدمات الاستدلال."}, "moonshot": {"description": "Moonshot هي منصة مفتوحة أطلقتها شركة Beijing Dark Side Technology Co.، Ltd، تقدم مجموعة متنوعة من نماذج معالجة اللغة الطبيعية، وتغطي مجالات واسعة، بما في ذلك ولكن لا تقتصر على إنشاء المحتوى، والبحث الأكاديمي، والتوصيات الذكية، والتشخيص الطبي، وتدعم معالجة النصوص الطويلة والمهام المعقدة."}, "novita": {"description": "Novita AI هي منصة تقدم خدمات API لمجموعة متنوعة من نماذج اللغة الكبيرة وتوليد الصور بالذكاء الاصطناعي، مرنة وموثوقة وفعالة من حيث التكلفة. تدعم أحدث النماذج مفتوحة المصدر مثل Llama3 وMistral، وتوفر حلول API شاملة وسهلة الاستخدام وقابلة للتوسع تلقائيًا لتطوير تطبيقات الذكاء الاصطناعي، مما يجعلها مناسبة لنمو الشركات الناشئة في مجال الذكاء الاصطناعي."}, "nvidia": {"description": "تقدم NVIDIA NIM™ حاويات يمكن استخدامها لاستضافة خدمات استدلال معززة بواسطة GPU، تدعم نشر نماذج الذكاء الاصطناعي المدربة مسبقًا والمخصصة على السحابة ومراكز البيانات وأجهزة الكمبيوتر الشخصية RTX™ ومحطات العمل."}, "ollama": {"description": "تغطي نماذج Ollama مجموعة واسعة من مجالات توليد الشيفرة، والعمليات الرياضية، ومعالجة اللغات المتعددة، والتفاعل الحواري، وتدعم احتياجات النشر على مستوى المؤسسات والتخصيص المحلي."}, "openai": {"description": "OpenAI هي مؤسسة رائدة عالميًا في أبحاث الذكاء الاصطناعي، حيث دفعت النماذج التي طورتها مثل سلسلة GPT حدود معالجة اللغة الطبيعية. تلتزم OpenAI بتغيير العديد من الصناعات من خلال حلول الذكاء الاصطناعي المبتكرة والفعالة. تتمتع منتجاتهم بأداء ملحوظ وفعالية من حيث التكلفة، وتستخدم على نطاق واسع في البحث والتجارة والتطبيقات الابتكارية."}, "openrouter": {"description": "OpenRouter هي منصة خدمة تقدم واجهات لمجموعة متنوعة من النماذج الكبيرة المتقدمة، تدعم OpenAI وAnthropic وLLaMA وغيرها، مما يجعلها مناسبة لاحتياجات التطوير والتطبيق المتنوعة. يمكن للمستخدمين اختيار النموذج والسعر الأمثل وفقًا لاحتياجاتهم، مما يعزز تجربة الذكاء الاصطناعي."}, "perplexity": {"description": "Perplexity هي شركة رائدة في تقديم نماذج توليد الحوار، تقدم مجموعة من نماذج Llama 3.1 المتقدمة، تدعم التطبيقات عبر الإنترنت وغير المتصلة، وتناسب بشكل خاص مهام معالجة اللغة الطبيعية المعقدة."}, "ppio": {"description": "تقدم PPIO بايو السحابية خدمات واجهة برمجة التطبيقات لنماذج مفتوحة المصدر مستقرة وذات تكلفة فعالة، تدعم جميع سلسلة DeepSeek، وLlama، وQwen، وغيرها من النماذج الكبيرة الرائدة في الصناعة."}, "qiniu": {"description": "كشركة رائدة في خدمات السحابة، تقدم <PERSON><PERSON> خدمات استدلال ذكاء اصطناعي في الوقت الفعلي ومجموعة كبيرة بتكلفة فعالة وموثوقة، سهلة الاستخدام."}, "qwen": {"description": "Qwen هو نموذج لغة ضخم تم تطويره ذاتيًا بواسطة Alibaba Cloud، يتمتع بقدرات قوية في فهم وتوليد اللغة الطبيعية. يمكنه الإجابة على مجموعة متنوعة من الأسئلة، وكتابة المحتوى، والتعبير عن الآراء، وكتابة الشيفرات، ويؤدي دورًا في مجالات متعددة."}, "sambanova": {"description": "تتيح لك سحابة SambaNova استخدام أفضل النماذج مفتوحة المصدر بسهولة، والاستمتاع بأسرع سرعة استدلال."}, "search1api": {"description": "يوفر Search1API الوصول إلى سلسلة نماذج DeepSeek التي يمكن الاتصال بها حسب الحاجة، بما في ذلك النسخة القياسية والنسخة السريعة، مع دعم لاختيار نماذج بمقاييس معلمات متعددة."}, "sensenova": {"description": "تقدم شركة SenseTime خدمات نماذج كبيرة شاملة وسهلة الاستخدام، مدعومة بقوة من البنية التحتية الكبيرة لشركة SenseTime."}, "siliconcloud": {"description": "تسعى SiliconFlow إلى تسريع الذكاء الاصطناعي العام (AGI) لفائدة البشرية، من خلال تحسين كفاءة الذكاء الاصطناعي على نطاق واسع باستخدام حزمة GenAI سهلة الاستخدام وذات التكلفة المنخفضة."}, "spark": {"description": "تقدم شركة iFlytek نموذج Spark الكبير، الذي يوفر قدرات ذكاء اصطناعي قوية عبر مجالات متعددة ولغات متعددة، باستخدام تقنيات معالجة اللغة الطبيعية المتقدمة، لبناء تطبيقات مبتكرة مناسبة للأجهزة الذكية، والرعاية الصحية الذكية، والتمويل الذكي، وغيرها من السيناريوهات الرأسية."}, "stepfun": {"description": "نموذج StepFun الكبير يتمتع بقدرات متعددة الوسائط واستدلال معقد رائدة في الصناعة، ويدعم فهم النصوص الطويلة جدًا وميزات قوية لمحرك البحث الذاتي."}, "taichu": {"description": "أطلقت الأكاديمية الصينية للعلوم ومعهد ووهان للذكاء الاصطناعي نموذجًا جديدًا متعدد الوسائط، يدعم أسئلة وأجوبة متعددة الجولات، وإنشاء النصوص، وتوليد الصور، وفهم 3D، وتحليل الإشارات، ويغطي مجموعة شاملة من مهام الأسئلة والأجوبة، مع قدرات أقوى في الإدراك والفهم والإبداع، مما يوفر تجربة تفاعلية جديدة."}, "tencentcloud": {"description": "قدرة المحرك المعرفي الذري (LLM Knowledge Engine Atomic Power) هي قدرة كاملة للإجابة على الأسئلة مبنية على تطوير المحرك المعرفي، موجهة نحو الشركات والمطورين، وتوفر القدرة على تجميع وتطوير تطبيقات النماذج بشكل مرن. يمكنك من خلال مجموعة من القدرات الذرية تجميع خدمة النموذج الخاصة بك، واستدعاء خدمات تحليل الوثائق، والتقسيم، والتضمين، وإعادة الكتابة متعددة الجولات، لتخصيص أعمال الذكاء الاصطناعي الخاصة بالشركة."}, "togetherai": {"description": "تسعى Together AI لتحقيق أداء رائد من خلال نماذج الذكاء الاصطناعي المبتكرة، وتقدم مجموعة واسعة من القدرات المخصصة، بما في ذلك دعم التوسع السريع وعمليات النشر البديهية، لتلبية احتياجات الشركات المتنوعة."}, "upstage": {"description": "تتخصص Upstage في تطوير نماذج الذكاء الاصطناعي لتلبية احتياجات الأعمال المتنوعة، بما في ذلك Solar LLM وDocument AI، بهدف تحقيق الذكاء الاصطناعي العام (AGI) القائم على العمل. من خلال واجهة Chat API، يمكن إنشاء وكلاء حوار بسيطين، وتدعم استدعاء الوظائف، والترجمة، والتضمين، وتطبيقات المجالات المحددة."}, "v0": {"description": "v0 هو مساعد برمجة تعاوني، كل ما عليك هو وصف أفكارك بلغة طبيعية، وسيقوم بإنشاء الشيفرة وواجهة المستخدم (UI) لمشروعك."}, "vertexai": {"description": "سلسلة جيميني من جوجل هي نماذج الذكاء الاصطناعي الأكثر تقدمًا وعمومية، تم تطويرها بواسطة جوجل ديب مايند، مصممة خصيصًا لتكون متعددة الوسائط، تدعم الفهم والمعالجة السلسة للنصوص، الأكواد، الصور، الصوتيات، والفيديو. تناسب مجموعة متنوعة من البيئات، من مراكز البيانات إلى الأجهزة المحمولة، مما يعزز بشكل كبير كفاءة نماذج الذكاء الاصطناعي وتطبيقاتها الواسعة."}, "vllm": {"description": "vLLM هو مكتبة سريعة وسهلة الاستخدام لاستدلال LLM والخدمات."}, "volcengine": {"description": "منصة تطوير خدمات النماذج الكبيرة التي أطلقتها بايت دانس، تقدم خدمات استدعاء نماذج غنية بالوظائف وآمنة وتنافسية من حيث الأسعار، كما توفر بيانات النماذج، والتعديل الدقيق، والاستدلال، والتقييم، وغيرها من الوظائف الشاملة، لضمان تطوير تطبيقات الذكاء الاصطناعي الخاصة بك بشكل كامل."}, "wenxin": {"description": "منصة تطوير وخدمات النماذج الكبيرة والتطبيقات الأصلية للذكاء الاصطناعي على مستوى المؤسسات، تقدم مجموعة شاملة وسهلة الاستخدام من أدوات تطوير النماذج الذكية التوليدية وأدوات تطوير التطبيقات على مدار العملية بأكملها."}, "xai": {"description": "xAI هي شركة تكرّس جهودها لبناء الذكاء الاصطناعي لتسريع الاكتشافات العلمية البشرية. مهمتنا هي تعزيز فهمنا المشترك للكون."}, "xinference": {"description": "Xorbits Inference (Xinference) هو منصة مفتوحة المصدر مصممة لتبسيط تشغيل ودمج نماذج الذكاء الاصطناعي المتنوعة. باستخدام Xinference، يمكنك تشغيل الاستدلال على نماذج LLM مفتوحة المصدر، ونماذج التضمين، والنماذج متعددة الوسائط سواء في السحابة أو في البيئات المحلية، وإنشاء تطبيقات ذكاء اصطناعي قوية."}, "zeroone": {"description": "01.AI تركز على تقنيات الذكاء الاصطناعي في عصر الذكاء الاصطناعي 2.0، وتعزز الابتكار والتطبيقات \"الإنسان + الذكاء الاصطناعي\"، باستخدام نماذج قوية وتقنيات ذكاء اصطناعي متقدمة لتعزيز إنتاجية البشر وتحقيق تمكين التكنولوجيا."}, "zhipu": {"description": "تقدم Zhipu AI منصة مفتوحة للنماذج متعددة الوسائط ونماذج اللغة، تدعم مجموعة واسعة من سيناريوهات تطبيقات الذكاء الاصطناعي، بما في ذلك معالجة النصوص، وفهم الصور، والمساعدة في البرمجة."}}