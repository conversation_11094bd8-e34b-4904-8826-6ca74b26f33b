---
title: Using Minimax API Key in LobeChat
description: >-
  Learn how to use MiniMax in LobeChat to enhance AI conversations. Obtain MiniMax API key, configure MiniMax in LobeChat settings, and select a model for your AI assistant.

tags:
  - MiniMax
  - Web UI
  - API Key
  - MiniMax Models
---

# Using Minimax in LobeChat

<Image alt={'Using Minimax in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/********/703f170b-c03b-4c71-b57d-c2357596bdfb'} />

[MiniMax](https://www.minimaxi.com/) is a general artificial intelligence technology company founded in 2021, dedicated to co-creating intelligence with users. MiniMax has independently developed universal large models of different modalities, including trillion-parameter MoE text large models, speech large models, and image large models. They have also launched applications like Hai Luo AI.

This document will guide you on how to use Minimax in LobeChat:

<Steps>
  ### Step 1: Obtain MiniMax API Key

  - Register and log in to the [MiniMax Open Platform](https://www.minimaxi.com/platform)
  - In `Account Management`, locate the `API Key` menu and create a new key

  <Image alt={'Create MiniMax API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/f6e46f1c-0ac9-42ae-8e83-ddb0cc6c5bf8'} />

  - Enter a name for the API key and create it

  <Image alt={'Enter API Key Name'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/cbc23ca9-1188-4b85-8ef0-e75ac7d74b92'} />

  - Copy the API key from the pop-up dialog box and save it securely

  <Image alt={'Save API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/fb0f7574-c2f5-40d6-8613-3749e85ce881'} />

  <Callout type={'warning'}>
    Please store the key securely as it will only appear once. If you accidentally lose it, you will
    need to create a new key.
  </Callout>

  ### Step 2: Configure MiniMax in LobeChat

  - Go to the `Settings` interface of LobeChat
  - Find the setting for `MiniMax` under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/b839e04e-0cef-46a3-bb84-0484a3f51c69'} />

  - Open Minimax and enter the obtained API key
  - Choose a MiniMax model for your AI assistant to start the conversation

  <Image alt={'Select MiniMax Model and Start Conversation'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/f7d59c7a-abd0-4ebd-8c72-ca10c47a0f1a'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to MiniMax's relevant
    pricing policies.
  </Callout>
</Steps>

You can now use the models provided by MiniMax to have conversations in LobeChat.
