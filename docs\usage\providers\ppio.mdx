---
title: Using PPIO API Key in LobeChat
description: >-
  Learn how to integrate PPIO's language model APIs into LobeChat. Follow the steps to register, create an PPIO API key, configure settings, and chat with our various AI models.

tags:
  - PPIO
  - DeepSeek
  - Llama
  - Qwen
  - uncensored
  - API key
  - Web UI
---

# Using PPIO in LobeChat

<Image alt={'Using PPIO in LobeChat'} cover src={'https://github.com/user-attachments/assets/d0a5e152-160a-4862-8393-546f4e2e5387'} />

[PPIO](https://ppinfra.com/user/register?invited_by=RQIMOC\&utm_source=github_lobechat) supports stable and cost-efficient open-source LLM APIs, such as DeepSeek, Llama, Qwen etc.

This document will guide you on how to integrate PPIO in LobeChat:

<Steps>
  ### Step 1: Register and Log in to PPIO

  - Visit [PPIO](https://ppinfra.com/user/register?invited_by=RQIMOC\&utm_source=github_lobechat) and create an account
  - Upon registration, PPIO will provide a ￥5 credit (about 5M tokens).

  <Image alt={'Register PPIO'} height={457} inStep src={'https://github.com/user-attachments/assets/7cb3019b-78c1-48e0-a64c-a6a4836affd9'} />

  ### Step 2: Obtain the API Key

  - Visit PPIO's [key management page](https://ppinfra.com/settings/key-management), create and copy an API Key.

  <Image alt={'Obtain PPIO API key'} inStep src={'https://github.com/user-attachments/assets/5abcf21d-5a6c-4fc8-8de6-bc47d4d2fa98'} />

  ### Step 3: Configure PPIO in LobeChat

  - Visit the `Settings` interface in LobeChat
  - Find the setting for `PPIO` under `AI Service Provider`

  <Image alt={'Enter PPIO API key in LobeChat'} inStep src={'https://github.com/user-attachments/assets/9b70b292-6c52-4715-b844-ff5df78d16b9'} />

  - Open PPIO and enter the obtained API key
  - Choose a PPIO model for your assistant to start the conversation

  <Image alt={'Select and use PPIO model'} inStep src={'https://github.com/user-attachments/assets/b824b741-f2d8-42c8-8cb9-1266862affa7'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to PPIO's [pricing
    policy](https://ppinfra.com/llm-api?utm_source=github_lobe-chat\&utm_medium=github_readme\&utm_campaign=link).
  </Callout>
</Steps>

You can now engage in conversations using the models provided by PPIO in LobeChat.
