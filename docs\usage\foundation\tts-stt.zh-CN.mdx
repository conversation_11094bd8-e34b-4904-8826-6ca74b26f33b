---
title: LobeChat 文字语音转换功能指南
description: 了解如何在 LobeChat 中使用文字语音转换功能，包括文字转语音（TTS）和语音转文字（STT），以及设置您喜欢的语音模型。
tags:
  - LobeChat
  - 文字语音转换
  - TTS
  - STT
  - 语音模型
---

# 文字语音转换使用指南

LobeChat 支持文字语音转换功能，允许用户通过语音输入内容，以及将 AI 输出的内容通过语音播报。

## 文字转语音（TTS）

在对话窗口中选中任意内容，选择`文字转语音`，AI 将通过 TTS 模型对文本内容进行语音播报。

<Image alt={'TTS'} src={'https://github.com/user-attachments/assets/d2714769-15f8-4d70-9128-607134163c52'} />

## 语音转文字（STT）

在输入窗口中选择语音输入功能，LobeChat 将您的语音转换为文字并输入到文本框中，完成输入后可以直接发送给 AI。

<Image alt={'STT'} src={'https://github.com/user-attachments/assets/d643af6d-ca0f-4abd-9dd2-977dacecb25d'} />

## 文字语音转换设置

你可以在设置中为文字语音转换功能指定您希望使用的模型。

<Image alt={'TTS 设置'} src={'https://github.com/user-attachments/assets/2f7c5c45-ec6a-4393-8fa9-19a4c5f52f7a'} />

- 打开`设置`面板
- 找到`文字转语音`设置
- 选择您所需的语音服务和 AI 模型
