---
title: LobeChat 知识库 / 文件上传
description: 了解 LobeChat 的文件上传和知识库管理核心组件，助力成功部署。
tags:
  - LobeChat
  - 文件上传
  - 知识库管理
  - PostgreSQL
  - OpenAI
---

# 知识库 / 文件上传

LobeChat 支持文件上传 / 知识库管理。该功能依赖于以下核心技术组件，了解这些组件将有助于你成功部署和维护知识库系统。

## 核心组件

### 1. PostgreSQL 与 PGVector

PostgreSQL 是一个强大的开源关系型数据库系统，而 PGVector 是其扩展，为向量操作提供支持。

- **用途**：存储结构化数据和向量索引
- **部署建议**：使用官方 Docker 镜像可以快速部署 PostgreSQL 和 PGVector

示例部署脚本：

```
docker run -p 5432:5432 -d --name pg -e POSTGRES_PASSWORD=mysecretpassword pgvector/pgvector:pg16
```

- **注意事项**：确保分配足够的资源以处理向量操作

### 2. S3 兼容的对象存储

S3（或兼容 S3 协议的存储服务）用于存储上传的文件。

- **用途**：存储原始文件
- **选项**：可以使用 AWS S3、MinIO 或其他兼容 S3 协议的存储服务
- **注意事项**：配置适当的访问权限和安全策略

### 3. OpenAI Embedding

OpenAI 的嵌入（Embedding）服务用于将文本转化为向量表示。

<Callout type={'info'}>
  LobeChat 当前默认使用 OpenAI `text-embedding-3-small` 模型，请确保你的 API Key 可以访问该模型。
</Callout>

- **用途**：生成文本的向量表示，用于语义搜索
- **注意事项**：
  - 需要有效的 OpenAI API 密钥
  - 实施适当的 API 调用限制和错误处理机制

### 4. Unstructured.io（可选）

Unstructured.io 是一个强大的文档处理工具。

- **用途**：处理复杂的文档格式，提取结构化信息
- **应用场景**：处理 PDF、Word 等非纯文本格式的文档
- **注意事项**：评估处理需求，根据文档复杂度决定是否部署

通过正确配置和集成这些核心组件，您可以为 LobeChat 构建一个强大、高效的知识库系统。每个组件都在整体架构中扮演着关键角色，共同支持高级的文档管理和智能检索功能。

### 5. 自定义 Embedding（可选）

- **用途**: 使用不同的嵌入模型（Embedding）生成文本的向量表示，用于语义搜索
- **选项**: 支持的模型提供商:zhipu/github/openai/bedrock/ollama
- **部署建议**: 使用环境变量配置默认嵌入模型

```
environment: DEFAULT_FILES_CONFIG=embedding_model=openai/embedding-text-3-small
```
