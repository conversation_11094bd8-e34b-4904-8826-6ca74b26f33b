---
title: Configuration of Microsoft Entra ID Authentication Service for LobeChat
description: >-
  Learn how to configure Microsoft Entra ID Authentication Service for LobeChat, create applications, add users, and set up environment variables for seamless integration.

tags:
  - Microsoft Entra ID
  - Authentication Service
  - Azure Portal
  - SSO
  - Environment Variables
  - LobeChat
---

# Configuration of Microsoft Entra ID Authentication Service

<Steps>
  ### Create a Microsoft Entra ID Application

  In your [Microsoft Azure Portal][microsoft-azure-portal], go to Microsoft Entra ID -> App registrations -> New registration to create a new application.

  Fill in the desired application name to be displayed to organizational users, choose the account types you wish to support, and if only internal users are supported, select `Accounts in this organizational directory only (Default Directory only - Single tenant)`.

  In the `Redirect URI (optional)` section, for the application type, select `Web`, and in the Callback URL, enter:

  ```bash
  https://your-domain/api/auth/callback/microsoft-entra-id
  ```

  <Callout type={'info'}>
    - You can fill in or modify the Redirect URIs after registering, but make sure the URL you enter
      matches the deployed URL. - Please replace "your-domain" with your own domain.
  </Callout>

  <Image alt="App Register" inStep src="https://github.com/lobehub/lobe-chat/assets/********/4f9d83bd-b3fc-4abc-bcf4-ccbad65c219d" />

  Click on "Register".

  After successfully creating the application, click on the corresponding application to enter the application details page, and switch to the "Overview" tab to view the corresponding configuration information.

  <Image alt="App Overview" inStep src="https://github.com/lobehub/lobe-chat/assets/********/48a0b702-05bd-4ce4-a007-a8ad00a36e5a" />

  Go to "Certificates & secrets", select the "Client secrets" tab, click on "New client secret", fill in the description, select the expiration time, and click on "Add" to create a new client secret.

  <Image alt="Create App Client Secret" inStep src="https://github.com/lobehub/lobe-chat/assets/********/c9d66fa0-158c-4bd3-a1fa-969e638259d2" />

  <Callout type={'important'}>
    Please make sure to save your client secret as this is your only chance to view it.
  </Callout>

  ### Add Users

  Go back to the "Microsoft Entra ID" interface, enter "Users", click on "New user", fill in the user information, and click on "Create" to create a user for using LobeChat.

  ### Configure Environment Variables

  When deploying LobeChat, you need to configure the following environment variables:

  | Environment Variable                | Type     | Description                                                                                                                                                                                                                |
  | ----------------------------------- | -------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
  | `NEXT_AUTH_SECRET`                  | Required | Key used to encrypt Auth.js session tokens. You can generate the key using the following command: `openssl rand -base64 32`                                                                                                |
  | `NEXT_AUTH_SSO_PROVIDERS`           | Required | Select the single sign-on provider for LoboChat. Use `microsoft-entra-id` for Microsoft Entra ID.                                                                                                                          |
  | `AUTH_MICROSOFT_ENTRA_ID_ID`        | Required | Client ID of the Microsoft Entra ID application.                                                                                                                                                                           |
  | `AUTH_MICROSOFT_ENTRA_ID_SECRET`    | Required | Client Secret of the Microsoft Entra ID application.                                                                                                                                                                       |
  | `AUTH_MICROSOFT_ENTRA_ID_TENANT_ID` | Required | Tenant ID of the Microsoft Entra ID application.                                                                                                                                                                           |
  | `NEXTAUTH_URL`                      | Required | This URL is used to specify the callback address for Auth.js when performing OAuth authentication. It is only necessary to set it when the default generated redirect address is incorrect. `https://example.com/api/auth` |

  <Callout type={'tip'}>
    You can refer to [📘 environment
    variables](/docs/self-hosting/environment-variable#microsoft-entra-id) for details on related
    variables.
  </Callout>
</Steps>

<Callout>
  After successful deployment, users will be able to authenticate and use LobeChat using the users
  configured in Microsoft Entra ID.
</Callout>

## Advanced Configuration

Please explore further in the [Microsoft Entra ID Learning Center][microsoft-learn-entra].

## Related Resources

- [Quickstart: Register an app][microsoft-entra-register-app]

[microsoft-azure-portal]: https://portal.azure.com/
[microsoft-entra-register-app]: https://learn.microsoft.com/en-us/entra/identity-platform/quickstart-register-app
[microsoft-learn-entra]: https://learn.microsoft.com/en-us/entra/identity/
