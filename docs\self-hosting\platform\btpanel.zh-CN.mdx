---
title: 通过 宝塔面板Docker应用商店 部署 LobeChat
description: >-
  学习如何使用 宝塔面板Docker应用 部署 LobeChat 服务，包括安装 Docker 容器环境和使用指令一键启动服务。详细说明如何配置环境变量和使用代理地址。

tags:
  - Docker
  - LobeChat
  - 部署指引
---

# 宝塔面板部署

## 前提

- 仅适用于宝塔面板 9.2.0 及以上版本
- 安装宝塔面板，前往[宝塔面板](https://www.bt.cn/new/download.html)官网，选择正式版的脚本下载安装

## 部署

1. 登录宝塔面板，在左侧菜单栏中点击 `Docker` ![Docker](https://github.com/user-attachments/assets/15d92756-92f0-45da-8f95-bfe725d13003)

2. 首次会提示安装`Docker`和`Docker Compose`服务，点击立即安装，若已安装请忽略。 ![安装环境](https://github.com/user-attachments/assets/6e383b75-09e3-42d1-8a6c-5fb7cf558f00)

3. 安装完成后在`Docker-应用商店-AI/大模型`中找到 `LobeChat`，点击`安装` ![安装](https://github.com/user-attachments/assets/db59a5e7-32ed-49d7-a791-8f8ee6618c01)

4. 设置域名等基本信息，点击`确定` ![设置](https://github.com/user-attachments/assets/37251adf-949b-4aec-bc49-bf4647e119da)

- 名称：应用名称，默认`lobechat_随机字符`
- 版本选择：默认`latest`
- 域名：如您需要通过域名访问，请在此处填写您的域名
- 允许外部访问：如您需通过`IP+Port`直接访问，请勾选，如您已经设置了域名，请不要勾选此处
- 端口：默认`3210`，可自行修改
- 访问密码：默认随机生成
- OpenAI API 密钥：请输入您的 Open API 密钥
- OpenAI 代理 URL：默认为官方地址
- OpenAI 模型列表：输入使用的模型
- CPU 核心数限制：0 为不限制，根据实际需要设置
- 内存限制：0 为不限制，根据实际需要设置

5. 提交后面板会自动进行应用初始化，大概需要`1-3`分钟，初始化完成后即可访问。

<Callout type="warning">
  ⚠️ 请不要在面板的反向代理设置中开启任何形式的缓存，以免影响服务的正常运行。详情请见
  [https://github.com/lobehub/lobe-chat/discussions/5986](https://github.com/lobehub/lobe-chat/discussions/5986)
</Callout>

## 访问 LobeChat

- 如果您填写域名，请在浏览器输入您的域名访问，如`http://demo.lobechat`，即可访问 `LobeChat` 页面。
- 请在浏览器地址栏中输入域名访问 `http://<宝塔面板IP>:3210`，即可访问 `LobeChat` 页面。 ![LobeChat](https://github.com/user-attachments/assets/808f8849-5738-4a60-8ccf-01e300b0dc88)
