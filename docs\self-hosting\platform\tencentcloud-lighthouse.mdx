---
title: Deploy LobeChat on TencentCloud Lighthouse
description: >-
  Learn how to deploy the LobeChat application on TencentCloud Lighthouse, including preparing the large model API Key, clicking the deploy button, and other operations.

tags:
  - TencentCloud Lighthouse
  - TencentCloud
  - LobeChat
  - API Key
---

# Deploy LobeChat with TencentCloud Lighthouse

If you want to deploy LobeChat on TencentCloud Lighthouse, you can follow the steps below:

## Tencent Cloud Deployment Process

<Steps>
  ### Prepare your API Key

  Go to [OpenAI API Key](https://platform.openai.com/account/api-keys) to get your OpenAI API Key.

  ### One-click to deploy

  [![][deploy-button-image]][deploy-link]

  ### Once deployed, you can start using it
</Steps>

[deploy-button-image]: https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/d65fb782-4fb0-4348-ad85-f2943d6bee8f.svg
[deploy-link]: https://buy.tencentcloud.com/lighthouse?blueprintType=APP_OS&blueprintOfficialId=lhbp-6u0ti132&regionId=9&zone=ap-singapore-3&bundleId=bundle_starter_nmc_lin_med2_01&loginSet=AUTO&rule=true&from=lobechat
