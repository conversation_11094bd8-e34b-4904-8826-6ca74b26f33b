---
title: >-
  Guide to Using Prompts in LobeChat - How to Write Effective Instructions for Generative AI

description: >-
  Learn the basic concepts of prompts and how to write well-structured and effective instructions for generative AI. Improve the quality and effectiveness of prompts to guide AI models accurately.

tags:
  - Generative AI
  - Prompts
  - Writing Instructions
  - Structured Prompts
  - Improving AI Output
---

# Guide to Using Prompts

## Basic Concepts of Prompts

Generative AI is very useful, but it requires human guidance. In most cases, generative AI can be as capable as a new intern at a company, but it needs clear instructions to perform well. The ability to guide generative AI correctly is a very powerful skill. You can guide generative AI by sending a prompt, which is usually a text instruction. A prompt is the input provided to the assistant, and it will affect the output. A good prompt should be structured, clear, concise, and directive.

## How to Write a Well-Structured Prompt

<Callout type={'important'}>
  A structured prompt refers to the construction of the prompt having a clear logic and structure.
  For example, if you want the model to generate an article, your prompt may need to include the
  article's topic, outline, and style.
</Callout>

Let's look at a basic discussion prompt example:

> *"What are the most urgent environmental issues facing our planet, and what actions can individuals take to help address these issues?"*

We can convert it into a simple prompt for the assistant to answer the following questions: placed at the front.

```prompt
Answer the following questions:
What are the most urgent environmental issues facing our planet, and what actions can individuals take to help address these issues?
```

Since the results generated by this prompt are not consistent, some are only one or two sentences. A typical discussion response should have multiple paragraphs, so these results are not ideal. A good prompt should provide **specific formatting and content instructions**. You need to eliminate ambiguity in the language to improve consistency and quality. Here is a better prompt.

```prompt
Write a highly detailed paper, including an introduction, body, and conclusion, to answer the following questions:
What are the most urgent environmental issues facing our planet,
and what actions can individuals take to help address these issues?
```

The second prompt generates longer output and better structure. The use of the term "paper" in the prompt is intentional, as the assistant can understand the definition of a paper, making it more likely to generate coherent, structured responses.

## How to Improve Quality and Effectiveness

<Callout type={'tip'}>
  There are several ways to improve the quality and effectiveness of prompts:

  - **Be Clear About Your Needs:** The model's output will strive to meet your needs, so if your needs are not clear, the output may not meet expectations.
  - **Use Correct Grammar and Spelling:** The model will try to mimic your language style, so if your language style is problematic, the output may also be problematic.
  - **Provide Sufficient Contextual Information:** The model will generate output based on the contextual information you provide, so if the information is insufficient, it may not produce the desired results.
</Callout>

After formulating effective prompts for discussing issues, you now need to refine the generated results. This may involve adjusting the output to fit constraints such as word count or combining concepts from different generated results.

A simple method of iteration is to generate multiple outputs and review them to understand the concepts and structures being used. Once the outputs have been evaluated, you can select the most suitable ones and combine them into a coherent response. Another iterative method is to start small and **gradually expand**. This requires more than one prompt: an initial prompt for drafting the initial one or two paragraphs, followed by additional prompts to expand on the content already written. Here is a potential philosophical discussion prompt:

> *"Is mathematics an invention or a discovery? Use careful reasoning to explain your answer."*

Add it to a simple prompt as follows:

```prompt
Write an opening paragraph to answer the following question:
Is mathematics an invention or a discovery? Use careful reasoning to explain your answer.
```

After generating some responses, we found one that we like:

```prompt
Philosophers and mathematicians have long debated whether mathematics is an invention or a discovery.
Some argue that the concepts and principles of mathematics exist independently of human thought, and therefore can be considered discovered rather than invented.
For example, the concepts of numbers, ratios, and geometric shapes are observed in the natural world and can be abstracted and systematically studied.
```

It's a good start. Then, use another prompt to expand:

```prompt
I am writing a detailed essay to answer the following prompt:
Is mathematics an invention or a discovery? Use careful reasoning to explain your answer. Here is my current situation: Philosophers and mathematicians have long debated whether mathematics is an invention or a discovery.
Some argue that the concepts and principles of mathematics exist independently of human thought, and therefore can be considered discovered rather than invented.
For example, the concepts of numbers, ratios, and geometric shapes are observed in the natural world and can be abstracted and systematically studied. Write the next paragraph of my essay.
```

I used this prompt to generate several extensions and selected one that I liked, resulting in the following:

```prompt
Philosophers and mathematicians have long debated whether mathematics is invented or discovered.
Some argue that the concepts and principles of mathematics exist independently of human thought, and therefore can be considered to be discovered rather than invented.
For example, the concepts of numbers, ratios, and geometric shapes are observed in the natural world and can be abstracted and systematically studied.
On the other hand, some believe that mathematics is largely an invention of human thought.
Mathematical principles are based on logical reasoning, which is a characteristic of human thought.
For instance, Euclidean geometry is based on abstract axioms and premises, accepted as true without the need for proof.
Therefore, geometry can be considered an invention of human thought rather than a discovery.
Similarly, mathematical formulas and equations are used to model and predict physical phenomena, which are the result of human reasoning.
```

Using the prompt extensions, we can iteratively write and iterate at each step. This is very useful for situations that require **generating higher quality output and incremental modifications**.

## Further Reading

- **Learn Prompting**: [https://learnprompting.org/en-US/docs/intro](https://learnprompting.org/en-US/docs/intro)
