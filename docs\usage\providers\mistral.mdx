---
title: Using Mistral AI API Key in LobeChat
description: >-
  Learn how to integrate Mistral AI into LobeChat for enhanced conversational experiences. Follow the steps to configure Mistral AI and start using its models.

tags:
  - Mistral AI
  - Web UI
  - API key
---

# Using Mistral AI in LobeChat

<Image alt={'Using Mistral AI in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/********/a3f9f63a-48f8-4567-b960-7f3636c0d4ed'} />

The Mistral AI API is now available for everyone to use. This document will guide you on how to use [Mistral AI](https://mistral.ai/) in LobeChat:

<Steps>
  ### Step 1: Obtain Mistral AI API Key

  - Create a [Mistral AI](https://mistral.ai/) account
  - Obtain your [API key](https://console.mistral.ai/user/api-keys/)

  <Image alt={'Obtain your API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/79faa59a-dfc0-4365-a679-5fc12c12bc70'} />

  ### Step 2: Configure Mistral AI in LobeChat

  - Go to the `Settings` interface in LobeChat
  - Find the setting for `Mistral AI` under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/ba8e688a-e0c1-4567-9013-94205f83fc60'} />

  <Callout type={'warning'}>
    If you are using mistral.ai, your account must have a valid subscription for the API key to work
    properly. Newly created API keys may take 2-3 minutes to become active. If the "Test" button
    fails, please retry after 2-3 minutes.
  </Callout>

  - Enter the obtained API key
  - Choose a Mistral AI model for your AI assistant to start the conversation

  <Image alt={'Select Mistral AI Model and Start Conversation'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/82cf4f5c-be5c-4126-a475-3a03468a9c39'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to Mistral AI's relevant
    pricing policies.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by Mistral AI in LobeChat.
