---
title: 在 LobeChat 中使用 Stepfun 阶跃星辰 API Key
description: 学习如何在 LobeChat 中配置和使用 Stepfun 阶跃星辰的人工智能模型，包括获取 API Key 和选择模型开始对话。
tags:
  - Stepfun 阶跃星辰
  - API key
  - Web UI
---

# 在 LobeChat 中使用 Stepfun 阶跃星辰

<Image alt={'在 LobeChat 中使用 Stepfun 阶跃星辰'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/95717e2b-1a55-4fca-a96b-b1c186ed4563'} />

[Stepfun 阶跃星辰](https://www.stepfun.com/)是一家专注于通用人工智能 (AGI) 研发的创业公司，目前已推出 Step-1 千亿参数语言大模型、Step-1V 千亿参数多模态大模型，以及 Step-2 万亿参数 MoE 语言大模型预览版。

本文档将指导你如何在 LobeChat 中使用 Stepfun 阶跃星辰:

<Steps>
  ### 步骤一：获取 Stepfun 阶跃星辰 API 密钥

  - 访问并登录 [Stepfun Stepfun 阶跃星辰开放平台](https://platform.stepfun.com/)
  - 进入`接口密钥`菜单，系统已为你创建好 API 密钥
  - 复制已创建的 API 密钥

  <Image alt={'获取 Stepfun 阶跃星辰API密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/33d8ce3b-0083-48aa-9a66-3825e726c4de'} />

  ### 步骤二：在 LobeChat 中配置 Stepfun Stepfun 阶跃星辰

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到` Stepfun 阶跃星辰`的设置项

  <Image alt={'填写 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/470e5669-650b-46cf-8024-a1476c166059'} />

  - 打开 Stepfun 阶跃星辰并填入获得的 API 密钥
  - 为你的 AI 助手选择一个 Stepfun 阶跃星辰的模型即可开始对话

  <Image alt={'选择 Stepfun 阶跃星辰模型并开始对话'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/0275a552-f189-42b5-bf40-f9891c428b3d'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Stepfun 阶跃星辰的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Stepfun 阶跃星辰提供的模型进行对话了。
