---
title: Using Nvidia NIM API Key in LobeChat
description: Learn how to configure and use Nvidia NIM AI models in LobeChat, obtain an API key, and start a conversation.
tags:
  - LobeChat
  - Nvidia NIM
  - API Key
  - Web UI
---

# Using Nvidia NIM in LobeChat

<Image alt={'Using Nvidia NIM in LobeChat'} cover src={'https://github.com/user-attachments/assets/539349dd-2c16-4f42-b525-cca74e113541'} />

[NVIDIA NIM](https://developer.nvidia.com/nim) is part of NVIDIA AI Enterprise and is designed to accelerate the deployment of generative AI applications through microservices. It provides a set of easy-to-use inference microservices that can run on any cloud, data center, or workstation, supporting NVIDIA GPU acceleration.

This document will guide you on how to access and use AI models provided by Nvidia NIM in LobeChat:

<Steps>
  ### Step 1: Obtain Nvidia NIM API Key

  - First, visit the [Nvidia NIM console](https://build.nvidia.com/explore/discover) and complete the registration and login.
  - On the `Models` page, select the model you need, such as <PERSON>seek-R1.

  <Image alt={'Select Model'} inStep src={'https://github.com/user-attachments/assets/b49ed0c1-d6bf-4f46-b9df-5f7c730afaa3'} />

  - On the model details page, click "Build with this NIM".
  - In the pop-up dialog, click the `Generate API Key` button.

  <Image alt={'Get API Key'} inStep src={'https://github.com/user-attachments/assets/5321f987-2c64-4211-8549-bd30ca9b59b9'} />

  - Copy and save the created API Key.

  <Callout type={'warning'}>
    Please store the key securely as it will only appear once. If you accidentally lose it, you will
    need to create a new key.
  </Callout>

  ### Step 2: Configure Nvidia NIM in LobeChat

  - Visit the `Application Settings` -> `AI Service Provider` interface in LobeChat.
  - Find the settings item for `Nvidia NIM` in the list of providers.

  <Image alt={'Fill in the Nvidia NIM API Key'} inStep src={'https://github.com/user-attachments/assets/dfc45807-2ed6-43eb-af4c-47df66dfff7d'} />

  - Enable the Nvidia NIM service provider and fill in the obtained API key.
  - Select an Nvidia NIM model for your assistant and start the conversation.

  <Image alt={'Select Nvidia NIM Model'} inStep src={'https://github.com/user-attachments/assets/cb4ba5fe-c223-4b9f-a662-de93e4a536d1'} />

  <Callout type={'warning'}>
    You may need to pay the API service provider during use, please refer to Nvidia NIM's related fee policies.
  </Callout>
</Steps>

Now you can use the models provided by Nvidia NIM to have conversations in LobeChat.
