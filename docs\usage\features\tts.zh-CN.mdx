---
title: LobeChat 支持 TTS & STT 语音会话
description: LobeChat 支持文字转语音（TTS）和语音转文字（STT）技术，提供高品质声音选项，个性化交流体验。了解更多关于 Lobe TTS 工具包。
tags:
  - TTS
  - STT
  - 语音会话
  - LobeChat
  - Lobe TTS
  - 文字转语音
  - 语音转文字
---

# TTS & STT 语音会话

<Image alt={'TTS & STT 语音会话'} borderless cover src={'https://github.com/user-attachments/assets/50189597-2cc3-4002-b4c8-756a52ad5c0a'} />

LobeChat 支持文字转语音（Text-to-Speech，TTS）和语音转文字（Speech-to-Text，STT）技术，我们的应用能够将文本信息转化为清晰的语音输出，用户可以像与真人交谈一样与我们的对话代理进行交流。用户可以从多种声音中选择，给助手搭配合适的音源。 同时，对于那些倾向于听觉学习或者想要在忙碌中获取信息的用户来说，TTS 提供了一个极佳的解决方案。

在 LobeChat 中，我们精心挑选了一系列高品质的声音选项 (OpenAI Audio, Microsoft Edge Speech)，以满足不同地域和文化背景用户的需求。用户可以根据个人喜好或者特定场景来选择合适的语音，从而获得个性化的交流体验。

## Lobe TTS

<Image alt={'LobeTTS @lobehub/tts'} borderless src={'https://github.com/lobehub/lobe-chat/assets/28616219/52d20fdc-48c0-45e4-9c51-ee150fc18be7'} />

[`@lobehub/tts`](https://tts.lobehub.com) 是一个使用 TS 语言开发的，高质量 TTS 工具包，支持在服务端和浏览器中使用。

- **服务端**：只要使用 15 行代码，即可实现对标 OpenAI TTS 服务的高质量语音生成能力。目前支持 EdgeSpeechTTS 与 MicrosoftTTS 与 OpenAITTS、OpenAISTT。
- **浏览器**：提供了高质量的 React Hooks 与可视化音频组件，支持加载、播放、暂停、拖动时间轴等常用功能，且提供了非常丰富的音轨样式调整能力。

<Callout type={'info'}>
  我们在实现 LobeChat 的 TTS 功能过程中，发现市面上并没有一款很好的 TTS
  前端库，导致在实现上耗费了很多精力，包括数据转换、音频进度管理、语音可视化等。秉承「 Community
  First 」 的理念，我们把这套实现打磨并开源了出来，希望能帮助到想要实现 TTS 的社区开发者们。
</Callout>
