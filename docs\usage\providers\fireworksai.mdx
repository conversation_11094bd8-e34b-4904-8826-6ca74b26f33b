---
title: Using Fireworks AI in LobeChat
description: >-
  Learn how to integrate and utilize Fireworks AI's language model APIs in LobeChat.

tags:
  - LobeChat
  - Fireworks AI
  - API Key
  - Web UI
---

# Using Fireworks AI in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/facdc83c-e789-4649-8060-7f7a10a1b1dd'} />

[Fireworks.ai](https://fireworks.ai/) is a high-performance generative AI model inference platform that allows users to access and utilize various models through its API. The platform supports multiple modalities, including text and visual language models, and offers features like function calls and JSON schemas to enhance the flexibility of application development.

This article will guide you on how to use Fireworks AI in LobeChat.

<Steps>
  ### Step 1: Obtain an API Key for Fireworks AI

  - Log in to the [Fireworks.ai Console](https://fireworks.ai/account/api-keys)
  - Navigate to the `User` page and click on `API Keys`
  - Create a new API key

  <Image alt={'Create API Key'} inStep src={'https://github.com/user-attachments/assets/eb027093-5ceb-4a9d-8850-b791fbf69a71'} />

  - Copy and securely save the generated API key

  <Image alt={'Save API Key'} inStep src={'https://github.com/user-attachments/assets/28590f7f-bfee-4215-b50b-8feddbf72366'} />

  <Callout type={'warning'}>
    Please store the key securely, as it will appear only once. If you accidentally lose it, you will
    need to create a new key.
  </Callout>

  ### Step 2: Configure Fireworks AI in LobeChat

  - Access the `Settings` interface in LobeChat
  - Under `AI Service Provider`, locate the settings for `Fireworks AI`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/12c1957d-f050-4235-95da-d55ddedfa6c9'} />

  - Enter the obtained API key
  - Select a Fireworks AI model for your AI assistant to start a conversation

  <Image alt={'Select Fireworks AI Model and Start Conversation'} inStep src={'https://github.com/user-attachments/assets/378df8df-8ec4-436e-8451-fbc52705faee'} />

  <Callout type={'warning'}>
    Please note that you may need to pay fees to the API service provider during use; refer to
    Fireworks AI's pricing policy for details.
  </Callout>
</Steps>

You are now ready to use the models provided by Fireworks AI for conversations in LobeChat.
