---
title: 在 LobeChat 中使用 Cloudflare Workers AI
description: 学习如何在 LobeChat 中配置和使用 Cloudflare Workers AI 的 API Key，以便开始对话和交互。
tags:
  - LobeChat
  - Cloudflare
  - Workers AI
  - 供应商
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 Cloudflare Workers AI

<Image cover src={'https://github.com/user-attachments/assets/91fe32a8-e5f0-47ff-b8ae-d036c8a7bff1'} />

[Cloudflare Workers AI](https://www.cloudflare.com/developer-platform/products/workers-ai/) 是一种将人工智能能力集成到 Cloudflare Workers 无服务器计算平台的服务。其核心功能在于通过 Cloudflare 的全球网络提供快速、可扩展的计算能力，降低运维开销。

本文档将指导你如何在 LobeChat 中使用 Cloudflare Workers AI:

<Steps>
  ### 步骤一：获取 Cloudflare Workers AI 的 API Key

  - 访问 [Cloudflare 官网](https://www.cloudflare.com/) 并注册一个账号。
  - 登录 [Cloudflare 控制台](https://dash.cloudflare.com/).
  - 在左侧的菜单中找到 `AI` > `Workers AI` 选项。

  <Image alt={'Cloudflare Workers AI'} inStep src={'https://github.com/user-attachments/assets/4257e123-9018-4562-ac66-0f39278906f5'} />

  - 在 `使用 REST API` 中点击 `创建 Workers AI API 令牌` 按钮
  - 在弹出的侧边栏中复制并保存你的 `API 令牌`
  - 同时也复制并保存你的 `账户ID`

  <Image alt={'Cloudflare Workers AI API Token'} inStep src={'https://github.com/user-attachments/assets/f54c912d-3ee9-4f85-b8bf-619790e51b49'} />

  <Callout type={'warning'}>
    - 请安全地存储 API 令牌，因为它只会出现一次。如果您意外丢失它，您将需要创建一个新令牌。
  </Callout>

  ### 步骤二：在 LobeChat 中配置 Cloudflare Workers AI

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `Cloudflare` 的设置项

  <Image alt={'填入访问令牌'} inStep src={'https://github.com/user-attachments/assets/82a7ebe0-69ad-43b6-8767-1316b443fa03'} />

  - 填入获得的 `API 令牌`
  - 填入你的`账户ID`
  - 为你的 AI 助手选择一个 Cloudflare Workers AI 的模型即可开始对话

  <Image alt={'选择 Cloudflare Workers AI 模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/09be499c-3b04-4dd6-a161-6e8ebe788354'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Cloudflare 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Cloudflare Workers AI 提供的模型进行对话了。
