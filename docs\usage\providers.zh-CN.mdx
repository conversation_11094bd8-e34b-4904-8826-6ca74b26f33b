---
title: 在 LobeChat 中使用多模型服务商
description: 了解 LobeChat 在多模型服务商支持方面的最新进展，包括已支持的模型服务商和计划中的扩展，以及本地模型支持的使用方式。
tags:
  - LobeChat
  - AI 会话服务
  - 模型服务商
  - 多模型支持
  - 本地模型支持
  - AWS Bedrock
  - Google AI
  - ChatGLM
  - Moonshot AI
  - 01 AI
  - Together AI
  - Ollama
---

# 在 LobeChat 中使用多模型服务商

<Image alt={'多模型服务商支持'} borderless cover src={'https://github.com/lobehub/lobe-chat/assets/17870709/1148639c-2687-4a9c-9950-8ca8672f34b6'} />

在 LobeChat 的不断发展过程中，我们深刻理解到在提供 AI 会话服务时模型服务商的多样性对于满足社区需求的重要性。因此，我们不再局限于单一的模型服务商，而是拓展了对多种模型服务商的支持，以便为用户提供更为丰富和多样化的会话选择。

通过这种方式，LobeChat 能够更灵活地适应不同用户的需求，同时也为开发者提供了更为广泛的选择空间。

## 模型服务商使用教程

<ProviderCards locale={'zh'} />
