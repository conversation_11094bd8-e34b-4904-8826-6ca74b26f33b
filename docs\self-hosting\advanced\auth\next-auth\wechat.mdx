---
title: Configure Wechat Authentication Service in LobeChat
description: >-
  Learn how to configure Wechat authentication service in LobeChat, including creating a new Wechat App, setting permissions, and environment variables.

tags:
  - Wechat Authentication
  - Wechat App
  - Environment Variable Configuration
  - Single Sign-On
  - LobeChat
---

# Configure Wechat Authentication Service

## Wechat Configuration Process

<Steps>
  ### Create a Wechat Application

  Click [here](https://open.weixin.qq.com/cgi-bin/index) and then click "Management Center", "Website Application", and "Create Website Application" in sequence.

  Fill in the information as required by the official website prompts and submit for review.

  After successful creation, click "Application Details" to obtain the AppID and AppSecret.

  ### Configure Environment Variables

  When deploying LobeChat, you need to configure the following environment variables:

  | Environment Variable      | Type     | Description                                                                                                                                                                                           |
  | ------------------------- | -------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
  | `NEXT_AUTH_SECRET`        | Required | Key used to encrypt Auth.js session tokens. You can generate the key using the command: `openssl rand -base64 32`                                                                                     |
  | `NEXT_AUTH_SSO_PROVIDERS` | Required | Select the Single Sign-On provider for LobeChat. Use `github` for Github.                                                                                                                             |
  | `WECHAT_CLIENT_ID`        | Required | Client ID from the Wechat website application details page                                                                                                                                            |
  | `WECHAT_CLIENT_SECRET`    | Required | Client Secret from the Wechat website application details page                                                                                                                                        |
  | `NEXTAUTH_URL`            | Required | This URL is used to specify the callback address for Auth.js when performing OAuth authentication. Only set it if the default generated redirect address is incorrect. `https://example.com/api/auth` |

  <Callout type={'tip'}>
    Go to [📘 Environment Variables](/en/docs/self-hosting/environment-variables/auth#wechat) for more details about related variables.
  </Callout>
</Steps>

<Callout type={'info'}>
  After successful deployment, users will be able to authenticate through the WeChat Open Platform
  and use LobeChat.
</Callout>
