---
title: Guide to Using Text-to-Image Models in LobeChat
description: >-
  Learn how to utilize text-to-image generation in LobeChat using DALL-E and Midjourney plugins. Generate images seamlessly with AI assistance.

tags:
  - Text-to-Image Models
  - LobeChat
  - DALL-E
  - Midjourney
  - Plugin Installation
  - AI Assistance
---

# Guide to Using Text-to-Image Models in LobeChat

LobeChat supports text-to-image generation through a plugin mechanism. Currently, LobeChat comes with the built-in DALL-E plugin, which allows users to generate images using OpenAI's DALL-E model. Additionally, users can also install the official Midjourney plugin to utilize the Midjourney text-to-image feature.

## DALL-E Model

If you have configured the OpenAI API, you can enable the DALL-E plugin directly in the assistant interface and input prompts in the conversation for AI to generate images for you.

<Image alt={'DALL-E Plugin'} src={'https://github.com/user-attachments/assets/7b11b795-4dd3-4020-a9ba-a3723d5f1f28'} />

If the DALL-E plugin is not available, please check if the OpenAI API key has been correctly configured.

## Midjourney Model

LobeChat also offers the Midjourney plugin, which generates images by calling the Midjourney API. Please install the Midjourney plugin in the plugin store beforehand.

<Image alt={'Midjourney Plugin'} src={'https://github.com/user-attachments/assets/5f526846-02cd-4dbc-b6e3-a603fa5ac8e7'} />

<Callout type={'info'}>
  info For plugin installation, please refer to [Plugin Usage](/docs/usage/plugins/basic-usage)
</Callout>

When using the Midjourney plugin for the first time, you will need to fill in your Midjourney API key in the plugin settings.

<Image alt={'Midjourney Plugin Settings'} src={'https://github.com/user-attachments/assets/a5d7a543-aec5-457e-a36c-aa1f82f7bc8a'} />
