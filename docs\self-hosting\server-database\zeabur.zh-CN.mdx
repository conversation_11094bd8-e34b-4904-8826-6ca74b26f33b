---
title: 在 Zeabur 上部署 LobeChat
description: 按照指南准备 OpenAI API Key 并点击按钮进行部署。在部署完成后，即可开始使用 LobeChat 并选择是否绑定自定义域名。
tags:
  - Zeabur
  - LobeChat
  - OpenAI API Key
  - 部署流程
  - 自定义域名
---

# 使用 Zeabur 部署 LobeChat 数据库版

<Callout type="info">
  本文假设你已经熟悉 LobeChat
  服务器数据库版的部署基本原理和流程，因此只包含与核心环境变量配置相关的内容。如果你对 LobeChat
  服务器数据库版的部署原理不熟悉，请先参考[部署服务器数据库](/zh/docs/self-hosting/server-database)。
</Callout>

在 Zeabur 的模板中总共包含有以下四个服务：

- Logto 提供身份校验
- 带有 Vector 插件的 PostgreSQL 来做数据存储和向量化
- MinIO 作为对象存储
- LobeChat Database 的实例

## 在 Zeabur 上部署

这里是在 Zeabur 上部署 LobeChat 服务器数据库版的流程：

<Steps>
  ### 前往 Zeabur 上的模板页面

  前往 [Zeabur 上的 LobeChat 数据库模板页面](https://zeabur.com/templates/RRSPSD) 并点击 "Deploy" 按钮。

  ### 填写必要的环境变量

  在你点击 “部署 “按钮后，你会看到一个模态弹窗，你可以在这里填写必要的环境变量。

  以下是你需要填写的环境变量：

  - OpenAI API key: 你的 OpenAI API key 用于获取模型的访问权限。
  - LobeChat Domain: 一个免费的 `.zeabur.app` 后缀的域名。
  - MinIO Public Domain: 一个免费的 `.zeabur.app` 后缀的域名为了暴露 MinIO 服务以公开访问资源。
  - Logto Console Domain: 一个免费的 `.zeabur.app` 后缀的域名来访问 Logto 的控制台。
  - Logto API Domain: 一个免费的 `.zeabur.app` 后缀的域名来访问 Logto 的 API。

  ### 选择一个区域并部署

  在你填写完所有必要的环境变量后，选择一个你想要部署 LobeChat 数据库的区域并点击 “部署” 按钮。

  你会看到另一个模态弹窗，你可以在这里看到部署的进度。

  ### 配置 Logto

  当部署完成后，你会被自动导航到你在 Zeabur 控制台上刚刚创建的项目。你需要再进一步配置你的 Logto 服务。

  使用你刚绑定的域名来访问你的 Logto 控制台，创建一个新项目以获得对应的客户端 ID 与密钥，将它们填入你的 LobeChat 服务的变量中。关于如何填入变量，可以参照 [Zeabur 的官方文档](https://zeabur.com/docs/deploy/variables)。

  Logto 的详细配置可以参考[这篇文档](/zh/docs/self-hosting/advanced/auth/next-auth/logto)。

  ### 访问你的 LobeChat

  按下 `LobeChat-Database` 你会看到你刚刚创建的公共域名，点击它以访问你的 LobeChat 数据库。

  你可以选择绑定一个自定义域名，这里有一个关于如何在 Zeabur 上[绑定自定义域名](https://zeabur.com/docs/deploy/domain-binding)的指南。
</Steps>
