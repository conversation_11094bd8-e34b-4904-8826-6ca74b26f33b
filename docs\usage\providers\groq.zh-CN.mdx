---
title: 在 LobeChat 中使用 Groq API Key
description: 了解如何获取 GroqCloud API Key，并在 LobeChat 中配置 Groq，体验 Groq 强大的性能。
tags:
  - LLAMA3
  - Qwen2
  - API keys
  - Web UI
  - API Key
---

# 在 LobeChat 中使用 Groq

<Image alt={'在 LobeChat 中使用 Groq'} cover src={'https://github.com/lobehub/lobe-chat/assets/17870709/1d840e27-fa74-4e71-b777-330bf41d6dff'} />

Groq 的 [LPU 推理引擎](https://wow.groq.com/news_press/groq-lpu-inference-engine-leads-in-first-independent-llm-benchmark/) 在最新的独立大语言模型（LLM）基准测试中表现卓越，以其惊人的速度和效率重新定义了 AI 解决方案的标准。通过 LobeChat 与 Groq Cloud 的集成，你现在可以轻松地利用 Groq 的技术，在 LobeChat 中加速大语言模型的运行。

<Callout type={'info'}>
  Groq LPU 推理引擎在内部基准测试中连续达到每秒 300 个令牌的速度，据 ArtificialAnalysis.ai
  的基准测试确认，Groq 在吞吐量（每秒 241 个令牌）和接收 100 个输出令牌的总时间（0.8
  秒）方面优于其他提供商。
</Callout>

本文档将指导你如何在 LobeChat 中使用 Groq：

<Steps>
  ### 获取 GroqCloud API Key

  首先，你需要到 [GroqCloud Console](https://console.groq.com/) 中获取一个 API Key。

  <Image alt={'获取 GroqCloud API Key'} height={274} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/6942287e-fbb1-4a10-a1ce-caaa6663da1e'} />

  在控制台的 `API Keys` 菜单中创建一个 API Key。

  <Image alt={'保存 GroqCloud API Key'} height={274} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/eb57ca57-4f45-4409-91ce-9fa9c7c626d6'} />

  <Callout type={'warning'}>
    妥善保存弹窗中的 key，它只会出现一次，如果不小心丢失了，你需要重新创建一个 key。
  </Callout>

  ### 在 LobeChat 中配置 Groq

  你可以在 `设置` -> `AI 服务商` 中找到 Groq 的配置选项，将刚才获取的 API Key 填入。

  <Image alt={'Groq 服务商设置'} height={274} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/88948a3a-6681-4a8d-9734-a464e09e4957'} />
</Steps>

接下来，在助手的模型选项中，选中一个 Groq 支持的模型，就可以在 LobeChat 中体验 Groq 强大的性能了。

<Video alt={'选择 Groq 模型'} src="https://github.com/lobehub/lobe-chat/assets/28616219/b6b8226b-183f-4249-8255-663a5e9f5af4" />
