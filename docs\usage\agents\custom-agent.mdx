---
title: Custom LobeChat Assistant Guide - Adding and Iterating Assistants
description: >-
  Learn how to add assistants to your favorites list in LobeChat through the role market or by creating custom assistants. Explore detailed steps for creating custom assistants and quick setup tips.

tags:
  - LobeChat
  - Adding Assistants
  - Custom Assistant
  - Role Market
  - Creating Assistants
  - Assistant Configuration
---

# Custom Assistant Guide

As the basic functional unit of LobeChat, adding and iterating assistants is very important. Now you can add assistants to your favorites list in two ways.

## `A` Add through the role market

If you are a beginner in Prompt writing, you might want to browse the assistant market of LobeChat first. Here, you can find commonly used assistants submitted by others and easily add them to your list with just one click, which is very convenient.

<Image
  alt={'Add through the role market'}
  src={
  'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279588466-4c32041b-a8e6-4703-ba4a-f91b7800e359.png'
}
/>

## `B` Create a custom assistant

When you need to handle specific tasks, you need to consider creating a custom assistant to help you solve the problem. You can add and configure the assistant in detail in the following ways.

<Cards rows={2}>
  <Image alt={'Create a custom assistant S1'} src={'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279587283-a3ea8dfd-70fb-47ee-ab00-e3911ac6a939.png'} />

  <Image alt={'Create a custom assistant S2'} src={'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279587292-a3d102c6-f61e-4578-91f1-c0a4c97588e1.png'} />
</Cards>

<Callout type={'tip'}>
  **Quick Setup Tip**: You can conveniently modify the Prompt through the quick edit button in the
  sidebar.
</Callout>

<Cards rows={2}>
  <Image alt={'Create a custom assistant S3'} src={'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279587294-388d1877-193e-4a50-9fe8-8fbcc3ccefa0.png'} />

  <Image alt={'Create a custom assistant S4'} src={'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279587298-333da153-13b8-4557-a0a2-cff55e7bc1c0.png'} />
</Cards>

If you want to understand Prompt writing tips and common model parameter settings, you can continue to view:

<Cards>
  <Card href={'/docs/usage/agents/prompt'} title={'Prompt User Guide'} />

  <Card href={'/docs/usage/agents/model'} title={'Large Language Model User Guide'} />
</Cards>
