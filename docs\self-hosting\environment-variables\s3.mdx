---
title: Configuring S3 Storage Service
description: >-
  Learn how to configure S3 storage service to support multimodal AI sessions and image uploads.

tags:
  - S3 Storage
  - Multimodal AI
  - Image Upload
  - File Storage
---

# Configuring S3 Storage Service

LobeChat supports multimodal AI sessions, including the ability to upload unstructured data such as images and files to large language models. To optimize storage and performance, we use S3-compatible file storage services to store image files and support file upload/knowledge base functionality.

## Core Environment Variables

### `S3_ACCESS_KEY_ID`

- Type: Required
- Description: Access key ID for S3 storage service
- Default: -
- Example: `AKIAIOSFODNN7EXAMPLE`

### `S3_SECRET_ACCESS_KEY`

- Type: Required
- Description: Secret access key for S3 storage service
- Default: -
- Example: `wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY`

### `S3_ENDPOINT`

- Type: Required
- Description: Request endpoint for the bucket; for `virtual-host` mode, should not include the bucket name prefix
- Default: -
- Example: `https://cos.ap-chengdu.myqcloud.com`

### `S3_BUCKET`

- Type: Required
- Description: Name of the storage bucket
- Default: -
- Example: `my-bucket`

### `S3_REGION`

- Type: Optional
- Description: Region of the storage bucket
- Default: -
- Example: `ap-chengdu`

### `S3_SET_ACL`

- Type: Optional
- Description: Whether to set ACL to `public-read` when uploading files
- Default: `1`
- Example: `0`

### `S3_PUBLIC_DOMAIN`

- Type: Required
- Description: Public access domain for the bucket, used to access files in the bucket
- Default: -
- Example: `https://files.example.com`

### `S3_ENABLE_PATH_STYLE`

- Type: Optional
- Description: Whether to enable S3 `path-style` access mode
- Default: `0`
- Example: `1`

<Callout type={'info'}>
  `path-style` and `virtual-host` are different methods of accessing buckets and objects in S3, with differences in URL structure and domain name resolution.

  Assuming the S3 service provider's domain is s3.example.net, the bucket is mybucket, and the object is config.env, the specific differences are:

  - path-style: `s3.example.net/mybucket/config.env`
  - virtual-host: `mybucket.s3.example.net/config.env`
</Callout>

### `LLM_VISION_IMAGE_USE_BASE64`

- Type: Optional
- Description: Set to `1` to use base64 encoding for image upload.
- Default: undefined
- Example: `1`

When set to `1`, LobeChat will convert images to base64 encoding before uploading them to the LLM model. When encountering the following error, please consider configuring this environment variable to `1`:

```log
Route: [xai] ProviderBizError: Fetching images over plain http:// is not supported.
```
