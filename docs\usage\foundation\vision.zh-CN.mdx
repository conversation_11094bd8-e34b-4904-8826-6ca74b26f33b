---
title: 视觉模型使用指南 - 支持多模态交互的大语言模型
description: "了解如何在LobeChat中使用支持视觉识别功能的大语言模型，通过上传图片或拖拽图片到输入框进行交互，并选择带有\U0001F441️图标的模型进行图片内容交互。"
tags:
  - 视觉模型
  - 多模态交互
  - 大语言模型
  - 自定义模型配置
---

# 视觉模型使用指南

当前支持视觉识别的大语言模型生态日益丰富。从 `gpt-4-vision` 开始，LobeChat 开始支持各类具有视觉识别能力的大语言模型，这使得 LobeChat 具备了多模态交互的能力。

<Video alt={'视觉模型使用'} src={'https://github.com/user-attachments/assets/1c6b4975-bfc3-4470-a934-558ff7a16941'} />

## 图片输入

如果你当前使用的模型支持视觉识别功能，您可以通过上传文件或直接将图片拖入输入框的方式输入图片内容。模型会自动识别图片内容，并根据您的提示词给出反馈。

<Image alt={'图片输入'} src={'https://github.com/user-attachments/assets/e6836560-8b05-4382-b761-d7624da4b0f1'} />

## 视觉模型

在模型列表中，模型名称后面带有`👁️`图标表示该模型支持视觉识别功能。选择该模型后即可发送图片内容。

<Image alt={'视觉模型'} src={'https://github.com/user-attachments/assets/fa07a326-04c8-4744-bb93-cef715d1d71f'} />

## 自定义模型配置

如果您需要添加当前列表中没有的自定义模型，并且该模型明确支持视觉识别功能，您可以在`自定义模型配置`中开启`视觉识别`功能，使该模型能够与图片进行交互。

<Image alt={'自定义模型配置'} src={'https://github.com/user-attachments/assets/c24718cc-402b-4298-b046-8b4aee610cbc'} />
