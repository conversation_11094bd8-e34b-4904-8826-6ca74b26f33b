---
title: 在 LobeChat 中使用 Anthropic Claude API Key
description: >-
  学习如何在 LobeChat 中配置和使用 Anthropic Claude API， Claude 3.5 sonnet / Claude 3 Opus / Claude 3 haiku

tags:
  - Anthropic Claude
  - API
  - WebUI
  - AI助手
---

# 在 LobeChat 中使用 Anthropic Claude

<Image alt={'在 LobeChat 中使用 Anthropic Claude'} cover src={'https://github.com/lobehub/lobe-chat/assets/17870709/620b956b-dcb2-442a-8bb1-9aa22681dfa4'} />

Anthropic Claude API 现在可供所有人使用，本文档将指导你如何在 LobeChat 中使用 [Anthropic Claude](https://www.anthropic.com/api):

<Steps>
  ### 步骤一：获取 Anthropic Claude API 密钥

  - 创建一个 [Anthropic Claude API](https://www.anthropic.com/api) 帐户
  - 获取您的 [API 密钥](https://console.anthropic.com/settings/keys)

  <Image alt={'创建 API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/15e09e71-5899-4805-9c5e-1f7c57be04ae'} />

  <Callout type={'warning'}>
    Claude API 现在提供 5 美元的免费积分，但是，它仅适用于某些特定国家 / 地区，您可以转到 Dashboard >
    Claim 查看它是否适用于您所在的国家 / 地区。
  </Callout>

  - 设置您的账单，让 API 密钥在 [https://console.anthropic.com/settings/plans](https://console.anthropic.com/settings/plans) 上工作（选择 “生成” 计划，以便您可以添加积分并仅为使用量付费）

  <Image alt={'设置您的账单'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/385f663f-cae2-4383-9bb0-52c45e5d7d7a'} />

  ### 步骤二：在 LobeChat 中配置 Anthropic Claude

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`Anthropic Claude`的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/ff9c3eb8-412b-4275-80be-177ae7b7acbc'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 Anthropic Claude 的模型即可开始对话

  <Image alt={'选择 Anthropic Claude 模型并开始对话'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/6cdc5c0e-0508-44ed-a283-03f6b538ed8a'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Anthropic Claude 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Anthropic Claude 提供的模型进行对话了。
