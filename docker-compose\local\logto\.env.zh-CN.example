# Logto 鉴权相关
AUTH_LOGTO_ID=
AUTH_LOGTO_SECRET=
AUTH_LOGTO_ISSUER=

# MinIO S3 配置
MINIO_ROOT_USER=YOUR_MINIO_USER
MINIO_ROOT_PASSWORD=YOUR_MINIO_PASSWORD

# 在下方配置 minio 中添加的桶
MINIO_LOBE_BUCKET=lobe
S3_ACCESS_KEY_ID=
S3_SECRET_ACCESS_KEY=


# Proxy，如果你需要的话（比如你使用 GitHub 作为鉴权服务提供商）
# HTTP_PROXY=http://localhost:7890
# HTTPS_PROXY=http://localhost:7890

# 其他环境变量，视需求而定，可以参照客户端版本的环境变量配置，注意不要有 ACCESS_CODE
# OPENAI_API_KEY=sk-xxxx
# OPENAI_PROXY_URL=https://api.openai.com/v1
# OPENAI_MODEL_LIST=...


# ----- 相关配置 start -----
# 如没有特殊需要不用更改
LOBE_PORT=3210
LOGTO_PORT=3001
MINIO_PORT=9000

# Postgres 相关，也即 DB 必须的环境变量
LOBE_DB_NAME=lobechat
POSTGRES_PASSWORD=uWNZugjBqixf8dxC
