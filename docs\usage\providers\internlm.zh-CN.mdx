---
title: 在 LobeChat 中使用书生浦语
description: 学习如何在 LobeChat 中配置和使用书生浦语的 API Key，以便开始对话和交互。
tags:
  - LobeChat
  - 书生浦语
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用书生浦语

<Image cover src={'https://github.com/user-attachments/assets/be7dcd49-0165-4f7b-bf90-0739cc9dd212'} />

[书生浦语（InternLM）](https://platform.sensenova.cn/home) 是由上海人工智能实验室与书生集团联合推出的一款大型预训练语言模型。该模型专注于自然语言处理，旨在理解和生成自然语言，具备强大的语义理解和文本生成能力。

本文将指导你如何在 LobeChat 中使用书生浦语。

<Steps>
  ### 步骤一：获取书生浦语的 API 密钥

  - 注册并登录 [浦语 API](https://internlm.intern-ai.org.cn/api/tokens)
  - 创建一个 API 令牌
  - 在弹出窗口中保存 API 令牌

  <Image alt={'保存 API Token'} inStep src={'https://github.com/user-attachments/assets/0e2fdc5d-9623-4a74-a7f6-dcb802d52297'} />

  <Callout type={'warning'}>
    妥善保存弹窗中的 API 令牌，它只会出现一次，如果不小心丢失了，你需要重新创建一个 API 令牌。
  </Callout>

  ### 步骤二：在 LobeChat 中配置书生浦语

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `书生浦语` 的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/8ec7656e-1e3d-41e0-95a0-f6883135c2fc'} />

  - 填入获得的 `AccessKey ID` 和 `AccessKey Secret`
  - 为你的 AI 助手选择一个书生浦语的模型即可开始对话

  <Image alt={'选择书生浦语模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/76ad163e-ee19-4f95-a712-85bea764d3ec'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考书生浦语的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用书生浦语提供的模型进行对话了。
