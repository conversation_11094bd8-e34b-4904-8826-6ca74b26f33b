---
title: 在 LobeChat 中使用 Novita AI API Key
description: >-
  学习如何将 Novita AI 的大语言模型 API 集成到 LobeChat 中。跟随以下步骤注册 Novita AI 账号、创建 API Key、充值信用额度并在 LobeChat 中进行设置。并与我们的多种 AI 模型交谈。

tags:
  - Novita AI
  - Llama3
  - Mistral
  - uncensored
  - API key
  - Web UI
---

# 在 LobeChat 中使用 Novita AI

<Image alt={'在 LobeChat 中使用 Novita AI'} cover src={'https://github.com/user-attachments/assets/b2b36128-6a43-4a1f-9c08-99fe73fb565f'} />

[Novita AI](https://novita.ai/) 是一个 AI API 平台，它提供多种大语言模型与 AI 图像生成的 API 服务。支持 Llama3 (8B, 70B)，Mistral 和其他最新的模型。

本文档将指导你如何在 LobeChat 中使用 Novita AI:

<Steps>
  ### 步骤一：注册 Novita AI 账号并登录

  - 访问 [Novita.ai](https://novita.ai/) 并创建账号
  - 你可以使用 Google 或者 Github 账号登录
  - 注册后，Novita AI 会赠送 0.5 美元的使用额度

  <Image alt={'注册 Novita AI'} height={457} inStep src={'https://github.com/user-attachments/assets/f3177ce2-281c-4ed4-a061-239547b466c6'} />

  ### 步骤二：创建 API 密钥

  - 访问 Novita AI 的 [密钥管理页面](https://novita.ai/dashboard/key) ，创建并且复制一个 API 密钥.

  <Image alt={'创建 Novita AI API 密钥'} inStep src={'https://github.com/user-attachments/assets/1e33aff2-6186-4e1f-80a8-4a2c855d8cc1'} />

  ### 步骤三：在 LobeChat 中配置 Novita AI

  - 访问 LobeChat 的 `设置` 界面
  - 在 `AI 服务商` 下找到 `novita.ai` 的设置项
  - 打开 novita.ai 并填入获得的 API 密钥

  <Image alt={'在 LobeChat 中输入 Novita AI API 密钥'} inStep src={'https://github.com/user-attachments/assets/00c02637-873e-4e7e-9dc3-a95085b16dd7'} />

  - 为你的助手选择一个 Novita AI 模型即可开始对话

  <Image alt={'Select and use Novita AI model'} inStep src={'https://github.com/user-attachments/assets/6f9f400a-72e0-49de-94cb-5069fddf1163'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Novita AI 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Novita AI 提供的模型进行对话了。
