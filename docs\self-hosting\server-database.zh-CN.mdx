---
title: 使用服务端数据库部署 - 配置数据库、身份验证服务和 S3 存储服务
description: 本文将介绍服务端数据库版 LobeChat 的部署思路，解释如何配置数据库、身份验证服务和 S3 存储服务。
tags:
  - 服务端数据库
  - Postgres
  - S3存储服务
  - 数据库配置
  - 身份验证服务
  - 环境变量配置
---

# 使用服务端数据库部署

LobeChat 默认使用客户端数据库（IndexedDB），同时也支持使用服务端数据库（下简称 DB 版）。LobeChat 采用了 Postgres 作为后端存储数据库。

<Callout>
  PostgreSQL 是一种强大的开源关系型数据库管理系统，具备高度扩展性和标准 SQL
  支持。它提供了丰富的数据类型、并发处理、数据完整性、安全性及可编程性，适用于复杂应用和大规模数据管理。
</Callout>

本文将从框架角度介绍在任何一个平台中部署 DB 版 LobeChat 的流程和原理，让你知其然也知其所以然，最后可以根据自己的实际情况进行部署。

如你已经熟悉完整原理，可以查看各个平台的部署指南快速开始：

<PlatformCards urlPrefix={'server-database'} />

---

对于 LobeChat 的 DB 版，正常的部署流程都需要包含三个模块的配置：

1. 数据库配置；
2. 身份验证服务配置；
3. S3 存储服务配置。

## 配置数据库

在部署之前，请确保你已经准备好 Postgres 数据库实例，你可以选择以下任一实例：

- `A.` 使用 Vercel / Neon 等 Serverless Postgres 实例；
- `B.` 使用 Docker / Railway / Zeabur 等自部署 Postgres 实例，下统称 Node Postgres 实例；

<Callout>两者的配置方式在环境变量的取值上会略有一点区别，其他方面是一样的。</Callout>

同时，由于我们支持了文件对话 / 知识库对话的能力，因此我们需要为 Postgres 安装 `pgvector` 插件，该插件提供了向量搜索的能力，是 LobeChat 实现 RAG 的重要构件之一。

<Steps>
  ### `NEXT_PUBLIC_SERVICE_MODE`

  LobeChat 同时支持了客户端数据库和服务端数据库，因此我们提供了一个环境变量用于切换模式，这个变量为 `NEXT_PUBLIC_SERVICE_MODE`，该值默认为 `client`。

  针对服务端数据库部署场景，你需要将 `NEXT_PUBLIC_SERVICE_MODE` 设置为 `server`。

  <Callout type={'info'}>
    在官方的 `lobe-chat-database` Docker 镜像中，已经默认将该环境变量设为 `server`，因此如果你使用
    Docker 镜像部署，则无需再配置该环境变量。
  </Callout>

  <Callout type={'tip'}>
    由于 `NEXT_PUBLIC` 开头的环境变量是在前端代码中生效的，而因此无法通过容器运行时注入进行修改。 （`next.js`的参考文档 [Configuring: Environment Variables | Next.js (nextjs.org)](https://nextjs.org/docs/pages/building-your-application/configuring/environment-variables) ) 这也是为什么我们选择再打一个 DB 版镜像的原因。

    如果你需要在 Docker 部署中修改 `NEXT_PUBLIC` 前缀的变量，你必须自行构建镜像，在 build 时就把自己的 `NEXT_PUBLIC` 开头的环境变量打进去。
  </Callout>

  ### `DATABASE_URL`

  配置数据库，核心是添加 `DATABASE_URL` 环境变量，将你准备好的 Postgres 数据库连接 URL 填入其中。数据库连接 URL 的通常格式为 `postgres://username:password@host:port/database`。

  <Callout type={'info'}>
    如果希望连接数据库时启用 SSL
    ，请自行参考[文档](https://stackoverflow.com/questions/14021998/using-psql-to-connect-to-postgresql-in-ssl-mode)进行设置
  </Callout>

  ### `DATABASE_DRIVER`

  `DATABASE_DRIVER` 环境变量用于区分两种 Postgres 数据库实例，`DATABASE_DRIVER` 的取值为 `node` 或 `neon`。

  为提升部署便捷性，我们根据不同的平台特点设置了默认值：

  - 在 Vercel 平台下，`DATABASE_DRIVER` 默认为 `neon`；
  - 在我们提供的 Docker 镜像 `lobe-chat-database` 中，`DATABASE_DRIVER` 默认为 `node`。

  因此如果你采用了以下标准的部署方式，你无需手动配置 `DATABASE_DRIVER` 环境变量：

  - Vercel + Serverless Postgres
  - Docker 镜像 + Node Postgres

  ### `KEY_VAULTS_SECRET`

  考虑到用户会存储自己的 API Key 和 baseURL 等敏感信息到数据库中，因此我们需要一个密钥来加密这些信息，避免数据库被爆破 / 脱库时这些关键信息被泄露。 因此有了 `KEY_VAULTS_SECRET` 环境变量，用于加密用户存储的 apikey 等敏感信息。

  <Callout type={'info'}>
    你可以使用 `openssl rand -base64 32` 生成一个随机的 32 位字符串作为 `KEY_VAULTS_SECRET` 的值。
  </Callout>
</Steps>

## 配置身份验证服务

在服务端数据库模式下，我们要为不同用户区分身份，因此需要一个身份验证服务。开源社区中已经存在较多完善的身份验证解决方案。我们在实现过程中集成了两种不同的身份验证服务，用于满足不同场景的诉求，一种是 Clerk ，另外一种是 NextAuth。

### Clerk

[Clerk](https://clerk.com?utm_source=lobehub\&utm_medium=docs) 是一个身份验证 SaaS 服务，提供了开箱即用的身份验证能力，产品化程度很高，集成成本较低，体验很好。对于提供 SaaS 化产品的诉求来说，Clerk 是一个不错的选择。我们官方提供的 [LobeChat Cloud](https://lobechat.com)，就是使用了 Clerk 作为身份验证服务。

Clerk 的集成也相对简单，只需要配置 `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` 、 `CLERK_SECRET_KEY` 和 `CLERK_WEBHOOK_SECRET` 环境变量即可，这三个环境变量可以在 Clerk 控制台中获取。

<Callout type={'tip'}>
  在 Vercel 部署模式下，我们推荐使用 Clerk 作为身份验证服务，可以获得更好的用户体验。
</Callout>

但是这种身份验证依赖了 Clerk 官方的服务，因此在一些场景下可能会有一些限制：

- 比如在国内使用 Clerk 时，可能会受到网络环境的影响；
- 需要完全私有化部署的场景下，Clerk 并不适用；
- 必须依赖 `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`，对于公共 Docker 镜像无法开箱即用；

因此针对上述场景，我们也提供了 NextAuth 作为备选方案。

### NextAuth

NextAuth 是一个开源的身份验证库，支持多种身份验证提供商，包括 Auth0、Cognito、GitHub、Google、Facebook、Apple、Twitter 等。NextAuth 本身提供了一套完整的身份验证解决方案，包括用户注册、登录、密码找回、多种身份验证提供商的集成等。

关于 NextAuth 的配置，你可以参考 [身份验证](/zh/docs/self-hosting/advanced/authentication) 的文档获取更多信息。

<Callout type={'tip'}>
  在官方的 Docker 镜像 `lobe-chat-database` 中，我们推荐使用 NextAuth 作为身份验证服务。
</Callout>

## 配置 S3 存储服务

LobeChat 在 [很早以前](https://x.com/lobehub/status/1724289575672291782) 就支持了多模态的 AI 会话，其中涉及到图片上传给大模型的功能。在客户端数据库方案中，图片文件直接以二进制数据存储在浏览器 IndexedDB 数据库，但在服务端数据库中这个方案并不可行。因为在 Postgres 中直接存储文件类二进制数据会大大浪费宝贵的数据库存储空间，并拖慢计算性能。

这块最佳实践是使用文件存储服务（S3）来存储图片文件，同时 S3 也是文件上传 / 知识库功能所依赖的大容量静态文件存储方案。

<Callout type={'info'}>
  在本文档库中，S3 所指代的是指兼容 S3 存储方案，即支持 Amazon S3 API 的对象存储系统，常见例如
  Cloudflare R2 、阿里云 OSS，可以自部署的 minio 等均支持 S3 兼容 API。
</Callout>

关于 S3 的详细配置指南，请参阅 [S3 对象存储](/zh/docs/self-hosting/advanced/s3) 了解详情。

## 开始部署

以上就是关于服务端数据库版 LobeChat 的配置详解，你可以根据自己的实际情况进行配置，然后选择适合自己的部署平台开始部署：

<PlatformCards urlPrefix={'server-database'} />
