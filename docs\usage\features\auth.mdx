---
title: Support Multi-User Management - Identity Verification Solutions
description: >-
  Explore LobeChat's user authentication solutions with next-auth and Clerk for flexible and secure user management. Learn about features like user registration, session management, multi-factor authentication, and more.

tags:
  - Multi-User Management
  - Identity Verification
  - next-auth
  - Clerk
  - User Authentication
  - Session Management
  - Multi-Factor Authentication
  - User Management
---

# Support Multi-User Management

<Image alt={'Identity Verification System'} cover src={'https://github.com/user-attachments/assets/80bb232e-19d1-4f97-98d6-e291f3585e6d'} />

In modern applications, user management and identity verification are essential functions. To meet the diverse needs of different users, LobeChat provides two main user authentication and management solutions: `next-auth` and `Clerk`. Whether you are looking for simple user registration and login or need advanced multi-factor authentication and user management, LobeChat can flexibly accommodate your requirements.

## next-auth: Flexible and Powerful Identity Verification Library

LobeChat integrates `next-auth`, a flexible and powerful identity verification library that supports various authentication methods, including OAuth, email login, and credential login. With `next-auth`, you can easily achieve the following functions:

- **User Registration and Login**: Support various authentication methods to meet different user needs.
- **Session Management**: Efficiently manage user sessions to ensure security.
- **Social Login**: Support quick login via various social platforms.
- **Data Security**: Ensure the security and privacy of user data.

<Callout type={'warning'}>
  Due to workload constraints, integration of next-auth with a server-side database has not been
  implemented yet. If you need to use a server-side database, please use Clerk.
</Callout>

<Callout type={'info'}>
  For information on using Next-Auth, you can refer to [Authentication Services - Next
  Auth](/docs/self-hosting/advanced/authentication#next-auth).
</Callout>

## Clerk: Modern User Management Platform

For users requiring advanced user management features, LobeChat also supports [Clerk](https://clerk.com), a modern user management platform. Clerk offers richer functionality to help you achieve higher security and flexibility:

- **Multi-Factor Authentication (MFA)**: Provides higher security protection.
- **User Profile Management**: Conveniently manage user information and configurations.
- **Login Activity Monitoring**: Real-time monitoring of user login activities to ensure account security.
- **Scalability**: Supports complex user management requirements.

<Callout type={'info'}>
  For information on using Clerk, you can refer to [Authentication Services -
  Clerk](/docs/self-hosting/advanced/authentication#clerk) .
</Callout>

<Callout type={'tip'}>
  If you need to use Clerk in conjunction with a server-side database, you can refer to the
  "Configuring Authentication Services" section in [Deploying with a Server-Side
  Database](/docs/self-hosting/advanced/server-database).
</Callout>
