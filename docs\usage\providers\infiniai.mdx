---
title: Using Infini-AI in LobeChat
description: Learn how to configure and utilize Infini-AI's model services in LobeChat.
tags:
  - LobeChat
  - Infini-AI
  - API Key
  - LLM Deployment
---

# Using Infini-AI in LobeChat

[Infini-AI](https://cloud.infini-ai.com/) is a large model service platform optimized for multiple chip architectures, providing efficient and unified AGI infrastructure solutions.

This guide will help you quickly integrate Infini-AI's AI capabilities into LobeChat.

<Steps>
  ### Step 1: Obtain Infini-AI API Key

  - Log in to the [Large Model Service Platform](https://cloud.infini-ai.com/genstudio/model)
  - Select "API KEY Management" in the left navigation bar
  - In the newly opened page, click the "Create API KEY" button, enter a name, and click "Create"

  ### Step 2: Configure LobeChat Model Service

  - Open LobeChat and go to the "Settings" interface
  - Select "Infini-AI" in the "Language Model" module
  - Paste the API key you obtained
</Steps>
