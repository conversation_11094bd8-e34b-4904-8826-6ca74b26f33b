---
title: 在 LobeChat 中使用紫东太初 API Key
description: 学习如何在 LobeChat 中配置和使用紫东太初的API Key，以便开始对话和交互。
tags:
  - LobeChat
  - 太初
  - 紫东太初
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用紫东太初

<Image alt={'在 LobeChat 中使用太初'} cover src={'https://github.com/user-attachments/assets/9cb27b68-f2ac-4ff9-8f97-d96314b1af03'} />

本文将指导你如何在 LobeChat 中使用紫东太初：

<Steps>
  ### 步骤一：获取紫东太初 API 密钥

  - 创建一个[紫东太初](https://ai-maas.wair.ac.cn/)账户
  - 创建并获取 [API 密钥](https://ai-maas.wair.ac.cn/#/settlement/api/key)

  <Image alt={'创建 API Key'} inStep src={'https://github.com/user-attachments/assets/8d90ae64-cf8e-4d90-8a31-c18ab484740b'} />

  ### 步骤二：在 LobeChat 中配置紫东太初

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`紫东太初`的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/55028fe5-44db-49e2-93c5-5dabbd664f10'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个紫东太初的模型即可开始对话

  <Image alt={'选择太初模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/c44b6894-70cb-4876-b792-2e76e75ac542'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考紫东太初的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用紫东太初提供的模型进行对话了。
