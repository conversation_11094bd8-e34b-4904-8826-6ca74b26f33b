---
title: Deploy LobeChat with Database on RepoCloud
description: >-
  Learn how to deploy LobeChat on RepoCloud with ease, including database, authentication and S3 storage service.

tags:
  - Deploy LobeChat
  - RepoCloud Deployment
  - OpenAI API Key
  - Custom Domain Binding
---

# Deploying LobeChat Database Edition with RepoCloud

If you want to deploy LobeChat Database Edition on RepoCloud, you can follow the steps below:

## RepoCloud Deployment Process

<Steps>
  ### Prepare your OpenAI API Key

  Go to [OpenAI API Key](https://platform.openai.com/account/api-keys) to get your OpenAI API Key.

  ### One-click to deploy

  [![Deploy to RepoCloud](https://d16t0pc4846x52.cloudfront.net/deploy.svg)](https://repocloud.io/details/?app_id=248)

  ### Once deployed, you can start using it

  ### Bind a custom domain (optional)

  You can use the subdomain provided by RepoCloud, or choose to bind a custom domain. Currently, the domains provided by RepoCloud have not been contaminated, and most regions can connect directly.
</Steps>
