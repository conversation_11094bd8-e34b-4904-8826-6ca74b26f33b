---
title: Using Anthropic Claude API Key in LobeChat
description: >-
  Learn how to integrate Anthropic Claude API in LobeChat to enhance your AI assistant capabilities. Support Claude 3.5 sonnet / Claude 3 Opus / Claude 3 haiku

tags:
  - Anthropic Claude
  - API Key
  - AI assistant
  - Web UI
---

# Using Anthropic Claude in LobeChat

<Image alt={'Using Anthropic Claude in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/********/620b956b-dcb2-442a-8bb1-9aa22681dfa4'} />

The Anthropic Claude API is now available for everyone to use. This document will guide you on how to use [Anthropic Claude](https://www.anthropic.com/api) in LobeChat:

<Steps>
  ### Step 1: Obtain Anthropic Claude API Key

  - Create an [Anthropic Claude API](https://www.anthropic.com/api) account.
  - Get your [API key](https://console.anthropic.com/settings/keys).

  <Image alt={'Create API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/15e09e71-5899-4805-9c5e-1f7c57be04ae'} />

  <Callout type={'warning'}>
    The Claude API currently offers $5 of free credits, but it is only available in certain specific
    countries/regions. You can go to Dashboard > Claim to see if it is applicable to your
    country/region.
  </Callout>

  - Set up your billing for the API key to work on [https://console.anthropic.com/settings/plans](https://console.anthropic.com/settings/plans) (choose the "Build" plan so you can add credits and only pay for usage).

  <Image alt={'Set Up Your Billing'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/385f663f-cae2-4383-9bb0-52c45e5d7d7a'} />

  ### Step 2: Configure Anthropic Claude in LobeChat

  - Access the `Settings` interface in LobeChat.
  - Find the setting for `Anthropic Claude` under `AI Service Provider`.

  <Image alt={'Enter API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/ff9c3eb8-412b-4275-80be-177ae7b7acbc'} />

  - Enter the obtained API key.
  - Choose an Anthropic Claude model for your AI assistant to start the conversation.

  <Image alt={'Select Anthropic Claude Model and Start Conversation'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/6cdc5c0e-0508-44ed-a283-03f6b538ed8a'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider. Please refer to Anthropic Claude's
    relevant pricing policies.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by Anthropic Claude in LobeChat.
