---
title: 在 LobeChat 中 配置数据统计服务环境变量指南
description: 了解如何在 LobeChat 中配置各种数据统计服务的环境变量，包括Vercel Analytics、Google Analytics 等。
tags:
  - 数据统计
  - 环境变量
  - Vercel Analytics
  - Google Analytics
  - Posthog Analytics
  - Umami Analytics
---

# 数据统计

我们在 LobeChat 中集成了若干免费 / 开源的数据统计服务，用于了解用户的使用情况，以下是相关环境变量。

## Vercel Analytics

### `ENABLE_VERCEL_ANALYTICS`

- 类型：可选
- 描述：用于配置 Vercel Analytics 的环境变量，当设为 `1` 时开启 Vercel Analytics
- 默认值： `-`
- 示例：`1`

### `DEBUG_VERCEL_ANALYTICS`

- 类型：可选
- 描述：用于开启 Vercel Analytics 的调试模式
- 默认值： `-`
- 示例：`1`

## Google Analytics

### `GOOGLE_ANALYTICS_MEASUREMENT_ID`

- 类型：必选
- 描述：Google Analytics 的 Measurement ID，填写此项后将自动开启 Google Analytics
- 默认值： `-`
- 示例：`G-63LP2TV03T`

## Posthog Analytics

### `POSTHOG_KEY`

- 类型：必选
- 描述：设置 PostHog 项目 Key，设置此项后将自动开启 PostHog Analytics
- 默认值： `-`
- 示例：`phc_xxxxxxxx`

### `POSTHOG_HOST`

- 类型：可选
- 描述：设置 PostHog 服务的部署地址，默认为官方的 SaaS 地址
- 默认值：`https://app.posthog.com`
- 示例：`https://example.com`

### `POSTHOG_DEBUG`

- 类型：可选
- 描述：开启 PostHog 的调试模式
- 默认值： `-`
- 示例：`1`

## Umami Analytics

### `UMAMI_WEBSITE_ID`

- 类型：必选
- 描述：你的 Umami 的 Website ID，填写此项后将自动开启 Umami Analytics
- 默认值：`-`
- 示例：`E738D82A-EE9E-4806-A81F-0CA3CAE57F65`

### `UMAMI_SCRIPT_URL`

- 类型：可选
- 描述：Umami 脚本的网址，默认为 Umami Cloud 提供的脚本网址
- 默认值：`https://analytics.umami.is/script.js`
- 示例：`https://umami.your-site.com/script.js`
