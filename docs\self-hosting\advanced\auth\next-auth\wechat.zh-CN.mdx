---
title: 在 LobeChat 中配置微信身份验证服务
description: 学习如何在 LobeChat 中配置微信身份验证服务，包括创建新的微信网站应用、设置权限和环境变量。
tags:
  - 微信身份验证
  - 微信网站应用
  - 环境变量配置
  - 单点登录
  - LobeChat
---

# 配置微信身份验证服务

\## 微信配置流程

<Steps>
  ### 创建微信网站应用

  点击 [这里](https://open.weixin.qq.com/cgi-bin/index) 依次点击 “管理中心”、“网站应用”、“创建网站应用”

  按照管网提示要求填写信息并提交审核。

  创建成功后，点击 “应用详情”，可获知 AppID 和 AppSecret。

  ### 配置环境变量

  在部署 LobeChat 时，你需要配置以下环境变量：

  | 环境变量                      | 类型 | 描述                                                                                          |
  | ------------------------- | -- | ------------------------------------------------------------------------------------------- |
  | `NEXT_AUTH_SECRET`        | 必选 | 用于加密 Auth.js 会话令牌的密钥。您可以使用以下命令生成秘钥： `openssl rand -base64 32`                               |
  | `NEXT_AUTH_SSO_PROVIDERS` | 必选 | 选择 LoboChat 的单点登录提供商。使用 Github 请填写 `github`。                                                |
  | `WECHAT_CLIENT_ID`        | 必选 | 微信网站应用详情页的 客户端 ID                                                                           |
  | `WECHAT_CLIENT_SECRET`    | 必选 | 微信网站应用详情页的 客户端 Secret                                                                       |
  | `NEXTAUTH_URL`            | 必选 | 该 URL 用于指定 Auth.js 在执行 OAuth 验证时的回调地址，当默认生成的重定向地址发生不正确时才需要设置。`https://example.com/api/auth` |

  <Callout type={'tip'}>
    前往 [📘 环境变量](/zh/docs/self-hosting/environment-variables/auth#wechat) 可查阅相关变量详情。
  </Callout>
</Steps>

<Callout type={'info'}>部署成功后，用户将可以通过微信开放平台身份认证并使用 LobeChat。</Callout>
