---
title: Using <PERSON><PERSON><PERSON> in LobeChat
description: >-
  Learn how to configure and use SenseNova's API Key in LobeChat to start conversations and interactions.

tags:
  - LobeChat
  - SenseNova
  - API Key
  - Web UI
---

# Using <PERSON>Nova in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/420379cd-d8a4-4ab3-9a46-75dcc3d56920'} />

[SenseNova](https://platform.sensenova.cn/home) is a large model system introduced by SenseTime, aimed at promoting the rapid iteration and practical application of artificial intelligence (AI) technology.

This article will guide you on how to use SenseNova in LobeChat.

<Steps>
  ### Step 1: Obtain the API Key for SenseNova

  - Register and log in to the [SenseCore Development Platform](https://www.sensecore.cn/product/aistudio).
  - Locate the `SenseNova Large Model` in the product menu and activate the service.

  <Image alt={'Activate SenseNova Large Model'} inStep src={'https://github.com/user-attachments/assets/c6319e83-c4e7-48cf-9625-2edfc4aa77b3'} />

  - Go to the [AccessKey Management](https://console.sensecore.cn/iam/Security/access-key) page.
  - Create an access key.
  - Save the Access Key ID and secret in the pop-up window.

  <Image alt={'Save Access Key'} inStep src={'https://github.com/user-attachments/assets/f9f7ed26-e506-4c52-a118-e0bb5e0918db'} />

  <Callout type={'warning'}>
    Please keep the access key from the pop-up window secure, as it will only appear once. If you lose
    it, you will need to create a new access key.
  </Callout>

  ### Step 2: Configure SenseNova in LobeChat

  - Access the `Settings` interface on LobeChat.
  - Find the setting for `SenseNova` under `AI Service Provider`.

  <Image alt={'Enter Access Key'} inStep src={'https://github.com/user-attachments/assets/0c73c453-6ee3-4f90-bc5d-119c52c38fef'} />

  - Input the obtained `Access Key ID` and `Access Key Secret`.
  - Choose a SenseNova model for your AI assistant and start the conversation.

  <Image alt={'Choose SenseNova model and start conversation'} inStep src={'https://github.com/user-attachments/assets/aea782b1-27bd-4d9c-b521-c172c2095fe6'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to the relevant fee
    policy for SenseNova.
  </Callout>
</Steps>

You can now have conversations using the models provided by SenseNova in LobeChat.
