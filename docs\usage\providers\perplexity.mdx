---
title: Using Perplexity AI API Key in LobeChat
description: >-
  Learn how to integrate and use Perplexity AI in LobeChat to enhance your AI assistant's capabilities.

tags:
  - Perplexity AI
  - API key
  - Web UI
---

# Using Perplexity AI in LobeChat

<Image alt={'Using Perplexity AI in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/********/0c2c399f-2ed3-44b5-97c8-53e007e8c095'} />

The Perplexity AI API is now available for everyone to use. This document will guide you on how to use [Perplexity AI](https://www.perplexity.ai/) in LobeChat:

<Steps>
  ### Step 1: Obtain Perplexity AI API Key

  - Create a [Perplexity AI](https://www.perplexity.ai/) account
  - Obtain your [API key](https://www.perplexity.ai/settings/api)

  <Image alt={'Create API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/80e22593-dc0f-482c-99bf-69acdb62d952'} />

  ### Step 2: Configure Perplexity AI in LobeChat

  - Go to the `Settings` interface in LobeChat
  - Find the setting for `Perplexity AI` under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/51f8f8f6-5d8a-4cf0-a2e5-d96c69fe05b8'} />

  - Enter the API key you obtained
  - Choose a Perplexity AI model for your AI assistant to start the conversation

  <Image alt={'Select Perplexity AI model and start conversation'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/e6a429cb-96e1-4e85-9aa3-1334ffcad8c0'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider. Please refer to Perplexity AI's
    relevant pricing policies.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by Perplexity AI in LobeChat.
