---
title: Configuring Azure OpenAI for LobeChat
description: >-
  Learn how to configure Azure OpenAI for LobeChat, including interface configuration, and deployment settings.

tags:
  - Azure OpenAI
  - LobeChat
  - Configuration
  - Azure API
  - Model Service Provider
---

# Integrating with Azure OpenAI

LobeChat supports using [Azure OpenAI](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/concepts/models) as the model service provider for OpenAI. This article will explain how to configure Azure OpenAI.

## Configuring in the Interface

Click in the bottom left corner "Actions" - "Settings", then switch to the "Language Model" tab and enable the "Azure OpenAI" switch to start using Azure OpenAI.

<Image alt="Configuring in the Interface" src="https://github-production-user-asset-6210df.s3.amazonaws.com/********/*********-422a3714-627e-4bef-9fbc-141a2a8ca916.png" />

You can fill in the corresponding configuration items as needed:

- **API Key**: The API key you applied for on the Azure OpenAI account page, which can be found in the "Keys and Endpoints" section.
- **API Address**: Azure API address, which can be found in the "Keys and Endpoints" section when checking resources in the Azure portal.
- **Azure API Version**: The API version of Azure, following the format YYYY-MM-DD. Refer to the [latest version](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions).
- \*\* Model List\*\*: set your model list

After completing the configuration of the above fields, click "Check". If it prompts "Check passed", it means the configuration was successful.

## Configuration during Deployment

If you want the deployed version to be pre-configured with Azure OpenAI for end users to use directly, you need to configure the following environment variables during deployment:

| Environment Variable | Type     | Description                                                                                                                                                                                                                                                          | Default Value      | Example                                                                                                          |
| -------------------- | -------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------ | ---------------------------------------------------------------------------------------------------------------- |
| `AZURE_API_KEY`      | Required | This is the API key you obtained from the Azure OpenAI account page                                                                                                                                                                                                  | -                  | `c55168be3874490ef0565d9779ecd5a6`                                                                               |
| `AZURE_ENDPOINT`     | Required | Azure API address, can be found in the "Keys and Endpoints" section when checking resources in the Azure portal                                                                                                                                                      | -                  | `https://docs-test-001.openai.azure.com`                                                                         |
| `AZURE_API_VERSION`  | Required | Azure API version, following the format YYYY-MM-DD                                                                                                                                                                                                                   | 2023-08-01-preview | `-`, see [latest version](https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#chat-completions) |
| `AZURE_MODEL_LIST`   | Required | Used to control the model list, use `+` to add a model, use `-` to hide a model, use `id->deplymentName=displayName` to customize the display name of a model, separated by commas. Definition syntax rules see [Model List](/docs/self-hosting/advanced/model-list) | -                  | `gpt-35-turbo->my-deploy=GPT 3.5 Turbo` or `gpt-4-turbo->my-gpt4=GPT 4 Turbo<128000:vision:fc>`                  |
| `ACCESS_CODE`        | Optional | Add a password to access LobeChat. You can set a long password to prevent brute force attacks. When this value is separated by commas, it becomes an array of passwords                                                                                              | -                  | `awCT74` or `e3@09!` or `code1,code2,code3`                                                                      |
