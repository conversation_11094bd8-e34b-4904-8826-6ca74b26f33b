---
title: Using Jina AI API Key in LobeChat
description: Learn how to configure and use Jina AI models in LobeChat, obtain an API key, and start conversations.
tags:
  - LobeChat
  - Jina AI
  - API Key
  - Web UI
---

# Using Jina AI in LobeChat

<Image alt={'Using Jina AI in LobeChat'} cover src={'https://github.com/user-attachments/assets/840442b1-bf56-4a5f-9700-b3608b16a8a5'} />

[Jina AI](https://jina.ai/) is an open-source neural search company founded in 2020. It focuses on using deep learning technology to process multimodal data, providing efficient information retrieval solutions and supporting search for various data types such as text, images, and videos.

This document will guide you on how to use Jina AI in LobeChat:

<Steps>
  ### Step 1: Obtain a Jina AI API Key

  - Visit the [Jina AI official website](https://jina.ai/) and click the `API` button on the homepage.

  <Image alt={'Obtain a Jina AI API Key'} inStep src={'https://github.com/user-attachments/assets/5ea37821-4ea8-437c-a15e-3b182d10f19e'} />

  - Find the API Key generated for you in the `API Key` menu below.
  - Copy and save the generated API Key.

  <Callout type={'info'}>
    * Jina AI provides each user with 1M free API Tokens, and the API can be used without
      registration. \* If you need to manage the API Key or recharge the API, you need to register and
      log in to the [Jina AI Console](https://jina.ai/api-dashboard/).
  </Callout>

  ### Step 2: Configure Jina AI in LobeChat

  - Visit LobeChat's `Application Settings` interface.
  - Find the `Jina AI` setting under `AI Service Provider`.

  <Image alt={'Fill in Jina AI API Key'} inStep src={'https://github.com/user-attachments/assets/1077bee5-b379-4063-b7bd-23b98ec146e2'} />

  - Enable Jina AI and fill in the obtained API Key.
  - Select a Jina AI model for your assistant and start the conversation.

  <Image alt={'Select Jina AI Model'} inStep src={'https://github.com/user-attachments/assets/be06e348-8d4c-440c-b59f-b71120f21335'} />

  <Callout type={'warning'}>
    You may need to pay the API service provider during use. Please refer to Jina AI's relevant fee policy.
  </Callout>
</Steps>

Now you can use the models provided by Jina AI in LobeChat to have conversations.
