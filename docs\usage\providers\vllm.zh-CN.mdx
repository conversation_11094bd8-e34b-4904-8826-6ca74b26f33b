---
title: 在 LobeChat 中使用 vLLM API Key
description: 学习如何在 LobeChat 中配置和使用 vLLM 语言模型，获取 API 密钥并开始对话。
tags:
  - LobeChat
  - vLLM
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 vLLM

<Image alt={'在 LobeChat 中使用 vLLM'} cover src={'https://github.com/user-attachments/assets/1d77cca4-7363-4a46-9ad5-10604e111d7c'} />

[vLLM](https://github.com/vllm-project/vllm)是一个开源的本地大型语言模型（LLM）部署工具，允许用户在本地设备上高效运行 LLM 模型，并提供兼容 OpenAI API 的服务接口。

本文档将指导你如何在 LobeChat 中使用 vLLM:

<Steps>
  ### 步骤一：准备工作

  vLLM 对于硬件和软件环境均有一定要求，请务必根据以下要求进行配置：

  | 硬件需求      |                                                                         |
  | --------- | ----------------------------------------------------------------------- |
  | GPU       | - NVIDIA CUDA <br /> - AMD ROCm <br /> - Intel XPU                      |
  | CPU       | - Intel/AMD x86 <br /> - ARM AArch64 <br /> - Apple silicon             |
  | 其他 AI 加速器 | - Google TPU <br /> - Intel Gaudi <br /> - AWS Neuron <br /> - OpenVINO |

  | 软件需求                                    |
  | --------------------------------------- |
  | - OS: Linux <br /> - Python: 3.9 – 3.12 |

  ### 步骤二：安装 vLLM

  如果你正在使用 NVIDIA GPU，你可以直接使用`pip`安装 vLLM。但这里建议使用`uv`，它一个非常快速的 Python 环境管理器，来创建和管理 Python 环境。请按照[文档](https://docs.astral.sh/uv/#getting-started)安装 uv。安装 uv 后，你可以使用以下命令创建一个新的 Python 环境并安装 vLLM：

  ```shell
  uv venv myenv --python 3.12 --seed
  source myenv/bin/activate
  uv pip install vllm
  ```

  另一种方法是使用`uv run`与`--with [dependency]`选项，这允许你运行`vllm serve`等命令而无需创建环境：

  ```shell
  uv run --with vllm vllm --help
  ```

  你也可以使用 [conda](https://docs.conda.io/projects/conda/en/latest/user-guide/getting-started.html) 来创建和管理你的 Python 环境。

  ```shell
  conda create -n myenv python=3.12 -y
  conda activate myenv
  pip install vllm
  ```

  <Callout type={'note'}>
    对于非 CUDA
    平台，请参考[官方文档](https://docs.vllm.ai/en/latest/getting_started/installation/index.html#installation-index)了解如何安装
    vLLM
  </Callout>

  ### 步骤三：启动本地服务

  vLLM 可以部署为一个 OpenAI API 协议兼容的服务器。默认情况下，它将在 `http://localhost:8000` 启动服务器。你可以使用 `--host` 和 `--port` 参数指定地址。服务器目前一次仅运行一个模型。

  以下命令将启动一个 vLLM 服务器并运行 `Qwen2.5-1.5B-Instruct` 模型：

  ```shell
  vllm serve Qwen/Qwen2.5-1.5B-Instruct
  ```

  你可以通过传递参数 `--api-key` 或环境变量 `VLLM_API_KEY` 来启用服务器检查头部中的 API 密钥。如不设置，则无需 API Key 即可访问。

  <Callout type={'note'}>
    更详细的 vLLM 服务器配置，请参考[官方文档](https://docs.vllm.ai/en/latest/)
  </Callout>

  ### 步骤四：在 LobeChat 中配置 vLLM

  - 访问 LobeChat 的 `应用设置`界面
  - 在 `AI 服务商` 下找到 `vLLM` 的设置项

  <Image alt={'填写 vLLM API 密钥'} inStep src={'https://github.com/user-attachments/assets/669c68bf-3f85-4a6f-bb08-d0d7fb7f7417'} />

  - 打开 vLLM 服务商并填入 API 服务地址以及 API Key

  <Callout type={'warning'}>
    * 如果你的 vLLM 没有配置 API Key，请将 API Key 留空 \* 如果你的 vLLM
      运行在本地，请确保打开`客户端请求模式`
  </Callout>

  - 在下方的模型列表中添加你运行的模型
  - 为你的助手选择一个 vLLM 运行的模型即可开始对话

    <Image alt={'选择 vLLM 模型'} inStep src={'https://github.com/user-attachments/assets/fcdfb9c5-819a-488f-b28d-0857fe861219'} />
</Steps>

至此你已经可以在 LobeChat 中使用 vLLM 提供的模型进行对话了。
