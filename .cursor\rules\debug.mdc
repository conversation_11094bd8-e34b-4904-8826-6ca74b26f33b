---
description: Debug 调试指南
globs: 
alwaysApply: false
---
# Debug 调试指南

## 💡 调试流程概览

当遇到问题时，请按照以下优先级进行处理：

1. **快速判断** - 对于熟悉的错误，直接提供解决方案
2. **信息收集** - 使用工具搜索相关代码和配置
3. **网络搜索** - 查找现有解决方案
4. **定位调试** - 添加日志进行问题定位
5. **临时方案** - 如果找不到根本解决方案，提供临时解决方案
6. **解决实施** - 提供可维护的最终解决方案

## 🔍 错误信息分析

### 错误来源识别

错误信息可能来自：

- **Terminal 输出** - 构建、运行时错误
- **浏览器控制台** - 前端 JavaScript 错误
- **开发工具** - ESLint、TypeScript、测试框架等
- **服务器日志** - API、数据库连接等后端错误
- **截图或文本** - 用户直接提供的错误信息

## 🛠️ 信息收集工具

### 代码搜索工具

使用以下工具收集相关信息，并根据场景选择最合适的工具：

- **`codebase_search` (语义搜索)**
    - **何时使用**: 当你不确定具体的代码实现，想要寻找相关概念、功能或逻辑时。
    - **示例**: `查询"文件上传"功能的实现`
- **`grep_search` (精确/正则搜索)**
    - **何时使用**: 当你知道要查找的确切字符串、函数名、变量名或一个特定的模式时。
    - **示例**: `查找所有使用了 'useState' 的地方`
- **`file_search` (文件搜索)**
    - **何时使用**: 当你知道文件名的一部分，需要快速定位文件时。
    - **示例**: `查找 'Button.tsx' 组件`
- **`read_file` (内容读取)**
    - **何时使用**: 在定位到具体文件后，用于查看其完整内容和上下文。
- **`web_search` (网络搜索)**
    - **何时使用**: 当错误信息可能与第三方库、API 或常见问题相关时，用于获取外部信息。

### 环境与依赖检查

- **检查 `package.json`**: 查看 `scripts` 了解项目如何运行、构建和测试。查看 `dependencies` 和 `devDependencies` 确认库版本，版本冲突有时是问题的根源。
- **运行测试**: 使用 `ni vitest` 运行单元测试和集成测试，这可以快速定位功能回归或组件错误。

### 项目特定搜索目标

针对 lobe-chat 项目，重点关注：

- **配置文件**: [package.json](mdc:package.json), [next.config.mjs](mdc:next.config.mjs)
- **核心功能**: `src/features/` 下的相关模块
- **状态管理**: `src/store/` 下的 Zustand stores
- **数据库**: `src/database/` 和 `src/migrations/`
- **类型定义**: `src/types/` 下的类型文件
- **服务层**: `src/services/` 下的 API 服务
- **启动流程**: [apps/desktop/src/main/core/App.ts](mdc:apps/desktop/src/main/core/App.ts) - 了解应用启动流程

## 🌐 网络搜索策略

### 搜索顺序优先级

1. **和问题相关的项目的 github issue**

2. **技术社区**
    - Stack Overflow
    - GitHub Discussions
    - Reddit

3. **官方文档**
    - 使用 `mcp_context7_resolve-library-id` 和 `mcp_context7_get-library-docs` 工具
    - 查阅官方文档网站

### 搜索关键词策略

- **错误信息**: 完整的错误消息
- **技术栈**: "Next.js 15" + "error message"
- **上下文**: 添加功能相关的关键词

## 🔧 问题定位与结构化思考

如果问题比较复杂，我们要按照先定位问题，再解决问题的大方向进行。

### 结构化思考工具

对于复杂或多步骤的调试任务，使用 `mcp_sequential-thinking_sequentialthinking` 工具来结构化思考过程。这有助于：

- **分解问题**: 将大问题拆解成可管理的小步骤。
- **清晰追踪**: 记录每一步的发现和决策，避免遗漏。
- **自我修正**: 在过程中评估和调整调试路径。

### 日志调试

在问题产生的路径上添加日志，可以简单使用 `console.log` 或者参考 [debug-usage.mdc](mdc:.cursor/rules/debug-usage.mdc) 使用 `debug` 模块。添加完日志后，请求我运行相关的代码并提供关键输出和错误信息。

### 引导式交互调试

虽然我无法直接操作浏览器开发者工具，但我可以引导你进行交互式调试：

1.  **设置断点**: 我会告诉你可以在哪些关键代码行设置断点。
2.  **检查变量**: 我会请你在断点处检查特定变量的值或 `props`/`state`。
3.  **分析调用栈**: 我会请你提供调用栈信息，以帮助理解代码执行流程。

## 💡 临时解决方案策略

当无法找到根本解决方案时，提供临时解决方案：

### 临时方案准则

- **快速修复** - 优先让功能可用
- **最小修改** - 减少对现有代码的影响
- **清晰标记** - 明确标注这是临时方案
- **后续计划** - 说明后续如何找到更好的解决方案

### 临时方案模板

```markdown
## 临时解决方案 ⚠️

**问题**: [简要描述问题]

**临时修复**:
[具体的临时修复步骤]

**风险说明**:

- [可能的副作用或限制]
- [需要注意的事项]

**后续计划**:

- [ ] 深入调研根本原因
- [ ] 寻找更优雅的解决方案
- [ ] 监控是否有其他影响
```

## ✅ 解决方案准则

### 方案质量标准

提供的解决方案应该：

- **✅ 低侵入性** - 最小化对现有代码的修改
- **✅ 可维护性** - 易于理解和后续维护
- **✅ 类型安全** - 符合 TypeScript 规范
- **✅ 最佳实践** - 遵循项目的编码规范
- **✅ 测试友好** - 便于编写和运行测试
- **❌ 避免长期 Hack** - 临时方案可以 hack，但要明确标注

### 解决方案模板

```markdown
## 问题原因

[简要说明问题产生的根本原因]

## 解决方案

[详细的解决步骤]

## 代码修改

[具体的代码变更]

## 验证方法

[如何验证问题已解决]

## 预防措施

[如何避免类似问题再次发生]
```

## 🔄 迭代调试流程

如果初次解决方案无效：

1.  **重新收集信息** - 基于新的错误信息搜索
2.  **深入代码分析** - 查看更多相关代码文件
3.  **运行相关测试** - 编写或运行一个失败的测试来稳定复现问题。
4.  **扩大搜索范围** - 搜索更广泛的相关问题
5.  **请求更多日志** - 添加更详细的调试信息
6.  **提供临时方案** - 如果根本解决方案复杂，先提供临时修复
7.  **分解问题** - 将复杂问题拆解为更小的子问题
