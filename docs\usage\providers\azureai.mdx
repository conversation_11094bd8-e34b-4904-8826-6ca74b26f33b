---
title: Using Azure AI API Key in LobeChat
description: Learn how to configure and use Azure AI models in LobeChat, get the API key, and start a conversation.
tags:
  - LobeChat
  - Azure AI
  - API Key
  - Web UI
---

# Using Azure AI in LobeChat

<Image alt={'Using Azure AI in LobeChat'} cover src={'https://github.com/user-attachments/assets/81d0349a-44fe-4dfc-bbc4-8e9a1e09567d'} />

[Azure AI](https://azure.microsoft.com) is an open artificial intelligence technology platform based on the Microsoft Azure cloud platform. It provides various AI functionalities, including natural language processing, machine learning, and computer vision, helping businesses easily develop and deploy AI applications.

This document will guide you on how to integrate Azure AI models into LobeChat:

<Steps>
  ### Step 1: Deploy Azure AI Project and Model

  - First, visit [Azure AI Foundry](https://ai.azure.com/) and complete the registration and login process.
  - After logging in, select `Browse models` on the homepage.

  <Image alt={'Accessing Azure AI Foundry'} inStep src={'https://github.com/user-attachments/assets/1c6a3e42-8e24-4148-b2c3-0bfe60a8cf77'} />

  - Choose the model you want in the model marketplace.
  - Enter the model details and click the `Deploy` button.

  <Image alt={'Browsing Models'} inStep src={'https://github.com/user-attachments/assets/3ed3226c-3d4c-49ef-b2c0-8953dac8a92e'} />

  - In the pop-up dialog, create a new project.

  <Image alt={'Creating a New Project'} inStep src={'https://github.com/user-attachments/assets/199b862a-5de4-4a54-83b2-f4dbf69be902'} />

  <Callout type={'note'}>
    For detailed configuration of Azure AI Foundry, please refer to the [official
    documentation](https://learn.microsoft.com/azure/ai-foundry/model-inference/).
  </Callout>

  ### Step 2: Obtain the Model's API Key and Endpoint

  - In the details of the deployed model, you can find the Endpoint and API Key information.
  - Copy and save the obtained information.

  <Image alt={'Obtaining API Key'} inStep src={'https://github.com/user-attachments/assets/30c33426-412d-4dec-b096-317fe5880e79'} />

  ### Step 3: Configure Azure AI in LobeChat

  - Visit the `App Settings` and `AI Service Provider` interface in LobeChat.
  - Find the settings for `Azure AI` in the list of providers.

  <Image alt={'Entering Azure AI API Key'} inStep src={'https://github.com/user-attachments/assets/eb41f77f-ccdd-4a48-a8a2-7badac868c03'} />

  - Enable the Azure AI service provider and fill in the obtained Endpoint and API Key.

  <Callout type={'warning'}>
    For the Endpoint, you only need to fill in the first part:
    `https://xxxxxx.services.ai.azure.com/models`.
  </Callout>

  - Choose an Azure AI model for your assistant and start the conversation.

  <Image alt={'Selecting Azure AI Model'} inStep src={'https://github.com/user-attachments/assets/a1ba8ec0-e259-4da4-8980-0cf82ca5f52b'} />

  <Callout type={'warning'}>
    You may need to pay the API service provider for usage. Please refer to Azure AI's relevant pricing policies.
  </Callout>
</Steps>

Now you can use the models provided by Azure AI in LobeChat for conversations.
