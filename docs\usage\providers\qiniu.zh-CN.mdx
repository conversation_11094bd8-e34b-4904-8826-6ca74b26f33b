---
title: 在 LobeChat 中使用七牛云大模型 API Key
description: 学习如何在 LobeChat 中配置和使用七牛云的大模型，提供强大的自然语言理解和生成能力。
tags:
  - API key
  - Web UI
  - 七牛
  - 七牛云
  - 七牛智能
  - Qiniu
  - DeepSeek
---

# 在 LobeChat 中使用七牛云大模型

<Image alt={'在 LobeChat 中使用七牛云大模型'} cover src={'https://github.com/user-attachments/assets/3ad2655e-dd20-4534-bf6d-080b3677df86'} />

[七牛云](https://www.qiniu.com)作为老牌云服务厂商，提供高性价比稳定的实时、批量 AI 推理服务，简单易用。

本文档将指导你如何在 LobeChat 中使用七牛云大模型:

<Steps>
  ### 步骤一：[获取 AI 大模型 API 密钥](https://developer.qiniu.com/aitokenapi/12884/how-to-get-api-key)

  - 方法一：使用控制台获取

    1. [注册七牛账号](https://s.qiniu.com/umqq6n?ref=developer.qiniu.com\&s_path=%2Faitokenapi%2F12884%2Fhow-to-get-api-key)
    2. [前往控制台获取 API Key](https://portal.qiniu.com/ai-inference/api-key)
       <Image alt={'获取 API Key'} inStep src={'https://static.sufy.com/lobehub/438758098-119239c1-8552-420a-9906-de2eab739fc6.png'} />

  - 方法二：使用小程序获取
    1. 打开七牛小程序
    2. 快速登录账号
    3. 点击【我的】底部导航栏
    4. 点击【我的控制台】
    5. 进入【AI 推理】
    6. 查看和复制你的 API 密钥

  ### 步骤二：在 LobeChat 中配置七牛云大模型服务

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`七牛云`的设置项

  <Image alt={'填写 API 密钥'} inStep src={'https://static.sufy.com/lobehub/439049319-6ae44f36-bf48-492a-a6aa-7be72f4a29d8.png'} />

  - 打开七牛云并填入获得的 API 密钥
  - 为你的 AI 助手选择一个七牛云的大模型即可开始对话

  <Image alt={'选择七牛云大模型并开始对话'} inStep src={'https://static.sufy.com/lobehub/439048945-c608eb9e-6ee1-4611-9df7-2075e95d069b.png'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考[七牛云的相关费用政策](https://developer.qiniu.com/aitokenapi/12898/ai-token-api-pricing)。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用七牛云提供的大模型进行对话了。
