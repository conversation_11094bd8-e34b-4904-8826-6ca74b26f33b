---
title: 在 LobeChat 中使用本地通义千问 Qwen 模型
description: 通过 LobeChat 与 Ollama 的集成，轻松在本地部署的通义千问 Qwen 模型中进行对话。学习如何安装和选择 Qwen 模型。
tags:
  - 通义千问
  - Qwen模型
  - LobeChat集成
  - Ollama
  - 本地部署
---

# 使用本地通义千问 Qwen 模型

<Image alt={'在 LobeChat 中使用 Qwen'} cover src={'https://github.com/lobehub/lobe-chat/assets/17870709/b4a01219-e7b1-48a0-888c-f0271b18e3a6'} />

[通义千问](https://github.com/QwenLM/Qwen1.5) 是阿里云开源的一款大语言模型（LLM），官方定义是一个不断进化的 AI 大模型，并通过更多的训练集内容达到更精准的中文识别能力。

<Video src="https://github.com/lobehub/lobe-chat/assets/28616219/31e5f625-8dc4-4a5f-a5fd-d28d0457782d" />

现在，通过 LobeChat 与 [Ollama](https://ollama.com/) 的集成，你可以轻松地在 LobeChat 中使用 通义千问。

本文档将指导你如何在 LobeChat 中使用通义千问本地部署版：

<Steps>
  ### 本地安装 Ollama

  首先，你需要安装 Ollama，安装过程请查阅 [Ollama 使用文件](/zh/docs/usage/providers/ollama)。

  ### 用 Ollama 拉取 Qwen 模型到本地

  在安装完成 Ollama 后，你可以通过以下命令安装 Qwen 模型，以 14b 模型为例：

  ```bash
  ollama pull qwen:14b
  ```

  <Image alt={'使用 Ollama 拉取 Qwen 模型'} height={473} inStep src={'https://github.com/lobehub/lobe-chat/assets/1845053/fe34fdfe-c2e4-4d6a-84d7-4ebc61b2516a'} />

  ### 选择 Qwen 模型

  在会话页面中，选择模型面板打开，然后选择 Qwen 模型。

  <Image alt={'模型选择面板中选择 Qwen 模型'} height={430} inStep src={'https://github.com/lobehub/lobe-chat/assets/28616219/e0608cca-f62f-414a-bc55-28a61ba21f14'} />

  <Callout type={'info'}>
    如果你没有在模型选择面板中看到 Ollama 服务商，请查阅 [与 Ollama
    集成](/zh/docs/self-hosting/examples/ollama) 了解如何在 LobeChat 中开启 Ollama 服务商。
  </Callout>
</Steps>

接下来，你就可以使用 LobeChat 与本地 Qwen 模型对话了。
