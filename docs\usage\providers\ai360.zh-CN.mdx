---
title: 在 LobeChat 中使用360智脑
description: 学习如何在 LobeChat 中配置和使用360智脑的API Key，以便开始对话和交互。
tags:
  - LobeChat
  - 360智脑
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 360 智脑

<Image cover src={'https://github.com/user-attachments/assets/e617def1-ce50-4acc-974b-12f5ed592a0e'} />

[360 智脑](https://ai.360.com/)是 360 公司自主研发的认知型通用大模型，旨在为企业和开发者提供强大的自然语言处理能力。该模型已升级至 4.0 版本，能够支持多种应用场景，包括对话服务、图片生成、向量数据库服务等。

本文将指导你如何在 LobeChat 中使用 360 智脑。

<Steps>
  ### 步骤一：获得 360 智脑的 API Key

  - 注册并登录 [360 智脑 API 开放平台](https://ai.360.com/platform/keys)
  - 点击左侧 `API Keys` 菜单
  - 创建一个 API 密钥并复制

  <Image alt={'创建API密钥'} inStep src={'https://github.com/user-attachments/assets/72da7af1-e180-4759-84a5-a6f6ca28392e'} />

  ### 步骤二：在 LobeChat 中配置 360 智脑

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `360` 的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/a53deb11-2c14-441a-8a5c-a0f3a74e2a63'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 360 智脑的模型即可开始对话

  <Image alt={'选择360模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/452d0b48-5ff7-4f42-a46e-68a62b87632b'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 360 智脑的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 360 智脑提供的模型进行对话了。
