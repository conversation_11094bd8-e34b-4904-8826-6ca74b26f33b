---
title: 在 RepoCloud 上部署 LobeChat 数据库版
description: 学习如何在 RepoCloud 上部署 LobeChat 应用，包括准备 OpenAI API Key、点击部署按钮、绑定自定义域名等操作。
tags:
  - RepoCloud
  - LobeChat
  - 部署流程
  - OpenAI API Key
  - 自定义域名
---

# 在 RepoCloud 上部署 LobeChat 数据库版

如果您想在 RepoCloud 上部署 LobeChat 数据库版，可以按照以下步骤进行操作：

## RepoCloud 部署流程

<Steps>
  ### 准备您的 OpenAI API 密钥

  请访问 [OpenAI API 密钥](https://platform.openai.com/account/api-keys) 获取您的 OpenAI API 密钥。

  ### 一键部署

  [![部署到 RepoCloud](https://d16t0pc4846x52.cloudfront.net/deploy.svg)](https://repocloud.io/details/?app_id=248)

  ### 部署完成后，您可以开始使用

  ### 绑定自定义域名（可选）

  您可以使用 RepoCloud 提供的子域名，或选择绑定自定义域名。目前，RepoCloud 提供的域名尚未被污染，大多数地区可以直接连接。
</Steps>
