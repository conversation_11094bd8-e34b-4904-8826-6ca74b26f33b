---
title: Using OpenAI API Key in LobeChat
description: >-
  Learn how to integrate OpenAI API Key in LobeChat. Support GPT-4o / GPT-4-turbo / GPT-4-vision

tags:
  - OpenAI
  - ChatGPT
  - GPT-4
  - GPT-4o
  - API Key
  - Web UI
---

# Using OpenAI in LobeChat

<Image alt={'Using OpenAI in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/********/c9e5eafc-ca22-496b-a88d-cc0ae53bf720'} />

This document will guide you on how to use [OpenAI](https://openai.com/) in LobeChat:

<Steps>
  ### Step 1: Obtain OpenAI API Key

  - Register for an [OpenAI account](https://platform.openai.com/signup). You will need to register using an international phone number and a non-mainland email address.

  - After registration, go to the [API Keys](https://platform.openai.com/api-keys) page and click on `Create new secret key` to generate a new API Key.

  - Open the creation window

  <Image
    alt={'Open the creation window'}
    inStep
    src={
'https://github-production-user-asset-6210df.s3.amazonaws.com/********/*********-ff2193dd-f125-4e58-82e8-91bc376c0d68.png'
}
  />

  - Create API Key

  <Image
    alt={'Create API Key'}
    inStep
    src={
'https://github-production-user-asset-6210df.s3.amazonaws.com/********/*********-803bacf0-4471-4171-ae79-0eab08d621d1.png'
}
  />

  - Retrieve API Key

  <Image
    alt={'Retrieve API Key'}
    inStep
    src={
'https://github-production-user-asset-6210df.s3.amazonaws.com/********/*********-f2745f2b-f083-4ba8-bc78-9b558e0002de.png'
}
  />

  <Callout type={'warning'}>
    After registering, you generally have a free credit of $5, but it is only valid for three months.
  </Callout>

  ### Step 2: Configure OpenAI in LobeChat

  - Visit the `Settings` page in LobeChat
  - Find the setting for `OpenAI` under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/3f31bc33-509f-4ad2-ba81-280c2a6ec5fa'} />

  - Enter the obtained API Key
  - Choose an OpenAI model for your AI assistant to start the conversation

  <Image alt={'Select OpenAI model and start conversation'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/ff7ebacf-27f0-42d7-810b-00314499a084'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider. Please refer to OpenAI's relevant
    pricing policies.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by OpenAI in LobeChat.
