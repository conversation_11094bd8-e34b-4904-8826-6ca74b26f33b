---
title: Efficiently Organize Your AI Assistants with <PERSON>beC<PERSON>
description: >-
  Learn how to use LobeChat's grouping, search, and pinning functions to efficiently organize and locate your AI assistants.

tags:
  - <PERSON>beChat
  - AI assistants
  - assistant organization
  - grouping
  - search function
  - pinning function
---

# Assistant Organization Guide

<Image alt={'Assistant Organization'} cover src={'https://github.com/user-attachments/assets/5e04b71a-4c25-4f3d-ae7d-9237ffcb37be'} />

LobeChat provides a rich variety of AI assistant resources. Users can easily add various assistants through the assistant market, offering a wide range of application scenarios for AI applications.

When you have added a large number of assistants, finding a specific assistant in the list may become challenging. LobeChat provides `search`, `grouping`, and `pinning` functions to help you better organize assistants and improve efficiency in locating them.

## Assistant Grouping

Firstly, LobeChat's AI assistants support organization through grouping. You can categorize assistants of the same type together and easily search for the required assistants by collapsing and expanding groups.

### Assistant Settings

<Image alt={'Assistant Grouping'} src={'https://github.com/user-attachments/assets/97a9d713-f4c1-4170-a707-d2060eeab35c'} />

- In the menu of an individual assistant, selecting the `Move to Group` option can quickly categorize the assistant into the specified group.
- If you don't find the group you want, you can choose `Add Group` to quickly create a new group.

### Group Settings

<Image alt={'Group Menu'} src={'https://github.com/user-attachments/assets/b9ca37db-0f0b-449f-b3b7-f147bec3a735'} />

- In the group menu, you can quickly create a new assistant under that group.
- Clicking the `Group Management` button allows you to `rename`, `delete`, `sort`, and perform other operations on all groups.

## Assistant Search

<Image alt={'Assistant Search'} src={'https://github.com/user-attachments/assets/816ad463-e1ef-478b-9930-c40948247288'} />

- At the top of the assistant list, you can use the assistant search function to easily locate the assistant you need using keywords.

## Assistant Pinning

<Image alt={'Assistant Pinning'} src={'https://github.com/user-attachments/assets/d574b08e-ab93-4723-9d1b-9e9be2bff7ee'} />

- In the assistant menu, you can use the `Pin` function to pin the assistant to the top.
- After pinning an assistant, a pinned area will appear at the top of the assistant list, displaying all pinned assistants.
- For pinned assistants, you can choose `Unpin` to remove them from the pinned area.
