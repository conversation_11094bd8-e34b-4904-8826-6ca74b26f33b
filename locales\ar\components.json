{"DragUpload": {"dragDesc": "اسحب الملفات هنا، يدعم تحميل عدة صور.", "dragFileDesc": "اسحب الصور والملفات هنا، يدعم تحميل عدة صور وملفات.", "dragFileTitle": "تحميل الملفات", "dragTitle": "تحميل الصور"}, "FileManager": {"actions": {"addToKnowledgeBase": "إضافة إلى قاعدة المعرفة", "addToOtherKnowledgeBase": "إضافة إلى قاعدة معرفة أخرى", "batchChunking": "تقسيم دفعي", "chunking": "تقسيم", "chunkingTooltip": "قم بتقسيم الملف إلى عدة كتل نصية وتحويلها إلى متجهات، يمكن استخدامها في البحث الدلالي والمحادثة حول الملفات", "chunkingUnsupported": "هذا الملف لا يدعم تقسيم الأجزاء", "confirmDelete": "سيتم حذف هذا الملف، ولن يمكن استعادته بعد الحذف، يرجى تأكيد العملية", "confirmDeleteMultiFiles": "سيتم حذف {{count}} ملفًا محددًا، ولن يمكن استعادته بعد الحذف، يرجى تأكيد العملية", "confirmRemoveFromKnowledgeBase": "سيتم إزالة {{count}} ملفًا محددًا من قاعدة المعرفة، لا يزال بإمكانك رؤية الملفات في جميع الملفات، يرجى تأكيد العملية", "copyUrl": "نسخ الرابط", "copyUrlSuccess": "تم نسخ عنوان الملف بنجاح", "createChunkingTask": "جارٍ التحضير...", "deleteSuccess": "تم حذف الم<PERSON><PERSON> بنجاح", "downloading": "جارٍ تحميل الملف...", "removeFromKnowledgeBase": "إزالة من قاعدة المعرفة", "removeFromKnowledgeBaseSuccess": "تمت إزالة الملف بنجاح"}, "bottom": "لقد وصلت إلى النهاية", "config": {"showFilesInKnowledgeBase": "عرض المحتوى في قاعدة المعرفة"}, "emptyStatus": {"actions": {"file": "رفع ملف", "folder": "ر<PERSON><PERSON> مجلد", "knowledgeBase": "إنشاء قاعدة معرفة جديدة"}, "or": "أو", "title": "قم بسحب الملف أو المجلد هنا"}, "title": {"createdAt": "تاريخ الإنشاء", "size": "الحجم", "title": "مل<PERSON>"}, "total": {"fileCount": "إجمالي {{count}} عنصر", "selectedCount": "تم تحديد {{count}} عنصر"}}, "FileParsingStatus": {"chunks": {"embeddingStatus": {"empty": "لم يتم تحويل كتل النص بالكامل إلى متجهات، مما سيؤدي إلى عدم توفر وظيفة البحث الدلالي، لتحسين جودة البحث، يرجى تحويل كتل النص إلى متجهات", "error": "فشل في تحويل البيانات إلى متجهات", "errorResult": "فشل في تحويل البيانات إلى متجهات، يرجى التحقق والمحاولة مرة أخرى. سبب الفشل:", "processing": "يتم تحويل كتل النص إلى متجهات، يرجى الانتظار", "success": "تم تحويل جميع كتل النص الحالية إلى متجهات"}, "embeddings": "تحويل إلى متجهات", "status": {"error": "فشل في التقسيم", "errorResult": "فشل في التقسيم، يرجى التحقق والمحاولة مرة أخرى. سبب الفشل:", "processing": "جارٍ التقسيم", "processingTip": "الخادم يقوم بتقسيم كتل النص، إغلاق الصفحة لا يؤثر على تقدم التقسيم"}}}, "GoBack": {"back": "عودة"}, "ImageUpload": {"actions": {"changeImage": "انقر لتغيير الصورة", "dropMultipleFiles": "لا يدعم تحميل ملفات متعددة في آن واحد، سيتم استخدام الملف الأول فقط"}, "placeholder": {"primary": "إضافة صورة", "secondary": "انقر أو اسحب للإرفاق"}}, "KeyValueEditor": {"addButton": "إضافة صف جديد", "deleteTooltip": "<PERSON><PERSON><PERSON>", "duplicateKeyError": "يجب أن يكون اسم المفتاح فريدًا", "keyPlaceholder": "المفتاح", "valuePlaceholder": "القيمة"}, "MaxTokenSlider": {"unlimited": "<PERSON>ير محدود"}, "ModelSelect": {"featureTag": {"custom": "نموذج مخصص، الإعداد الافتراضي يدعم الاستدعاء الوظيفي والتعرف البصري، يرجى التحقق من قدرة النموذج على القيام بذلك بناءً على الحالة الفعلية", "file": "يدعم هذا النموذج قراءة وتعرف الملفات المرفوعة", "functionCall": "يدعم هذا النموذج استدعاء الوظائف", "imageOutput": "يدعم هذا النموذج إنشاء الصور", "reasoning": "يدعم هذا النموذج التفكير العميق", "search": "يدعم هذا النموذج البحث عبر الإنترنت", "tokens": "يدعم هذا النموذج حتى {{tokens}} رمزًا في جلسة واحدة", "vision": "يدعم هذا النموذج التعرف البصري"}, "removed": "هذا النموذج لم يعد متوفر في القائمة، سيتم إزالته تلقائيًا إذا تم إلغاء تحديده"}, "ModelSwitchPanel": {"emptyModel": "لا توجد نماذج ممكن تمكينها، يرجى الانتقال إلى الإعدادات لتمكينها", "emptyProvider": "لا توجد مزودات مفعلة، يرجى الذهاب إلى الإعدادات لتفعيلها", "goToSettings": "اذه<PERSON> إلى الإعدادات", "provider": "مزود", "title": "نموذج"}, "MultiImagesUpload": {"actions": {"uploadMore": "انقر أو اسحب لإضافة المزيد"}, "modal": {"complete": "اكتمل", "newFileIndicator": "جديد", "selectImageToPreview": "يرجى اختيار صورة للمعاينة", "title": "إدارة الصور ({{count}})", "upload": "تحميل الصور"}, "placeholder": {"primary": "انقر أو اسحب لتحميل الصور", "secondary": "يدعم اختيار عدة صور"}, "progress": {"uploadingWithCount": "تم تحميل {{completed}} من أصل {{total}}"}}, "OllamaSetupGuide": {"action": {"close": "إغلاق الإشعار", "start": "تم التثبيت والتشغيل، ابدأ المحادثة"}, "cors": {"description": "بس<PERSON><PERSON> قيود أمان المتصفح، تحتاج إلى تكوين CORS لـ Ollama لاستخدامه بشكل صحيح.", "linux": {"env": "أضف `Environment` تحت قسم [Service]، وأضف متغير البيئة OLLAMA_ORIGINS:", "reboot": "أعد تحميل systemd وأعد تشغيل Ollama", "systemd": "استخدم systemd لتحرير خدمة ollama:"}, "macos": "يرجى فتح تطبيق «الطرفية» ولصق الأوامر التالية ثم الضغط على Enter للتنفيذ", "reboot": "ير<PERSON>ى إعادة تشغيل خدمة Ollama بعد الانتهاء من التنفيذ", "title": "تكوين Ollama للسماح بالوصول عبر النطاقات المتعددة", "windows": "على نظام Windows، انقر على «لوحة التحكم»، ثم انتقل إلى تحرير متغيرات البيئة للنظام. أنشئ متغير بيئة جديد باسم «OLLAMA_ORIGINS» لقائمة المستخدم الخاصة بك، وقيمته هي *، ثم انقر على «موافق/تطبيق» لحفظ التغييرات."}, "install": {"description": "يرجى التأكد من أنك قد قمت بتشغيل Ollama، إذا لم تقم بتنزيل Ollama، يرجى زيارة الموقع الرسمي <1>للتنزيل</1>", "docker": "إذا كنت تفضل استخدام Docker، فإن Ollama يوفر أيضًا صورة Docker رسمية، يمكنك سحبها باستخدام الأمر التالي:", "linux": {"command": "قم بتثبيت باستخدام الأمر التالي:", "manual": "أو يمكنك الرجوع إلى <1>دليل التثبيت اليدوي لنظام Linux</1> للتثبيت بنفسك."}, "title": "تثبيت وتشغيل تطبيق Ollama محليًا", "windowsTab": "Windows (نسخة المعاينة)"}}, "Thinking": {"thinking": "في حالة تفكير عميق...", "thought": "لقد فكرت بعمق (استغرق الأمر {{duration}} ثانية)", "thoughtWithDuration": "لقد فكرت بعمق"}}