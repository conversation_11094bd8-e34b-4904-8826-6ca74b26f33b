---
title: Automatic Synchronization with LobeChat Desktop
description: Configure LobeChat Desktop for a synchronized experience
tags:
  - LobeChat
  - Desktop Synchronization
  - Self-Hosted Instance
  - OIDC Configuration
---

# Automatic Synchronization with LobeChat Desktop

LobeChat Desktop provides users with an enhanced experience while also expanding the capabilities of LobeChat, allowing users to utilize LobeChat in offline environments. This document will guide you on how to configure LobeChat Desktop and connect it to your self-hosted instance.

## System Requirements

LobeChat Desktop is built using Electron and supports the following operating systems:

- Windows
- macOS
- Linux (certain distributions)

## Desktop Version Information

The version release of LobeChat Desktop follows this logic:

- **Beta Version**: All merges to the main branch that are automatically released will be created as Beta versions of LobeChat Desktop.
- **Stable Version**: The development team will periodically release stable versions through manual tagging.

If you wish to access the latest features, you can use the Beta version; if you prioritize stability, please use the Stable version.

## Connecting to a Self-Hosted Instance

LobeChat Desktop can connect to your self-hosted LobeChat instance, allowing you to use your self-hosted configuration on the desktop.

### Preparations

Before connecting the desktop to your self-hosted instance, ensure that your self-hosted instance is correctly configured with the OIDC-related environment variables.

<Callout>
  Please ensure that your self-hosted instance version is 1.83.6 or higher to support desktop
  connection features.
</Callout>

#### OIDC Environment Variable Configuration

You need to add the following two environment variables, `ENABLE_OIDC` and `OIDC_JWKS_KEY`, to your self-hosted instance. You can click the button below to generate them with one click:

<OIDCJWKs />

Add the generated JWK key to your environment variables.

If you are deploying LobeChat using one-click deployment methods (such as Vercel, Railway, etc.), you need to:

1. Add the above environment variables to the environment variable configuration of your deployment platform.
2. For Vercel deployments, ensure that the `APP_URL` environment variable is set to your domain URL, which is crucial for correctly handling authentication callbacks.

### Connection Steps

1. Open LobeChat Desktop.
2. Click the Wi-Fi icon in the upper right corner.
3. Enter the address of your self-hosted instance (e.g., `https://your-lobechat-instance.com`).
4. Click the "Connect" button.
5. You will be redirected to the login page; after completing the authorization, you can use your self-hosted configuration.

## Frequently Asked Questions

### 502 Bad Gateway

**Issue:** After clicking the authorization button, a 502 Bad Gateway error occurs when redirected to the `/oidc/consent` path.

**Solution:**

- Confirm that you have correctly set the `ENABLE_OIDC=1` and `OIDC_JWKS_KEY` environment variables.
- Ensure that `OIDC_JWKS_KEY` is in valid JSON format without extra single quotes.
- Check your server logs for specific error messages.

If you are using Nginx as a reverse proxy, the issue may be due to oversized request headers. You can try adding the following settings to your Nginx configuration:

```nginx
proxy_buffer_size   16k;
proxy_buffers       8 16k;
proxy_busy_buffers_size 32k;
```

### invalid\_redirect\_uri Error

**Issue:** Encountering the error "redirect\_uri did not match any of the client's registered redirect\_uris" during the authorization process.

**Solution:**

Check if the `APP_URL` environment variable matches the URL you are accessing.

### macOS Startup Hangs or Crashes

**Issue:** On macOS, the application hangs for a long time or crashes after startup.

**Solution:**

- Check if you have downloaded the correct macOS architecture version:
  - For Apple Silicon (M3/M4, etc.) Macs, use the `arm64` version.
  - For Intel Macs, use the `x64` version.
- Example file names:
  - Apple Silicon: `LobeHub-Beta-1.83.6-arm64.dmg` or `LobeHub-Beta-1.83.6-arm64-mac.zip`
  - Intel: `LobeHub-Beta-1.83.6-x64.dmg` or `LobeHub-Beta-1.83.6-mac.zip`

### Image Loading Issues

**Issue:** When using a Docker-deployed server, the desktop client cannot load avatars or other image resources after connecting.

**Solution:**

- If you are using Minio for storage, ensure that cross-origin requests are allowed in the Minio configuration.
- Add appropriate CORS settings in your Minio service.
