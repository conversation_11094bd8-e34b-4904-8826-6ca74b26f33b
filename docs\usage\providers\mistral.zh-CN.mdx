---
title: 在 LobeChat 中使用 Mistral AI API Key
description: 学习如何在 LobeChat 中配置并使用 Mistral AI，包括获取 API 密钥和选择适合的 AI 模型进行对话。
tags:
  - Web UI
  - Mistral AI
  - API Key
---

# 在 LobeChat 中使用 Mistral AI

<Image alt={'在 LobeChat 中使用 Mistral AI'} cover src={'https://github.com/lobehub/lobe-chat/assets/17870709/a3f9f63a-48f8-4567-b960-7f3636c0d4ed'} />

Mistral AI API 现在可供所有人使用，本文档将指导你如何在 LobeChat 中使用 [Mistral AI](https://mistral.ai/):

<Steps>
  ### 步骤一：获取 Mistral AI API 密钥

  - 创建一个 [Mistral AI](https://mistral.ai/) 帐户
  - 获取您的 [API 密钥](https://console.mistral.ai/user/api-keys/)

  <Image alt={'创建 API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/79faa59a-dfc0-4365-a679-5fc12c12bc70'} />

  ### 步骤二：在 LobeChat 中配置 Mistral AI

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`Mistral AI`的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/ba8e688a-e0c1-4567-9013-94205f83fc60'} />

  <Callout type={'warning'}>
    如果您使用的是 mistral.ai，则您的帐户必须具有有效的订阅才能使 API 密钥正常工作。新创建的 API
    密钥需要 2-3 分钟才能开始工作。如果单击 “测试” 按钮但失败，请在 2-3 分钟后重试。
  </Callout>

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 Mistral AI 的模型即可开始对话

  <Image alt={'选择 Mistral AI 模型并开始对话'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/82cf4f5c-be5c-4126-a475-3a03468a9c39'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Mistral AI 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Mistral AI 提供的模型进行对话了。
