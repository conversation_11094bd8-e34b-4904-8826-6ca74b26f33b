---
title: Deploy LobeChat on Alibaba Cloud
description: >-
  Learn how to deploy the LobeChat application on Alibaba Cloud, including preparing the large model API Key, clicking the deploy button, and other operations.

tags:
  - Alibaba Cloud
  - LobeChat
  - Alibaba Cloud Compute Nest
  - API Key
---

# Deploy LobeChat with Alibaba Cloud

If you want to deploy LobeChat on Alibaba Cloud, you can follow the steps below:

## Alibaba Cloud Deployment Process

<Steps>
  ### Prepare your API Key

  Go to [OpenAI API Key](https://platform.openai.com/account/api-keys) to get your OpenAI API Key. Or go to [Tongyi Qianwen API Key](https://bailian.console.aliyun.com/?apiKey=1#/api-key) to get your API Key.

  ### One-click to deploy

  [![][deploy-button-image]][deploy-link]

  ### Once deployed, you can start using it
</Steps>

[deploy-button-image]: https://service-info-public.oss-cn-hangzhou.aliyuncs.com/computenest-en.svg
[deploy-link]: https://computenest.console.aliyun.com/service/instance/create/default?type=user&ServiceName=LobeChat%E7%A4%BE%E5%8C%BA%E7%89%88
