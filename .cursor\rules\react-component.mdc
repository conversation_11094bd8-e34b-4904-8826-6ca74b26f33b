---
description: 
globs: *.tsx
alwaysApply: false
---
# react component 编写指南

- 如果要写复杂样式的话用 antd-style ，简单的话可以用 style 属性直接写内联样式
- 如果需要 flex 布局或者居中布局应该使用 react-layout-kit 的 Flexbox 和 Center 组件
- 选择组件时优先顺序应该是 src/components > 安装的组件 package > lobe-ui > antd

## antd-style token system

### 访问 token system 的两种方式

#### 使用 antd-style 的 useTheme hook

```tsx
import { useTheme } from 'antd-style';

const MyComponent = () => {
  const theme = useTheme();
  
  return (
    <div style={{ 
      color: theme.colorPrimary,
      backgroundColor: theme.colorBgContainer,
      padding: theme.padding,
      borderRadius: theme.borderRadius
    }}>
      使用主题 token 的组件
    </div>
  );
}
```

#### 使用 antd-style 的 createStyles

```tsx
const useStyles = createStyles(({ css, token }) => {
  return {
    container: css`
      background-color: ${token.colorBgContainer};
      border-radius: ${token.borderRadius}px;
      padding: ${token.padding}px;
      color: ${token.colorText};
    `,
    title: css`
      font-size: ${token.fontSizeLG}px;
      font-weight: ${token.fontWeightStrong};
      margin-bottom: ${token.marginSM}px;
    `,
    content: css`
      font-size: ${token.fontSize}px;
      line-height: ${token.lineHeight};
    `
  };
});

const Card: FC<CardProps> = ({ title, content }) => {
  const { styles } = useStyles();
  
  return (
    <Flexbox className={styles.container}>
      <div className={styles.title}>{title}</div>
      <div className={styles.content}>{content}</div>
    </Flexbox>
  );
};
```

### 一些你经常会忘记使用的 token

请注意使用下面的 token 而不是 css 字面值。可以访问 https://ant.design/docs/react/customize-theme-cn 了解所有 token

- 动画类
    - token.motionDurationMid
    - token.motionEaseInOut
- 包围盒属性
    - token.paddingSM
    - token.marginLG


## Lobe UI 包含的组件

- 不知道 @lobehub/ui 的组件怎么用，有哪些属性，就自己搜下这个项目其它地方怎么用的，不要瞎猜，大部分组件都是在 antd 的基础上扩展了属性
- 具体用法不懂可以联网搜索，例如 ActionIcon 就爬取 https://ui.lobehub.com/components/action-icon

- General
  ActionIcon
  ActionIconGroup
  Block
  Button
  Icon
- Data Display
  Avatar
  Collapse
  FileTypeIcon
  FluentEmoji
  GuideCard
  Highlighter
  Hotkey
  Image
  List
  Markdown
  MaterialFileTypeIcon
  Mermaid
  Segmented
  Snippet
  SortableList
  Tag
  Tooltip
  Video
- Data Entry
  AutoComplete
  CodeEditor
  ColorSwatches
  CopyButton
  DatePicker
  EditableText
  EmojiPicker
  Form
  FormModal
  HotkeyInput
  ImageSelect
  Input
  SearchBar
  Select
  SliderWithInput
  ThemeSwitch
- Feedback
  Alert
  Drawer
  Modal
- Layout
  DraggablePanel
  Footer
  Grid
  Header
  Layout
  MaskShadow
  ScrollShadow
- Navigation
  Burger
  Dropdown
  Menu
  SideNav
  Tabs
  Toc
- Theme
  ConfigProvider
  FontLoader
  ThemeProvider