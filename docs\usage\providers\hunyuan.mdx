---
title: Using Tencent Hunyuan in LobeChat
description: >-
  Learn how to integrate and utilize Tencent Hunyuan's language model APIs in LobeChat.

tags:
  - LobeChat
  - Tencent Hunyuan
  - API Key
  - Web UI
---

# Using Tencent Hunyuan in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/467bb431-ca0d-4bb4-ac17-e5e2b764a770'} />

[Tencent Hunyuan](https://hunyuan.tencent.com/) is a large model launched by Tencent, designed to provide users with intelligent assistant services. It utilizes natural language processing technology to help users solve problems, offer suggestions, and generate content. By conversing with the model, users can quickly access the information they need, thereby enhancing work efficiency.

This article will guide you on how to use Tencent Hunyuan in LobeChat.

<Steps>
  ### Step 1: Obtain the Tencent Hunyuan API Key

  - Register and log in to the [Tencent Cloud Console](https://console.cloud.tencent.com/hunyuan/api-key)
  - Navigate to `Hunyuan Large Model` and click on `API KEY Management`
  - Create an API key

  <Image alt={'Create API Key'} inStep src={'https://github.com/user-attachments/assets/5f344314-ecbc-41e6-9120-520a2d5352ff'} />

  - Click `View`, and copy the API key from the pop-up panel, ensuring you save it securely

  <Image alt={'Save Key'} inStep src={'https://github.com/user-attachments/assets/659b5ac1-82f1-43bd-9d4b-a98491e05794'} />

  ### Step 2: Configure Tencent Hunyuan in LobeChat

  - Go to the `Settings` page in LobeChat
  - Find the `Tencent Hunyuan` settings under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/796c94af-9bad-4e3c-b1c7-dbb17c215c56'} />

  - Enter the API key you obtained
  - Select a Tencent Hunyuan model for your AI assistant to start the conversation

  <Image alt={'Select Tencent Hunyuan Model and Start Conversation'} inStep src={'https://github.com/user-attachments/assets/e3f44bc8-2fa5-441d-8934-943481472450'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to Tencent Hunyuan's
    relevant pricing policy.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by Tencent Hunyuan in LobeChat.
