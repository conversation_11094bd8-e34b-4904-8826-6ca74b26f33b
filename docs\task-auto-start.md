# 定时任务自动启动配置

本文档说明如何配置和使用定时任务的自动启动功能。

## 功能概述

系统提供了自动启动定时任务的功能，主要包括：

1. **用户免费额度重置任务** - 每天凌晨自动重置所有用户的 `coinsFree` 字段为 200

## 启动方式

### 1. 自动启动（推荐）

系统会在应用启动时自动启动定时任务，支持以下环境：

- **开发环境** - 通过 `src/instrumentation.ts` 在应用启动时自动执行
- **生产环境** - 通过 `src/instrumentation.ts` 在应用启动时自动执行
- **Docker 环境** - 通过 `src/instrumentation.ts` 在应用启动时自动执行

### 2. 手动启动

如果需要手动启动定时任务，可以调用以下 API：

```bash
GET /api/task/start?token=your_secret_token
```

**参数说明：**
- `token`: 认证令牌，默认为 `fmai_task`，可通过环境变量 `TASK_API_SECRET` 自定义

**响应示例：**
```json
{
  "message": "定时任务已成功启动",
  "success": true,
  "startedBy": "api"
}
```

## 环境变量配置

### AUTO_START_CRON_JOBS

控制是否自动启动定时任务。

- **默认值**: `true`（启用自动启动）
- **可选值**:
  - `true` - 启用自动启动
  - `false` - 禁用自动启动

**示例：**
```bash
# 启用自动启动（默认）
AUTO_START_CRON_JOBS=true

# 禁用自动启动
AUTO_START_CRON_JOBS=false
```

### TASK_API_SECRET

设置手动启动 API 的认证令牌。

- **默认值**: `fmai_task`
- **建议**: 在生产环境中设置为复杂的随机字符串

**示例：**
```bash
TASK_API_SECRET=your_secure_random_token_here
```

## 任务详情

### 用户免费额度重置任务

- **执行时间**: 每天凌晨 00:00
- **功能**: 重置所有用户的 `coinsFree` 字段为 200
- **Cron 表达式**: `0 0 * * *`

## 日志监控

系统会输出详细的日志信息，便于监控任务状态：

```
【应用启动】开始初始化服务器端功能
【定时任务】自动启动模式已启用
【定时任务】[重置用户免费额度]启用
【定时任务】[重置用户免费额度]启用成功
【应用启动】服务器端功能初始化完成
```

## 故障排除

### 1. 定时任务未启动

**检查步骤：**
1. 确认环境变量 `AUTO_START_CRON_JOBS` 未设置为 `false`
2. 查看应用启动日志，确认是否有相关错误信息
3. 检查数据库连接是否正常

### 2. 手动启动失败

**可能原因：**
1. 认证令牌不正确
2. 定时任务已经在运行中
3. 数据库连接异常

**解决方法：**
1. 检查 `TASK_API_SECRET` 环境变量设置
2. 查看 API 响应信息确认具体错误
3. 检查应用日志获取详细错误信息

### 3. Docker 环境问题

如果在 Docker 环境中遇到问题：

1. 检查容器启动日志，确认 `instrumentation.ts` 是否正常执行
2. 验证应用是否在服务器端运行（`NEXT_RUNTIME === 'nodejs'`）
3. 确认数据库连接是否正常建立

## 开发说明

### 添加新的定时任务

1. 在 `src/task/user.ts` 中添加新的任务函数
2. 在 `autoStartCronJobs` 函数中调用新任务
3. 在 `/api/task/start` 路由中添加相应的启动逻辑

### 修改任务执行时间

修改 `schedule()` 函数中的 Cron 表达式：

```typescript
// 每天凌晨执行
schedule('0 0 * * *', async () => {
  // 任务逻辑
});

// 每小时执行
schedule('0 * * * *', async () => {
  // 任务逻辑
});
```

## 注意事项

1. **数据库连接**: 确保在启动定时任务前数据库连接已建立
2. **重复启动**: 系统会自动防止重复启动同一个定时任务
3. **错误处理**: 任务执行失败不会影响应用的正常运行
4. **性能影响**: 定时任务在后台运行，对应用性能影响很小
