# Internationalization Implementation Guide

Welcome to the LobeChat Internationalization Implementation Guide. This document will guide you through understanding the internationalization mechanism of LobeChat, including file structure and how to add new languages. LobeChat uses `i18next` and `lobe-i18n` as the internationalization solution, aiming to provide users with seamless multilingual support.

## Internationalization Overview

Internationalization (i18n for short) is the process of enabling an application to adapt to different languages and regions. In LobeChat, we support multiple languages and achieve dynamic language switching and content localization through the `i18next` library. Our goal is to provide a localized experience for global users.

## File Structure

In the LobeChat project, internationalization-related files are organized as follows:

- `src/locales/default`: Contains translation files for the default development language (Chinese), which we use as Chinese.
- `locales`: Contains folders for all supported languages, with each language folder containing the respective translation files generated by lobe-i18n.

In the directory structure of `src/locales`, the `default` folder contains the original translation files (Chinese), while each other language folder contains JSON translation files for the respective language. The files in each language folder correspond to the TypeScript files in the `default` folder, ensuring consistency in the structure of translation files across languages.

```bash
src/locales
├── create.ts
├── default
│ ├── chat.ts
│ ├── common.ts
│ ├── error.ts
│ ├── index.ts
│ ├── market.ts
│ ├── migration.ts
│ ├── plugin.ts
│ ├── setting.ts
│ ├── tool.ts
│ └── welcome.ts
└── resources.ts
```

The file structure generated by lobe-i18n is as follows:

```bash
locales
├── ar
│   ├── chat.json
│   ├── common.json
│   ├── error.json
│   └── ... (other translation files)
├── de-DE
│   ├── chat.json
│   ├── common.json
│   ├── error.json
│   └── ... (other translation files)
├── en-US
├── ... (other language directories)
├── zh-CN
└── zh-TW
```

## Core Implementation Logic

The internationalization core implementation logic of LobeChat is as follows:

- Initialize and configure using the `i18next` library.
- Automatically detect the user's language preference using `i18next-browser-languagedetector`.
- Dynamically load translation resources using `i18next-resources-to-backend`.
- Set the direction of the HTML document (LTR or RTL) based on the user's language preference.

Here is a simplified pseudo code example to illustrate the core implementation logic of internationalization in LobeChat:

```ts
import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import resourcesToBackend from 'i18next-resources-to-backend';
import { isRtlLang } from 'rtl-detect';

// Create i18n instance and configure
const createI18nInstance = (lang) => {
  const i18nInstance = i18n
    .use(LanguageDetector) // Use language detection
    .use(
      resourcesToBackend((language, namespace) => {
        // Dynamically load translation resources for the corresponding language
        return import(`path/to/locales/${language}/${namespace}.json`);
      }),
    );

  // Listen for language change events and dynamically set document direction
  i18nInstance.on('languageChanged', (language) => {
    const direction = isRtlLang(language) ? 'rtl' : 'ltr';
    document.documentElement.dir = direction; // Set HTML document direction
  });

  // Initialize i18n instance
  i18nInstance.init({
    // Relevant configurations
  });

  return i18nInstance;
};
```

In this example, we demonstrate how to use `i18next` and related plugins to initialize internationalization settings. We dynamically import translation resources and respond to language change events to adjust the text direction of the page. This process provides LobeChat with flexible multilingual support capabilities.

## Adding Support for New Languages

We have already supported a variety of languages globally through the following efforts:

- [✨ feat: adding Arabic Language Support #1049](https://github.com/lobehub/lobe-chat/pull/1049)
- [🌐 style: Add Vietnamese files and add the vi-VN option in the General Settings #860](https://github.com/lobehub/lobe-chat/pull/860)
- [🌐 style: support it-IT nl-NL and pl-PL locales #759](https://github.com/lobehub/lobe-chat/pull/759)
- [🌐 feat(locale): Add fr-FR (#637) #645](https://github.com/lobehub/lobe-chat/pull/645)
- [🌐 Add russian localy #137](https://github.com/lobehub/lobe-chat/pull/137)

To add support for new languages, please refer to the detailed steps in the [New Locale Addition Guide](add-new-locale).

## Resources and Further Reading

- [i18next Official Documentation](https://www.i18next.com/)
- [lobe-i18n Tool Description](https://github.com/lobehub/lobe-cli-toolbox/tree/master/packages/lobe-i18n)

By following this guide, you can better understand and participate in the internationalization work of LobeChat, providing a seamless multilingual experience for global users.
