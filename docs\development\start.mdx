# Technical Development Getting Started Guide

Welcome to the LobeChat Technical Development Getting Started Guide. LobeChat is an AI conversation application built on the Next.js framework, incorporating a range of technology stacks to achieve diverse functionalities and features. This guide will detail the main technical components of LobeChat and how to configure and use these technologies in your development environment.

## Basic Technology Stack

The core technology stack of LobeChat is as follows:

- **Framework**: We chose [Next.js](https://nextjs.org/), a powerful React framework that provides key features such as server-side rendering, routing framework, and Router Handler.
- **Component Library**: We use [Ant Design (antd)](https://ant.design/) as the basic component library, along with [lobe-ui](https://github.com/lobehub/lobe-ui) as our business component library.
- **State Management**: We selected [zustand](https://github.com/pmndrs/zustand), a lightweight and easy-to-use state management library.
- **Network Requests**: We use [swr](https://swr.vercel.app/), a React Hooks library for data fetching.
- **Routing**: For routing management, we directly use the solution provided by [Next.js](https://nextjs.org/).
- **Internationalization**: We use [i18next](https://www.i18next.com/) to support multiple languages in the application.
- **Styling**: We use [antd-style](https://github.com/ant-design/antd-style), a CSS-in-JS library that complements Ant Design.
- **Unit Testing**: We use [vitest](https://github.com/vitest-dev/vitest) for unit testing.

## Folder Directory Structure

The folder directory structure of LobeChat is as follows:

```bash
src
├── app        # Code related to the main logic and state management of the application
├── components # Reusable UI components
├── config     # Application configuration files, including client and server environment variables
├── const      # Used to define constants, such as action types, route names, etc.
├── features   # Business-related feature modules, such as Agent settings, plugin development pop-ups, etc.
├── hooks      # Custom utility Hooks reusable across the application
├── layout     # Application layout components, such as navigation bars, sidebars, etc.
├── locales    # Language files for internationalization
├── services   # Encapsulated backend service interfaces, such as HTTP requests
├── store      # Zustand store for state management
├── types      # TypeScript type definition files
└── utils      # General utility functions
```

For a detailed introduction to the directory structure, see: [Folder Directory Structure](/docs/development/basic/folder-structure)

## Local Development Environment Setup

This section outlines setting up the development environment and local development. Before starting, please ensure that Node.js, Git, and your chosen package manager (Bun or PNPM) are installed in your local environment.

We recommend using WebStorm as your integrated development environment (IDE).

1. **Get the code**: Clone the LobeChat code repository locally:

```bash
git clone https://github.com/lobehub/lobe-chat.git
```

2. **Install dependencies**: Enter the project directory and install the required dependencies:

```bash
cd lobe-chat
# If you use Bun
bun install
# If you use PNPM
pnpm install
```

3. **Run and debug**: Start the local development server and begin your development journey:

```bash
# Start the development server with Bun
bun run dev
# Visit http://localhost:3010 to view the application
```

> \[!IMPORTANT]\
> If you encounter the error "Could not find 'stylelint-config-recommended'" when installing dependencies with `npm`, please reinstall the dependencies using `pnpm` or `bun`.

Now, you should be able to see the welcome page of LobeChat in your browser. For a detailed environment setup guide, please refer to [Development Environment Setup Guide](/docs/development/basic/setup-development).

## Code Style and Contribution Guide

In the LobeChat project, we place great emphasis on the quality and consistency of the code. For this reason, we have established a series of code style standards and contribution processes to ensure that every developer can smoothly participate in the project. Here are the code style and contribution guidelines you need to follow as a developer.

- **Code Style**: We use `@lobehub/lint` to unify the code style, including ESLint, Prettier, remarklint, and stylelint configurations. Please adhere to our code standards to maintain code consistency and readability.
- **Contribution Process**: We use gitmoji and semantic release for code submission and release processes. Please use gitmoji to annotate your commit messages and ensure compliance with the semantic release standards so that our automation systems can correctly handle version control and releases.

All contributions will undergo code review. Maintainers may suggest modifications or requirements. Please respond actively to review comments and make timely adjustments. We look forward to your participation and contribution.

For detailed code style and contribution guidelines, please refer to [Code Style and Contribution Guide](/docs/development/basic/contributing-guidelines).

## Internationalization Implementation Guide

LobeChat uses `i18next` and `lobe-i18n` to implement multilingual support, ensuring a global user experience.

Internationalization files are located in `src/locales`, containing the default language (Chinese). We generate other language JSON files automatically through `lobe-i18n`.

If you want to add a new language, follow specific steps detailed in [New Language Addition Guide](/docs/development/internationalization/add-new-locale). We encourage you to participate in our internationalization efforts to provide better services to global users.

For a detailed guide on internationalization implementation, please refer to [Internationalization Implementation Guide](/docs/development/internationalization/internationalization-implementation).

## Appendix: Resources and References

To support developers in better understanding and using the technology stack of LobeChat, we provide a comprehensive list of resources and references — [LobeChat Resources and References](/docs/development/basic/resources) - Visit our maintained list of resources, including tutorials, articles, and other useful links.

We encourage developers to utilize these resources to deepen their learning and enhance their skills, join community discussions through [LobeChat GitHub Discussions](https://github.com/lobehub/lobe-chat/discussions) or [Discord](https://discord.com/invite/AYFPHvv2jT), ask questions, or share your experiences.

If you have any questions or need further assistance, please do not hesitate to contact us through the above channels.
