---
title: ModelScope 提供商配置
description: 学习如何在 LobeChat 中配置和使用 ModelScope 提供商
tags:
  - ModelScope
---

# ModelScope 提供商配置

ModelScope（魔塔社区）是阿里巴巴的开源模型社区，提供各种 AI 模型的访问服务。本指南将帮助您在 LobeChat 中设置 ModelScope 提供商。

## 前置条件

在使用 ModelScope API 之前，您需要：

1. **创建 ModelScope 账户**

   - 访问 [ModelScope](https://www.modelscope.cn/)
   - 注册账户

2. **绑定阿里云账户**

   - **重要**：ModelScope API 需要绑定阿里云账户
   - 访问您的 [ModelScope 访问令牌页面](https://www.modelscope.cn/my/myaccesstoken)
   - 按照说明绑定您的阿里云账户
   - 此步骤是 API 访问的必要条件

3. **获取 API 令牌**
   - 绑定阿里云账户后，生成 API 令牌
   - 复制令牌以在 LobeChat 中使用

## 配置

### 环境变量

在您的 `.env` 文件中添加以下环境变量：

```bash
# 启用 ModelScope 提供商
ENABLED_MODELSCOPE=1

# ModelScope API 密钥（必需）
MODELSCOPE_API_KEY=your_modelscope_api_token

# 可选：自定义模型列表（逗号分隔）
MODELSCOPE_MODEL_LIST=deepseek-ai/DeepSeek-V3-0324,Qwen/Qwen3-235B-A22B

# 可选：代理 URL（如需要）
MODELSCOPE_PROXY_URL=https://your-proxy-url
```

### Docker 配置

如果使用 Docker，请在您的 `docker-compose.yml` 中添加 ModelScope 环境变量：

```yaml
environment:
  - ENABLED_MODELSCOPE=1
  - MODELSCOPE_API_KEY=your_modelscope_api_token
  - MODELSCOPE_MODEL_LIST=deepseek-ai/DeepSeek-V3-0324,Qwen/Qwen3-235B-A22B
```

## 可用模型

ModelScope 提供各种模型的访问，包括：

- **DeepSeek 模型**：DeepSeek-V3、DeepSeek-R1 系列
- **Qwen 模型**：Qwen3 系列、Qwen2.5 系列
- **Llama 模型**：Meta-Llama-3 系列
- **其他模型**：各种开源模型

## 故障排除

### 常见问题

1. **"请先绑定阿里云账户后使用" 错误**

   - 这意味着您还没有将阿里云账户绑定到 ModelScope
   - 访问 [ModelScope 访问令牌页面](https://www.modelscope.cn/my/myaccesstoken)
   - 完成阿里云账户绑定流程

2. **401 认证错误**

   - 检查您的 API 令牌是否正确
   - 确保令牌没有过期
   - 验证您的阿里云账户是否正确绑定

3. **模型不可用**
   - 某些模型可能需要额外权限
   - 查看 ModelScope 上模型页面的访问要求

### 调试模式

启用调试模式以查看详细日志：

```bash
DEBUG_MODELSCOPE_CHAT_COMPLETION=1
```

## 注意事项

- ModelScope API 与 OpenAI API 格式兼容
- 该服务主要面向中国用户设计
- 某些模型可能有使用限制或需要额外验证
- 某些模型的 API 响应默认为中文

## 支持

对于 ModelScope 特定问题：

- 访问 [ModelScope 文档](https://www.modelscope.cn/docs)
- 查看 [ModelScope 社区](https://www.modelscope.cn/community)

对于 LobeChat 集成问题：

- 查看我们的 [GitHub Issues](https://github.com/lobehub/lobe-chat/issues)
- 加入我们的社区讨论

## 模型 ID 格式

ModelScope 使用命名空间前缀格式的模型 ID，例如：

```
deepseek-ai/DeepSeek-V3-0324
deepseek-ai/DeepSeek-R1-0528
Qwen/Qwen3-235B-A22B
Qwen/Qwen3-32B
```

在配置模型列表时，请使用完整的模型 ID 格式。

## API 限制

- ModelScope API 有速率限制
- 某些模型可能需要特殊权限
- 建议在生产环境中监控 API 使用情况
- 部分高级模型可能需要付费使用
