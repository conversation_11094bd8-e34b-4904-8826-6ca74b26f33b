---
title: ModelScope Provider Setup
description: Learn how to configure and use ModelScope provider in LobeChat
tags:
  - ModelScope
---

# ModelScope Provider Setup

ModelScope (魔塔社区) is Alibaba's open-source model community that provides access to various AI models. This guide will help you set up the ModelScope provider in LobeChat.

## Prerequisites

Before using ModelScope API, you need to:

1. **Create a ModelScope Account**

   - Visit [ModelScope](https://www.modelscope.cn/)
   - Register for an account

2. **Bind Alibaba Cloud Account**

   - **Important**: ModelScope API requires binding with an Alibaba Cloud account
   - Visit your [ModelScope Access Token page](https://www.modelscope.cn/my/myaccesstoken)
   - Follow the instructions to bind your Alibaba Cloud account
   - This step is mandatory for API access

3. **Get API Token**
   - After binding your Alibaba Cloud account, generate an API token
   - Copy the token for use in LobeChat

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Enable ModelScope provider
ENABLED_MODELSCOPE=1

# ModelScope API key (required)
MODELSCOPE_API_KEY=your_modelscope_api_token

# Optional: Custom model list (comma-separated)
MODELSCOPE_MODEL_LIST=deepseek-ai/DeepSeek-V3-0324,Qwen/Qwen3-235B-A22B

# Optional: Proxy URL if needed
MODELSCOPE_PROXY_URL=https://your-proxy-url
```

### Docker Configuration

If using Docker, add the ModelScope environment variables to your `docker-compose.yml`:

```yaml
environment:
  - ENABLED_MODELSCOPE=1
  - MODELSCOPE_API_KEY=your_modelscope_api_token
  - MODELSCOPE_MODEL_LIST=deepseek-ai/DeepSeek-V3-0324,Qwen/Qwen3-235B-A22B
```

## Available Models

ModelScope provides access to various models including:

- **DeepSeek Models**: DeepSeek-V3, DeepSeek-R1 series
- **Qwen Models**: Qwen3 series, Qwen2.5 series
- **Llama Models**: Meta-Llama-3 series
- **Other Models**: Various open-source models

## Troubleshooting

### Common Issues

1. **"Please bind your Alibaba Cloud account before use" Error**

   - This means you haven't bound your Alibaba Cloud account to ModelScope
   - Visit [ModelScope Access Token page](https://www.modelscope.cn/my/myaccesstoken)
   - Complete the Alibaba Cloud account binding process

2. **401 Authentication Error**

   - Check if your API token is correct
   - Ensure the token hasn't expired
   - Verify that your Alibaba Cloud account is properly bound

3. **Model Not Available**
   - Some models may require additional permissions
   - Check the model's page on ModelScope for access requirements

### Debug Mode

Enable debug mode to see detailed logs:

```bash
DEBUG_MODELSCOPE_CHAT_COMPLETION=1
```

## Notes

- ModelScope API is compatible with OpenAI API format
- The service is primarily designed for users in China
- Some models may have usage limitations or require additional verification
- API responses are in Chinese by default for some models

## Support

For ModelScope-specific issues:

- Visit [ModelScope Documentation](https://www.modelscope.cn/docs)
- Check [ModelScope Community](https://www.modelscope.cn/community)

For LobeChat integration issues:

- Check our [GitHub Issues](https://github.com/lobehub/lobe-chat/issues)
- Join our community discussions
