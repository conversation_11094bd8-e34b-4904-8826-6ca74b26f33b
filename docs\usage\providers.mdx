---
title: Enhancing LobeChat with Multiple Model Providers for AI Conversations
description: >-
  Discover how LobeChat offers diverse AI conversation options by supporting multiple model providers, providing flexibility and a wide range of choices for users and developers.

tags:
  - LobeChat
  - AI Conversations
  - Model Providers
  - Diversity
  - Flexibility
  - Google AI
  - ChatGLM
  - Moonshot AI
  - 01 AI
  - Together AI
  - Ollama
---

# Using Multiple Model Providers in LobeChat

<Image alt={'Multiple Model Providers Support'} borderless cover src={'https://github.com/lobehub/lobe-chat/assets/17870709/1148639c-2687-4a9c-9950-8ca8672f34b6'} />

In the continuous development of LobeChat, we deeply understand the importance of diversity in model providers for providing AI conversation services to meet the needs of the community. Therefore, we have expanded our support to multiple model providers instead of being limited to a single one, in order to offer users a more diverse and rich selection of conversation options.

This approach allows LobeChat to adapt more flexibly to different user needs and provides developers with a wider range of choices.

## Tutorial on Using Model Providers

<ProviderCards locale={'en'} />
