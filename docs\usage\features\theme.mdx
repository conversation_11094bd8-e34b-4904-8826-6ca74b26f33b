---
title: LobeChat support Custom Themes
description: >-
  Explore LobeChat's flexible theme modes and color customization options for a personalized interface design. Switch between light and dark modes, customize theme colors, and choose between conversation bubble and document modes.

tags:
  - Custom Themes
  - Personalized User Experiences
  - Theme Modes
  - Color Customization
  - Interface Design
  - LobeChat
---

# Custom Themes

<Image alt={'Custom Themes'} borderless cover src={'https://github.com/user-attachments/assets/b47c39f1-806f-492b-8fcb-b0fa973937c1'} />

LobeChat places a strong emphasis on personalized user experiences in its interface design, and thus introduces flexible and diverse theme modes, including a light mode for daytime and a dark mode for nighttime.

In addition to theme mode switching, we also provide a series of color customization options, allowing users to adjust the application's theme colors according to their preferences. Whether it's a stable deep blue, a lively peach pink, or a professional gray and white, users can find color choices in LobeChat that match their own style.

<Callout type={'tip'}>
  The default configuration can intelligently identify the user's system color mode and
  automatically switch themes to ensure a consistent visual experience with the operating system.
</Callout>

For users who prefer to manually adjust details, LobeChat also provides intuitive setting options and offers a choice between conversation bubble mode and document mode for chat scenes.
