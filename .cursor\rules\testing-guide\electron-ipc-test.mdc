---
description: Electron IPC 接口测试策略
alwaysApply: false
---

### Electron IPC 接口测试策略 🖥️

对于涉及 Electron IPC 接口的测试，由于提供真实的 Electron 环境比较复杂，采用 **Mock 返回值** 的方式进行测试。

#### 基本 Mock 设置

```typescript
import { vi } from 'vitest';

import { electronIpcClient } from '@/server/modules/ElectronIPCClient';

// Mock Electron IPC 客户端
vi.mock('@/server/modules/ElectronIPCClient', () => ({
  electronIpcClient: {
    getFilePathById: vi.fn(),
    deleteFiles: vi.fn(),
    // 根据需要添加其他 IPC 方法
  },
}));
```

#### 在测试中设置 Mock 行为

```typescript
beforeEach(() => {
  // 重置所有 Mock
  vi.resetAllMocks();

  // 设置默认的 Mock 返回值
  vi.mocked(electronIpcClient.getFilePathById).mockResolvedValue('/path/to/file.txt');
  vi.mocked(electronIpcClient.deleteFiles).mockResolvedValue({
    success: true,
  });
});
```

#### 测试不同场景的示例

```typescript
it('应该处理文件删除成功的情况', async () => {
  // 设置成功场景的 Mock
  vi.mocked(electronIpcClient.deleteFiles).mockResolvedValue({
    success: true,
  });

  const result = await service.deleteFiles(['desktop://file1.txt']);

  expect(electronIpcClient.deleteFiles).toHaveBeenCalledWith(['desktop://file1.txt']);
  expect(result.success).toBe(true);
});

it('应该处理文件删除失败的情况', async () => {
  // 设置失败场景的 Mock
  vi.mocked(electronIpcClient.deleteFiles).mockRejectedValue(new Error('删除失败'));

  const result = await service.deleteFiles(['desktop://file1.txt']);

  expect(result.success).toBe(false);
  expect(result.errors).toBeDefined();
});
```

#### Mock 策略的优势

1. **环境简化**: 避免了复杂的 Electron 环境搭建
2. **测试可控**: 可以精确控制 IPC 调用的返回值和行为
3. **场景覆盖**: 容易测试各种成功/失败场景
4. **执行速度**: Mock 调用比真实 IPC 调用更快

#### 注意事项

- **Mock 准确性**: 确保 Mock 的行为与真实 IPC 接口行为一致
- **类型安全**: 使用 `vi.mocked()` 确保类型安全
- **Mock 重置**: 在 `beforeEach` 中重置 Mock 状态，避免测试间干扰
- **调用验证**: 不仅要验证返回值，还要验证 IPC 方法是否被正确调用
