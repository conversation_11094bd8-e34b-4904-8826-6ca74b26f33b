---
title: Configuring Tencent Cloud COS Storage Service
description: >-
  Detailed steps to configure Tencent Cloud COS storage service for smooth file storage.

tags:
  - Tencent Cloud COS
  - S3 Storage
  - File Storage
  - Environment Variables
---

# Configuring Tencent Cloud COS Storage Service

We need to configure S3 storage service for file storage in the server-side database.

## Configuration Steps

<Steps>
  ### Configure and Obtain S3 Bucket

  Create a new bucket on [Tencent Cloud COS](https://console.cloud.tencent.com/cos/bucket):

  <Image alt={'Tencent Cloud COS Storage Interface'} src={'https://github.com/user-attachments/assets/d3626294-74ba-4944-9a63-052e6cf719ab'} />

  Name the bucket (e.g., 'lobe'). Choose 'Public Read, Private Write', select any region, and keep other settings default.

  <Image alt={'COS Create Bucket'} src={'https://github.com/user-attachments/assets/7257eb0e-4e2c-4db2-981d-354598e2c60f'} />

  ### Get Bucket-related Environment Variables

  View bucket information in COS bucket overview:

  <Image alt={'View Bucket Information'} src={'https://github.com/user-attachments/assets/d7d65e32-679d-4e50-a933-28cf5dde1330'} />

  Corresponding environment variables:

  ```shell
  S3_BUCKET=lobe-130xxxxxx2
  S3_ENDPOINT=https://cos.ap-chengdu.myqcloud.com
  S3_REGION=ap-chengdu
  S3_PUBLIC_DOMAIN=https://lobe-1251234567.cos.ap-chengdu.myqcloud.com
  ```

  <Callout type={'warning'}>
    Note: `S3_ENDPOINT` should be `https://cos.ap-chengdu.myqcloud.com`, not `https://lobe-130xxxxxx2.cos.ap-chengdu.myqcloud.com`

    Include `https://` in the URL
  </Callout>

  ### Configure CORS

  Add the following in 'Security Management - CORS Settings':

  <Image alt={'Configure CORS'} src={'https://github.com/user-attachments/assets/35164b25-c964-42ce-9cb0-32f6ebe1d07c'} />

  ### Get S3 Keys

  Create a new SecretId and SecretKey at [Tencent Cloud API Key Management](https://console.cloud.tencent.com/cam/capi). Use as `S3_ACCESS_KEY_ID` and `S3_SECRET_ACCESS_KEY`.
</Steps>

### Additional Notes

- Consider setting up CDN acceleration for improved access speed and reduced costs.
- Use role management when creating API keys for enhanced security.
