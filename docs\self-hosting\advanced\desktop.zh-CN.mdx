---
title: 与 LobeChat 桌面端自动同步
description: 配置 LobeChat 桌面端，获取端同步体验
tags:
  - LobeChat
  - 桌面端
  - 自托管
  - OIDC
  - 配置指南
---

# 与 LobeChat 桌面端自动同步

LobeChat 桌面端为用户提供了更好的使用体验，同时也扩展了 LobeChat 的能力边界，允许用户在离线环境下使用 LobeChat。本文档将指导您如何配置 LobeChat 桌面端以及如何将其与自托管实例连接。

## 系统要求

LobeChat 桌面端使用 Electron 构建，支持以下操作系统：

- Windows
- macOS
- Linux（部分发行版）

## 桌面端版本说明

LobeChat 桌面端的版本发布遵循以下逻辑：

- **Beta 版本**：所有合并到 main 分支并自动发布版本，都会自动创建为 LobeChat 桌面端的 Beta 版本
- **Stable 版本**：开发团队会通过手动打标来定期发布稳定版本

如果您希望获得最新功能，可以使用 Beta 版本；如果您注重稳定性，请使用 Stable 版本。

## 与自托管实例连接

LobeChat 桌面端可以与您自托管的 LobeChat 实例连接，以便您可以在桌面端使用您的自托管配置。

### 准备工作

在将桌面端与自托管实例连接前，您需要确保您的自托管实例已正确配置 OIDC 相关环境变量。

<Callout>请确保您的自托管实例版本为 1.83.6 或更高，以支持桌面端连接功能。</Callout>

#### OIDC 环境变量配置

您需要在自托管实例中添加以下`ENABLE_OIDC` 和 `OIDC_JWKS_KEY` 这两个环境变量，你可以点击下方按钮一键生成：

<OIDCJWKs />

将生成的 JWK 密钥添加到您的环境变量中。

如果您使用一键部署方式（如 Vercel、Railway 等平台）部署 LobeChat，您需要：

1. 将上述环境变量添加到部署平台的环境变量配置中
2. 对于 Vercel 部署，请确保 `APP_URL` 环境变量设置为你的域名 URL，这对于正确处理认证回调至关重要

### 连接步骤

1. 打开 LobeChat 桌面端
2. 点击右上角 wifi 图标
3. 输入您的自托管实例地址（例如：`https://your-lobechat-instance.com`）
4. 点击 "连接" 按钮
5. 您将被重定向到登录页面，完成授权后即可使用您的自托管配置

## 常见问题

### 502 Bad Gateway

**问题：** 点击授权按钮后，跳转到 `/oidc/consent` 路径时出现 502 Bad Gateway 错误。

**解决方案：**

- 确认您已经正确设置了 `ENABLE_OIDC=1` 和 `OIDC_JWKS_KEY` 环境变量
- 确保 `OIDC_JWKS_KEY` 是有效的 JSON 格式，没有额外的单引号
- 检查您的服务端日志，查看具体错误信息

如果您使用 Nginx 作为反向代理，可能是请求头太大导致的问题。可以尝试在 Nginx 配置中添加以下设置：

```nginx
proxy_buffer_size   16k;
proxy_buffers       8 16k;
proxy_busy_buffers_size 32k;
```

### invalid\_redirect\_uri 错误

**问题：** 授权过程中遇到 "redirect\_uri did not match any of the client's registered redirect\_uris" 错误。

**解决方案：**

检查 `APP_URL` 环境变量是否与您访问的 URL 一致

### macOS 启动卡住或闪退

**问题：** 在 macOS 上，应用启动后长时间卡住或闪退。

**解决方案：**

- 检查您是否下载了正确的 macOS 架构版本：
  - 对于 Apple Silicon (M3/M4 等) Mac，应使用 `arm64` 版本
  - 对于 Intel Mac，应使用 `x64` 版本
- 文件名示例：
  - Apple Silicon: `LobeHub-Beta-1.83.6-arm64.dmg` 或 `LobeHub-Beta-1.83.6-arm64-mac.zip`
  - Intel: `LobeHub-Beta-1.83.6-x64.dmg` 或 `LobeHub-Beta-1.83.6-mac.zip`

### 图片加载问题

**问题：** 使用 Docker 部署的服务端，桌面端连接后无法加载头像或其他图片资源。

**解决方案：**

- 如果您使用 Minio 作为存储，需要在 Minio 配置中允许跨域请求
- 在您的 Minio 服务中添加适当的 CORS 配置
