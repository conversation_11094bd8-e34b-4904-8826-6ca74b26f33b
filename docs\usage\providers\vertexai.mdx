---
title: Using Vertex AI API Key in LobeChat
description: Learn how to configure and use Vertex AI models in LobeChat, get an API key, and start a conversation.
tags:
  - LobeChat
  - Vertex AI
  - API Key
  - Web UI
---

# Using Vertex AI in LobeChat

<Image alt={'Using Vertex AI in LobeChat'} cover src={'https://github.com/user-attachments/assets/638dcd7c-2bff-4adb-bade-da2aaef872bf'} />

[Vertex AI](https://cloud.google.com/vertex-ai) is a fully managed, integrated AI development platform from Google Cloud, designed for building and deploying generative AI. It provides easy access to Vertex AI Studio, Agent Builder, and over 160 foundational models for AI development.

This document will guide you on how to connect Vertex AI models in LobeChat:

<Steps>
  ### Step 1: Prepare a Vertex AI Project

  - First, visit [Google Cloud](https://console.cloud.google.com/) and complete the registration and login process.
  - Create a new Google Cloud project or select an existing one.
  - Go to the [Vertex AI Console](https://console.cloud.google.com/vertex-ai).
  - Ensure that the Vertex AI API service is enabled for the project.

  <Image alt={'Accessing Vertex AI'} inStep src={'https://github.com/user-attachments/assets/c4fe4430-7860-4339-b014-4d8d264a12c0'} />

  ### Step 2: Set Up API Access Permissions

  - Go to the Google Cloud [IAM Management page](https://console.cloud.google.com/iam-admin/serviceaccounts) and navigate to `Service Accounts`.
  - Create a new service account and assign a role permission to it, such as `Vertex AI User`.

  <Image alt={'Creating a Service Account'} inStep src={'https://github.com/user-attachments/assets/692e7c67-f173-45da-86ef-5c69e17988e4'} />

  - On the service account management page, find the service account you just created, click `Keys`, and create a new JSON format key.
  - After successful creation, the key file will be automatically saved to your computer in JSON format. Please keep it safe.

  <Image alt={'Creating a Key'} inStep src={'https://github.com/user-attachments/assets/1fb5df18-5261-483e-a445-96f52f80dd20'} />

  ### Step 3: Configure Vertex AI in LobeChat

  - Visit the `App Settings` and then the `AI Service Provider` interface in LobeChat.
  - Find the settings item for `Vertex AI` in the list of providers.

  <Image alt={'Entering Vertex AI API Key'} inStep src={'https://github.com/user-attachments/assets/5d672e8b-566f-4f82-bdce-947168726bc0'} />

  - Open the Vertex AI service provider settings.
  - Fill the entire content of the JSON format key you just obtained into the API Key field.
  - Select a Vertex AI model for your assistant to start the conversation.

  <Image alt={'Selecting a Vertex AI Model'} inStep src={'https://github.com/user-attachments/assets/1a7e9600-cd0f-4c82-9d32-4e61bbb351cc'} />

  <Callout type={'warning'}>
    You may need to pay the API service provider during usage. Please refer to Google Cloud's relevant fee policies.
  </Callout>
</Steps>

Now you can use the models provided by Vertex AI for conversations in LobeChat.
