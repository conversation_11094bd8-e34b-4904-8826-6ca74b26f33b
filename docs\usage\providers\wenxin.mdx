---
title: Using <PERSON><PERSON> Qianfan in LobeChat
description: >-
  Learn how to integrate and utilize <PERSON><PERSON> Qianfan's language model APIs in LobeChat.

tags:
  - LobeChat
  - 百度
  - 文心千帆
  - API密钥
  - Web UI
---

# Using <PERSON><PERSON> Qianfan in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/e43dacf6-313e-499c-8888-f1065c53e424'} />

[<PERSON><PERSON>fan](https://qianfan.cloud.baidu.com/) is an artificial intelligence large language model platform launched by Baidu, supporting a variety of application scenarios, including literary creation, commercial copywriting, and mathematical logic reasoning. The platform features deep semantic understanding and generation capabilities across modalities and languages, and it is widely utilized in fields such as search Q\&A, content creation, and smart office applications.

This article will guide you on how to use Wenxin Qianfan in LobeChat.

<Steps>
  ### Step 1: Obtain the Wenxin Qianfan API Key

  - Register and log in to the [Baidu AI Cloud Console](https://console.bce.baidu.com/)
  - Navigate to `Baidu AI Cloud Qianfan ModelBuilder`
  - Select `API Key` from the left menu

  <Image alt={'API Key'} inStep src={'https://github.com/user-attachments/assets/6234428d-5633-4b2f-be22-1a1772a69a55'} />

  - Click `Create API Key`
  - In `Service`, select `Qianfan ModelBuilder`
  - In `Resource`, choose `All Resources`
  - Click the `Confirm` button
  - Copy the `API Key` and keep it safe

  <Image alt={'Create API Key'} inStep src={'https://github.com/user-attachments/assets/6d068fe0-8100-4b43-b0c3-7934f54e688f'} />

  <Image alt={'Copy API Key'} inStep src={'https://github.com/user-attachments/assets/629adf4e-e9e1-40dc-b9e5-d7b908878170'} />

  ### Step 2: Configure Wenxin Qianfan in LobeChat

  - Go to the `Settings` page of LobeChat
  - Under `AI Service Provider`, find the `Wenxin Qianfan` settings

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/d7666e2a-0202-4b45-8338-9806ddffa44e'} />

  - Enter the obtained `API Key`
  - Select a Wenxin Qianfan model for your AI assistant, and you're ready to start chatting!

  <Image alt={'Select Wenxin Qianfan Model and Start Chat'} inStep src={'https://github.com/user-attachments/assets/b6e6a3eb-13c6-46f0-9c7c-69a20deae30f'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider. Please refer to Wenxin Qianfan's
    relevant fee policy.
  </Callout>
</Steps>

You can now use the models provided by Wenxin Qianfan for conversations in LobeChat.
