---
title: Configuring Github Authentication Service for LobeChat
description: >-
  Learn how to configure Github authentication service for LobeChat, including creating a Github provider, setting up environment variables, and deploying LobeChat.

tags:
  - Github authentication
  - LobeChat
  - Environment variables
  - Single Sign-On
  - OAuth authentication
---

# Configuring Github Authentication Service

## Github Configuration Process

<Steps>
  ### Create a Github Provider

  Click [here][github-create-app] to create a new Github App.

  Fill in the Github App name, Homepage URL, and Callback URL.

  <Image alt="Create a Github Provider" inStep src="https://github.com/lobehub/lobe-chat/assets/64475363/2f919f99-2aaa-4fa7-9938-169d3ed09db7" />

  Set the webhook callback URL according to your needs.

  <Image alt="Fill in other fields" inStep src="https://github.com/lobehub/lobe-chat/assets/64475363/d7ef5ad1-b1a3-435e-b1bc-4436d2b6fecd" />

  Set the permission to read email addresses.

  <Image alt="Set required permissions" inStep src="https://github.com/lobehub/lobe-chat/assets/64475363/23131ca1-9e84-4a89-a840-ef79c4bc0251" />

  <Image alt="Set permission to read email addresses" inStep src="https://github.com/lobehub/lobe-chat/assets/64475363/358bca8d-3d82-4e76-9a5e-90d16a39efde" />

  Set whether it is accessible publicly or only accessible to yourself.

  <Image alt="Set whether it is accessible publicly or only accessible to yourself" inStep src="https://github.com/lobehub/lobe-chat/assets/64475363/995780cb-9096-4a36-ab17-d422703ab970" />

  Click "Create Github App".

  After successful creation, click "Generate a new client secret" to create a client secret.

  <Image alt="Create a new client secret" inStep src="https://github.com/lobehub/lobe-chat/assets/64475363/6d69bdca-7d18-4cbc-b3e0-220d8815cd29" />

  After successful creation, save the `Client ID` and `Client Secret`.

  <Image alt="Create a new client secret" inStep src="https://github.com/lobehub/lobe-chat/assets/64475363/c6108133-a918-48b0-ab1a-e3fa607572a4" />

  ### Configure Environment Variables

  When deploying LobeChat, you need to configure the following environment variables:

  | Environment Variable      | Type     | Description                                                                                                                                                                                           |
  | ------------------------- | -------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
  | `NEXT_AUTH_SECRET`        | Required | Key used to encrypt Auth.js session tokens. You can generate the key using the command: `openssl rand -base64 32`                                                                                     |
  | `NEXT_AUTH_SSO_PROVIDERS` | Required | Select the Single Sign-On provider for LobeChat. Use `github` for Github.                                                                                                                             |
  | `AUTH_GITHUB_ID`          | Required | Client ID in the Github App details page.                                                                                                                                                             |
  | `AUTH_GITHUB_SECRET`      | Required | Client Secret in the Github App details page.                                                                                                                                                         |
  | `NEXTAUTH_URL`            | Required | This URL is used to specify the callback address for Auth.js when performing OAuth authentication. Only set it if the default generated redirect address is incorrect. `https://example.com/api/auth` |

  <Callout type={'tip'}>
    Go to [📘 Environment Variables](/docs/self-hosting/environment-variables/auth#github) for detailed
    information on these variables.
  </Callout>
</Steps>

<Callout type={'info'}>
  After successful deployment, users will be able to authenticate with Github and use LobeChat.
</Callout>

[github-create-app]: https://github.com/settings/apps/new
