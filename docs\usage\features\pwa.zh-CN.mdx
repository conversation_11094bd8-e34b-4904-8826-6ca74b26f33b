---
title: LobeChat 支持渐进式 Web 应用（PWA）- 提升用户体验
description: 了解渐进式 Web 应用（PWA）技术如何提升网页应用至接近原生应用体验，以及如何在桌面和移动设备上提供优化的用户体验。
tags:
  - 渐进式 Web 应用
  - PWA 技术
  - 用户体验
  - 桌面应用
  - 移动设备
  - 轻量级
  - 高性能
  - 响应式布局
---

# 渐进式 Web 应用（PWA）

<Image alt={'渐进式 Web 应用（PWA）'} borderless cover src={'https://github.com/user-attachments/assets/9647f70f-b71b-43b6-9564-7cdd12d1c24d'} />

我们深知在当今多设备环境下为用户提供无缝体验的重要性。为此，我们采用了渐进式 Web 应用 [PWA](https://support.google.com/chrome/answer/9658361) 技术，这是一种能够将网页应用提升至接近原生应用体验的现代 Web 技术。通过 PWA，LobeChat 能够在桌面和移动设备上提供高度优化的用户体验，同时保持轻量级和高性能的特点。在视觉和感觉上，我们也经过精心设计，以确保它的界面与原生应用无差别，提供流畅的动画、响应式布局和适配不同设备的屏幕分辨率。

若您未熟悉 PWA 的安装过程，您可以按照以下步骤将 LobeChat 添加为您的桌面应用（也适用于移动设备）：

## Chrome / Edge 浏览器上运行

<Callout type={'important'}>
  macOS 下，使用 Chrome 安装的 PWA 时，必须要求 Chrome 是打开状态，否则会自动打开 Chrome 再打开 PWA
  应用。
</Callout>

<Steps>
  ### 在电脑上运行 Chrome 或 Edge 浏览器

  ### 访问 LobeChat 网页

  ### 在地址栏的右上角，单击 <kbd>安装</kbd> 图标

  ### 根据屏幕上的指示完成 PWA 的安装
</Steps>

## Safari 浏览器上运行

Safari PWA 需要 macOS Ventura 或更高版本。Safari 安装的 PWA 并不要求 Safari 是打开状态，可以直接打开 PWA 应用。

<Steps>
  ### 在电脑上运行 Safari 浏览器

  ### 访问 LobeChat 网页

  ### 在地址栏的右上角，单击 <kbd>分享</kbd> 图标

  ### 点选 <kbd>添加到程序坞</kbd>

  ### 根据屏幕上的指示完成 PWA 的安装
</Steps>

<Callout type={'tip'}>
  默认安装的 LobeChat PWA 图标是黑色背景的，您可以在自行使用 <kbd>cmd</kbd> + <kbd>i</kbd>{' '}
  粘贴如下图片替换为白色背景的。
</Callout>

<Image alt={'PWA White Icon'} borderless cover src={'https://github.com/lobehub/lobe-chat/assets/36695271/16ce82cb-49be-4d4d-ac86-4403a1536917'} />
