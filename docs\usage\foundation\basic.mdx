---
title: Basic Usage Guide for Conversations - Large Language Models (LLMs)
description: >-
  Learn about the fundamental functions for interacting with Large Language Models (LLMs) and how to utilize features like model selection, file/image upload, temperature setting, and more.

tags:
  - Large Language Models
  - Model Selection
  - File Upload
  - Temperature Setting
  - Voice Input
  - Plugin Setting
---

# Basic Usage Guide for Conversations

<Image alt={'Basic Functions'} cover src={'https://github.com/user-attachments/assets/ca6baea8-d782-4611-8555-7c81078028db'} />

In general, the basic interaction with Large Language Models (LLMs) can be done through the fundamental functions provided in this area (as shown above).

## Basic Function Description

<Image alt={'Basic Function Description'} src={'https://github.com/user-attachments/assets/de9f188e-8a3a-49a0-bec6-ab628cdda7e6'} />

1. **Model Selection**: Choose the Large Language Model (LLM) to be used in the current conversation. For model settings, refer to [Model Providers](/docs/usage/providers).
2. **File/Image Upload**: When the selected model supports file or image recognition, users can upload files or images during the conversation with the model.
3. **Temperature Setting**: Adjust the randomness level of the model's output. The higher the value, the more random the output results. For detailed information, refer to the [Large Language Model Guide](/docs/usage/agents/model).
4. **History Record Setting**: Set the number of chat records the model needs to remember in this conversation. The longer the history, the more conversation content the model can remember, but it will also consume more context tokens.
5. **Voice Input**: Click this button to convert speech to text input. For more information, refer to [Speech-to-Text Conversion](/docs/usage/foundation/tts-stt).
6. **Plugin Setting**: Choose the plugins to enable in this conversation. For more information, refer to [Plugin Usage](/docs/usage/plugins/basic-usage).
7. **Token Usage**: Display the context length and token consumption of this conversation.
8. **Start New Topic**: End the current conversation and start a new topic. For more information, refer to [Topic Usage](/docs/usage/agents/topics).
9. **Send Button**: Send the current input content to the model. The dropdown menu provides additional send operation options.

<Image alt={'Send Button'} inStep src={'https://github.com/user-attachments/assets/0833184e-91df-4b8e-a88e-99a19c20b86a'} />

<Callout type={'info'}>
  - **Send Shortcut**: Set a shortcut to send messages and line breaks using the Enter key or ⌘ +
    Enter key. - **Add an AI Message**: Manually add and edit a message input by an AI character in
    the conversation context, which will not trigger a model response. - **Add a User Message**: Add
    the current input content as a message input by the user character to the conversation context,
    which will not trigger a model response.
</Callout>
