---
title: 在 LobeChat 中使用 Minimax API Key
description: >-
  学习如何在 LobeChat 中配置并使用 MiniMax 智能模型进行对话。获取 MiniMax API 密钥、配置步骤详解，开始与 MiniMax 模型交互。

tags:
  - LobeChat
  - MiniMax
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 Minimax

<Image alt={'在 LobeChat 中使用 Minimax'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/703f170b-c03b-4c71-b57d-c2357596bdfb'} />

[MiniMax](https://www.minimaxi.com/) 是 2021 年成立的通用人工智能科技公司，致力于与用户共创智能。MiniMax 自主研发了不同模态的通用大模型，其中包括万亿参数的 MoE 文本大模型、语音大模型以及图像大模型。并推出了海螺 AI 等应用。

本文档将指导你如何在 LobeChat 中使用 Minimax:

<Steps>
  ### 步骤一：获取 MiniMax API 密钥

  - 注册并登录 [MiniMax 开放平台](https://www.minimaxi.com/platform)
  - 在 `账户管理` 中找到 `接口密钥` 菜单，并创建新的密钥

  <Image alt={'创建 MiniMax API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/f6e46f1c-0ac9-42ae-8e83-ddb0cc6c5bf8'} />

  - 填写一个 API 密钥的名称并创建

  <Image alt={'填写 API 密钥名称'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/cbc23ca9-1188-4b85-8ef0-e75ac7d74b92'} />

  - 在弹出的对话框中复制 API 密钥，并妥善保存

  <Image alt={'保存 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/fb0f7574-c2f5-40d6-8613-3749e85ce881'} />

  <Callout type={'warning'}>
    请安全地存储密钥，因为它只会出现一次。如果您意外丢失它，您将需要创建一个新密钥。
  </Callout>

  ### 步骤二：在 LobeChat 中配置 MiniMax

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`MiniMax`的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/b839e04e-0cef-46a3-bb84-0484a3f51c69'} />

  - 打开 Minimax 并填入获得的 API 密钥
  - 为你的 AI 助手选择一个 MiniMax 的模型即可开始对话

  <Image alt={'选择 MiniMax 模型并开始对话'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/f7d59c7a-abd0-4ebd-8c72-ca10c47a0f1a'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 MiniMax 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 MiniMax 提供的模型进行对话了。
