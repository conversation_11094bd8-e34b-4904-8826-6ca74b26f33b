---
title: Guide to Text-to-Speech Conversion - LobeChat TTS Feature
description: >-
  Learn how to use LobeChat's text-to-speech (TTS) feature for voice input and output. Explore speech-to-text (STT) functionality and customize TTS settings.

tags:
  - Text-to-Speech
  - TTS feature
  - Speech-to-Text
  - STT feature
  - TTS settings
---

# Guide to Text-to-Speech Conversion

LobeChat supports text-to-speech conversion, allowing users to input content through voice and have the AI output read aloud through speech.

## Text-to-Speech (TTS)

Select any content in the chat window, choose `Text-to-Speech`, and the AI will use the TTS model to read the text content aloud.

<Image alt={'TTS'} src={'https://github.com/user-attachments/assets/d2714769-15f8-4d70-9128-607134163c52'} />

## Speech-to-Text (STT)

Select the voice input feature in the input window, and LobeChat will convert your speech to text and input it into the text box. After completing the input, you can send it directly to the AI.

<Image alt={'STT'} src={'https://github.com/user-attachments/assets/d643af6d-ca0f-4abd-9dd2-977dacecb25d'} />

## Text-to-Speech Conversion Settings

You can specify the model you want to use for text-to-speech conversion in the settings.

<Image alt={'TTS Settings'} src={'https://github.com/user-attachments/assets/2f7c5c45-ec6a-4393-8fa9-19a4c5f52f7a'} />

- Open the `Settings` panel
- Find the `Text-to-Speech` settings
- Select the speech service and AI model you prefer
