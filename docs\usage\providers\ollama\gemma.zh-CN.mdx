---
title: 在 LobeChat 中使用 Google Gemma 模型
description: >-
  通过 LobeChat 与 Ollama 的集成，轻松使用 Google Gemma 模型进行自然语言处理任务。安装 Ollama，拉取 Gemma 模型，选择模型面板中的 Gemma 模型，开始对话。

tags:
  - Google Gemma
  - LobeChat
  - Ollama
  - 自然语言处理
  - 模型选择
---

# 使用 Google Gemma 模型

<Image alt={'在 LobeChat 中使用 Gemma'} cover rounded src={'https://github.com/lobehub/lobe-chat/assets/17870709/65d2dd2a-fdcf-4f3f-a6af-4ed5164a510d'} />

[Gemma](https://blog.google/technology/developers/gemma-open-models/) 是 Google 开源的一款大语言模型（LLM），旨在提供一个更加通用、灵活的模型用于各种自然语言处理任务。现在，通过 LobeChat 与 [Ollama](https://ollama.com/) 的集成，你可以轻松地在 LobeChat 中使用 Google Gemma。

本文档将指导你如何在 LobeChat 中使用 Google Gemma：

<Steps>
  ### 本地安装 Ollama

  首先，你需要安装 Ollama，安装过程请查阅 [Ollama 使用文件](/zh/docs/usage/providers/ollama)。

  ### 用 Ollama 拉取 Google Gemma 模型到本地

  在安装完成 Ollama 后，你可以通过以下命令安装 Google Gemma 模型，以 7b 模型为例：

  ```bash
  ollama pull gemma
  ```

  <Image alt={'使用 Ollama 拉取 Gemma 模型'} height={473} inStep src={'https://github.com/lobehub/lobe-chat/assets/28616219/7049a811-a08b-45d3-8491-970f579c2ebd'} width={791} />

  ### 选择 Gemma 模型

  在会话页面中，选择模型面板打开，然后选择 Gemma 模型。

  <Image alt={'模型选择面板中选择 Gemma 模型'} height={629} inStep src={'https://github.com/lobehub/lobe-chat/assets/28616219/69414c79-642e-4323-9641-bfa43a74fcc8'} width={791} />

  <Callout type={'info'}>
    如果你没有在模型选择面板中看到 Ollama 服务商，请查阅 [与 Ollama
    集成](/zh/docs/self-hosting/examples/ollama) 了解如何在 LobeChat 中开启 Ollama 服务商。
  </Callout>
</Steps>

接下来，你就可以使用 LobeChat 与本地 Gemma 模型对话了。
