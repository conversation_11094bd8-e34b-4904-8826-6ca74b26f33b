---
title: 在 LobeChat 中配置 Auth0 身份验证服务 - 详细步骤和环境变量设置
description: 学习如何在 LobeChat 中配置 Auth0 身份验证服务，包括创建应用、新增用户、配置环境变量等。了解如何连接现有的单点登录服务和配置社交登录。
tags:
  - Auth0
  - 身份验证
  - 单点登录
  - 社交登录
  - 环境变量
  - 用户管理
---

# 配置 Auth0 身份验证服务

<Steps>
  ### 创建 Auth0 应用

  注册并登录 [Auth0](https://manage.auth0.com/dashboard)，点击左侧导航栏的「Applications」，切换到应用管理界面，点击右上角「Create Application」以创建应用。

  <Image alt="创建 Auth0 应用 S1" inStep src="https://github.com/lobehub/lobe-chat/assets/30863298/f068190f-0027-4d3b-8667-d632e43d5a86" />

  填写你想向组织用户显示的应用名称，可选择任意应用类型，点击「Create」。

  <Image alt="创建 Auth0 应用 S2" inStep src="https://github.com/lobehub/lobe-chat/assets/30863298/3e0082df-9b6f-46f3-b67f-bdc79e1eb2cc" />

  创建成功后，点击相应的应用，进入应用详情页，切换到「Settings」标签页，就可以看到相应的配置信息

  <Image alt="创建 Auth0 应用 S3" inStep src="https://github.com/lobehub/lobe-chat/assets/30863298/df4cea85-616a-46f5-b2de-42725d9b82a6" />

  在应用配置页面中，还需要配置 Allowed Callback URLs，在此处填写:

  ```bash
  http(s)://your-domain/api/auth/callback/auth0
  ```

  <Image alt="创建 Auth0 应用 S4" inStep src="https://github.com/lobehub/lobe-chat/assets/30863298/62fbd09f-a69a-4460-949b-0f6285fa65b9" />

  <Callout type={'important'}>
    可以在部署后再填写或修改 Allowed Callback URLs，但是务必保证填写的 URL 与部署的 URL 一致
  </Callout>

  ### 新增用户

  点击左侧导航栏的「Users Management」，进入用户管理界面，可以为你的组织新建用户，用以登录 LobeChat

  <Image alt="新增用户" inStep src="https://github.com/lobehub/lobe-chat/assets/30863298/0beda150-d0b6-43cf-a9f1-fce928b83a96" />

  ### 配置环境变量

  在部署 LobeChat 时，你需要配置以下环境变量：

  | 环境变量                      | 类型 | 描述                                                                                          |
  | ------------------------- | -- | ------------------------------------------------------------------------------------------- |
  | `NEXT_AUTH_SECRET`        | 必选 | 用于加密 Auth.js 会话令牌的密钥。您可以使用以下命令生成秘钥： `openssl rand -base64 32`                               |
  | `NEXT_AUTH_SSO_PROVIDERS` | 必选 | 选择 LoboChat 的单点登录提供商。使用 Auth0 请填写 `auth0`。                                                  |
  | `AUTH_AUTH0_ID`           | 必选 | Auth0 应用程序的 Client ID                                                                       |
  | `AUTH_AUTH0_SECRET`       | 必选 | Auth0 应用程序的 Client Secret                                                                   |
  | `AUTH_AUTH0_ISSUER`       | 必选 | Auth0 应用程序的 Domain，`https://example.auth0.com`                                              |
  | `NEXTAUTH_URL`            | 必选 | 该 URL 用于指定 Auth.js 在执行 OAuth 验证时的回调地址，当默认生成的重定向地址发生不正确时才需要设置。`https://example.com/api/auth` |

  <Callout type={'tip'}>
    前往 [📘 环境变量](/zh/docs/self-hosting/environment-variables/auth#auth-0) 可查阅相关变量详情。
  </Callout>
</Steps>

<Callout type={'info'}>
  部署成功后，用户将可以使用 Auth0 中配置的用户通过身份认证并使用 LobeChat。
</Callout>

## 进阶配置

### 连接现有的单点登录服务

如果你的企业或组织已有现有的统一身份认证设施，可在 Applications -> SSO Integrations 中，连接现有的单点登录服务。

Auth0 支持 Azure Active Directory / Slack / Google Workspace / Office 365 / Zoom 等单点登录服务，详细支持列表可参考 [这里](https://marketplace.auth0.com/features/sso-integrations)

<Image alt="Connecting to an Existing Single Sign-On Service" src="https://github.com/lobehub/lobe-chat/assets/30863298/9891347e-a338-4aa9-8714-f16c8dbcfcec" />

### 配置社交登录

如果你的企业或组织需要支持外部人员登录，可以在 Authentication -> Social 中，配置社交登录服务。

<Image alt="Configuring Social Login" src="https://github.com/lobehub/lobe-chat/assets/30863298/880749a6-5ba4-4e20-a968-b583a54de7fa" />

<Callout type={'warning'}>
  配置社交登录服务默认会允许所有人通过认证，这可能会导致 LobeChat 被外部人员滥用。
</Callout>

<Callout>
  如果你需要限制登录人员，务必配置 **阻止策略**： 请在打开社交登录选项后，参考
  [这篇文章](https://auth0.com/blog/permit-or-deny-login-requests-using-auth0-actions/) 创建 Action
  来设置阻止 / 允许列表。
</Callout>
