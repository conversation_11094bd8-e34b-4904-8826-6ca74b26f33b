---
title: Deploy LobeChat using aaPanel
description: >-
  Learn how to deploy the LobeChat service using aaPanel-Docker, including installing the Docker container environment and using the command to start the service with one click. Detailed instructions on how to configure environment variables and use proxy addresses.

tags:
  - Docker
  - LobeChat
  - Deployment guidelines
---

## Prerequisite

To install aaPanel, go to the [aaPanel](https://www.aapanel.com/new/download.html#install) official website and select the corresponding script to download and install.

## Deployment

1. Log in to aaPanel and click `Docker` in the menu bar ![install](https://github.com/user-attachments/assets/f67180c2-47ba-4b04-9f12-d274c7821085)

2. The first time you will be prompted to install the `Docker` and `Docker Compose` services, click Install Now. If it is already installed, please ignore it. ![install2](https://github.com/user-attachments/assets/e269bd27-d323-43ba-811b-c0f5e4137903)

3. After the installation is complete, find `LobeChat` in `One-Click Install` and click `install`\
   ![install-LobeChat](https://github.com/user-attachments/assets/9f989104-bb8e-4acd-9721-6b1db1017d2b)

4. configure basic information such as the domain name, OpenAI API key, and port to complete the installation Note: The domain name is optional, if the domain name is filled, it can be managed through \[Website]--> \[Proxy Project], and you do not need to check \[Allow external access] after filling in the domain name, otherwise you need to check it before you can access it through the port ![addLobeChat](https://github.com/user-attachments/assets/f0b2e72d-9eee-46a8-b094-4834b78764df)

5. After installation, enter the domain name or IP+ port set in the previous step in the browser to access.

- Name: application name, default `LobeChat-random characters`
- Version selection: default `latest`
- Domain name: If you need to access directly through the domain name, please configure the domain name here and resolve the domain name to the server
- Allow external access: If you need direct access through `IP+Port`, please check. If you have set up a domain name, please do not check here.
- Port: Default `3210`, can be modified by yourself

6. After submission, the panel will automatically initialize the application, which will take about `1-3` minutes. It can be accessed after the initialization is completed.

<Callout type="warning">
  ⚠️ Do not enable any form of cache in the reverse proxy settings of the panel to avoid affecting
  the normal operation of the service. Read more at
  [https://github.com/lobehub/lobe-chat/discussions/5986](https://github.com/lobehub/lobe-chat/discussions/5986)
</Callout>

## Visit LobeChat

- If you have set a domain name, please directly enter the domain name in the browser address bar, such as `http://demo.lobechat`, to access the `LobeChat` console.
- If you choose to access through `IP+Port`, please enter the domain name in the browser address bar to access `http://<aaPanelIP>:3210` to access the `HertzBeat` console. ![console](https://github.com/user-attachments/assets/a1af5778-f47a-4fdc-baf5-ca2a1e66f48e)
