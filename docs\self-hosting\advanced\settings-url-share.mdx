---
title: Share Settings via URL - Import and Export Configuration Settings
description: >-
  Learn how to import and export configuration settings for LobeChat via URL. Understand the supported settings, URL format, and parameter schema for keyVaults and languageModel.

tags:
  - Share Settings
  - URL Import
  - URL Export
  - Configuration Settings
  - LobeChat
  - Parameter Schema
---

# Share settings via URL

LobeChat support import settings from external URL to quickly set up LobeChat configuration.

The currently supported settings are:

- `keyVaults`: provider api key and baseURL settings
- `languageModel`: Language model settings

## Import from URL

Use the following URL format to import settings parameters from an external URL:

```plaintext
https://lobehub.com/?settings=<settings object in JSON format>
https://lobehub.com/?settings={"keyVaults":{"openai":{"apiKey":"user-key","baseURL":"https://your-proxy.com/v1"}}}
```

Example of settings in JSON format:

```json
{
  "keyVaults": {
    "openai": {
      "apiKey": "user-key"
    }
  }
}
```

## Export settings to URL

```ts
// Generate settings to export to URL
const settings = {
  keyVaults: {
    openai: {
      apiKey: 'user-key',
      baseURL: 'https://your-proxy.com/v1',
    },
  },
};
// Convert settings to a JSON formatted string
const url = `/?settings=${JSON.stringify(settings)}`;
console.log(url);
// /?settings={"keyVaults":{"openai":{"apiKey":"user-key","baseURL":"https://your-proxy.com/v1"}}}
```

<Callout type={'warning'}>
  LobeChat does not verify the correctness of the settings parameters in the URL, nor provide
  encryption or decryption methods. Please use with caution.
</Callout>

## Parameter schema

### keyVaults

- Property name and type

| Property name | Type                       |
| ------------- | -------------------------- |
| anthropic     | `OpenAICompatibleKeyVault` |
| azure         | `AzureOpenAIKeyVault`      |
| bedrock       | `AWSBedrockKeyVault`       |
| google        | `OpenAICompatibleKeyVault` |
| groq          | `OpenAICompatibleKeyVault` |
| minimax       | `OpenAICompatibleKeyVault` |
| mistral       | `OpenAICompatibleKeyVault` |
| moonshot      | `OpenAICompatibleKeyVault` |
| ollama        | `OpenAICompatibleKeyVault` |
| openai        | `OpenAICompatibleKeyVault` |
| openrouter    | `OpenAICompatibleKeyVault` |
| perplexity    | `OpenAICompatibleKeyVault` |
| togetherai    | `OpenAICompatibleKeyVault` |
| zeroone       | `OpenAICompatibleKeyVault` |
| zhipu         | `OpenAICompatibleKeyVault` |

- Type `OpenAICompatibleKeyVault`

| Property name | Type   | Description                          |
| ------------- | ------ | ------------------------------------ |
| apiKey        | string | The API key for the model provider.  |
| baseURL       | string | The endpoint for the model provider. |

- Type `AzureOpenAIKeyVault`

| Property name | Type   | Description                          |
| ------------- | ------ | ------------------------------------ |
| apiVersion    | string | The API version for Azure OpenAI.    |
| apiKey        | string | The API key for the model provider.  |
| endpoint      | string | The endpoint for the model provider. |

- Type `AWSBedrockKeyVault`

| Property name   | Type   | Description                            |
| --------------- | ------ | -------------------------------------- |
| accessKeyId     | string | The access key ID for AWS Bedrock.     |
| region          | string | The region for AWS Bedrock.            |
| secretAccessKey | string | The secret access key for AWS Bedrock. |

### languageModel

```ts
export type UserModelProviderConfig = Record<string, ProviderConfig>;
```

| Property name | Type             |
| ------------- | ---------------- |
| anthropic     | `ProviderConfig` |
| azure         | `ProviderConfig` |
| bedrock       | `ProviderConfig` |
| google        | `ProviderConfig` |
| groq          | `ProviderConfig` |
| minimax       | `ProviderConfig` |
| mistral       | `ProviderConfig` |
| moonshot      | `ProviderConfig` |
| ollama        | `ProviderConfig` |
| openai        | `ProviderConfig` |
| openrouter    | `ProviderConfig` |
| perplexity    | `ProviderConfig` |
| togetherai    | `ProviderConfig` |
| zeroone       | `ProviderConfig` |
| zhipu         | `ProviderConfig` |

- Type `ProviderConfig`

| Property name       | Type      | Description                                 |                                |
| ------------------- | --------- | ------------------------------------------- | ------------------------------ |
| autoFetchModelLists | boolean   | Whether to automatically fetch model lists. |                                |
| enabled             | boolean   | Whether the model provider is enabled.      |                                |
| enabledModels       | string\[] | null                                        | The IDs of the enabled models. |
| fetchOnClient       | boolean   | Whether to fetch on the client.             |                                |
