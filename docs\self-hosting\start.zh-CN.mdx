---
title: 构建属于自己的 LobeChat - 自选部署平台
description: >-
  选择适合自己的部署平台，构建个性化的 Lobe Chat。支持 Docker、Docker Compose、Netlify、Railway、Repocloud、Sealos、Vercel 和 Zeabur 部署。

tags:
  - Lobe Chat
  - 部署平台
  - Docker
  - Netlify
  - Vercel
  - Sealos
  - 阿里云计算巢
  - 个性化
  - 腾讯云
  - 腾讯轻量云
---

# 构建属于自己的 Lobe Chat

LobeChat 支持多种部署平台，包括 Vercel、Docker、 Docker Compose 、阿里云计算巢 和腾讯轻量云 等，你可以选择适合自己的部署平台进行部署，构建属于自己的 Lobe Chat。

## 快速部署

对于第一次了解 LobeChat 的用户，我们推荐使用客户端数据库的模式快速部署，该模式的优势是一行指令 / 一个按钮即可快捷完成部署，便于你快速上手与体验 LobeChat。

你可以通过以下指南快速部署 LobeChat：

<PlatformCards urlPrefix={'platform'} />

<Callout>
  客户端数据库模式下数据均保留在用户本地，不会跨多端同步，也不支持文件上传、知识库等进阶功能。
</Callout>

## 进阶模式：服务端数据库

针对已经了解 LobeChat 的用户，或需要多端同步的用户，可以自行部署带有服务端数据库的版本，进而获得更完整、功能更强大的 LobeChat。

<Cards rows={1}>
  <Card href={'/zh/docs/self-hosting/server-database'} title={'服务端数据库部署指南'} />
</Cards>
