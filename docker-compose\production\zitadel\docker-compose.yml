name: lobe-chat-database
services:
  postgresql:
    image: pgvector/pgvector:pg16
    container_name: lobe-postgres
    ports:
      - '5432:5432'
    volumes:
      - './data:/var/lib/postgresql/data'
    environment:
      - 'POSTGRES_DB=lobe'
      - 'POSTGRES_PASSWORD=uWNZugjBqixf8dxC'
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5
    restart: always

  minio:
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z
    container_name: lobe-minio
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - './s3_data:/etc/minio/data'
    environment:
      - 'MINIO_ROOT_USER=YOUR_MINIO_USER'
      - 'MINIO_ROOT_PASSWORD=YOUR_MINIO_PASSWORD'
      - 'MINIO_DOMAIN=lobe-s3-api.example.com'
      - 'MINIO_API_CORS_ALLOW_ORIGIN=https://lobe.example.com' # Your LobeChat's domain name.
    restart: always
    command: >
      server /etc/minio/data --address ":9000" --console-address ":9001"


  zitadel:
    restart: always
    image: ghcr.io/zitadel/zitadel:latest
    container_name: lobe-zitadel
    command: start-from-init --config /zitadel-config.yaml --steps /zitadel-init-steps.yaml --masterkey "cft3Tekr/rQBOqwoQSCPoncA9BHbn7QJ" --tlsMode external #MasterkeyNeedsToHave32Characters
    ports:
      - 8080:8080
    volumes:
      - ./zitadel-config.yaml:/zitadel-config.yaml:ro
      - ./zitadel-init-steps.yaml:/zitadel-init-steps.yaml:ro
    depends_on:
      postgresql:
        condition: service_healthy

  lobe:
    image: lobehub/lobe-chat-database
    container_name: lobe-chat
    ports:
      - '3210:3210'
    depends_on:
      - postgresql
      - minio
      - zitadel
    env_file:
      - .env
    restart: always

volumes:
  data:
    driver: local
  s3_data:
    driver: local
