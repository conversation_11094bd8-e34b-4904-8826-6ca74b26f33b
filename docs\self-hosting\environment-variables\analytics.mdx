---
title: Integrating Analytics in LobeChat
description: >-
  Learn how to configure environment variables for Vercel Analytics, Google Analytics, PostHog Analytics, and Umami Analytics in LobeChat for data collection and analysis.

tags:
  - Data Analytics
  - LobeChat
  - Analytics Services
  - Environment Variables
  - Configuration
---

# Data Analytics

We have integrated several free/open-source data analytics services in LobeChat for collecting user usage data. Here are environment variables that you can use.

## Vercel Analytics

### `ENABLE_VERCEL_ANALYTICS`

- Type: Optional
- Description: Used to configure the environment variable for Vercel Analytics. Set to `1` to enable Vercel Analytics.
- Default: `-`
- Example: `1`

### `DEBUG_VERCEL_ANALYTICS`

- Type: Optional
- Description: Used to enable the debug mode for Vercel Analytics.
- Default: `-`
- Example: `1`

## Google Analytics

### `GOOGLE_ANALYTICS_MEASUREMENT_ID`

- Type: Required
- Description: Google Analytics Measurement ID，you can get it from the Google Analytics dashboard. Add it will auto enable Google Analytics.
- Default: `-`
- Example: `G-63LP2TV03T`

## Posthog Analytics

### `POSTHOG_KEY`

- Type: Required
- Description: Set the PostHog project Key. Add it will auto enable PostHog Analytics.
- Default: `-`
- Example: `phc_xxxxxxxx`

### `POSTHOG_HOST`

- Type: Optional
- Description: Set the deployment address of the PostHog service, defaulting to the official SAAS address.
- Default: `https://app.posthog.com`
- Example: `https://example.com`

### `DEBUG_POSTHOG_ANALYTICS`

- Type: Optional
- Description: Enable the debug mode for PostHog.
- Default: `-`
- Example: `1`

## Umami Analytics

### `UMAMI_WEBSITE_ID`

- Type: Required
- Description: Your Umami Website ID. Add it will auto enable Umami Analytics.
- Default: `-`
- Example: `E738D82A-EE9E-4806-A81F-0CA3CAE57F65`

### `UMAMI_SCRIPT_URL`

- Type: Optional
- Description: The URL of the Umami script, defaulting to the script URL provided by Umami Cloud.
- Default: `https://analytics.umami.is/script.js`
- Example: `https://umami.your-site.com/script.js`

## Langfuse Observability

[Langfuse](https://langfuse.com/) is an [open-source](https://github.com/langfuse/langfuse) LLM Observability platform. By enabling the Langfuse integration, you can trace your chat data with Langfuse to develop, monitor, and evaluate the use of your LobeChat.

### `ENABLE_LANGFUSE`

- Type: Required
- Description: Determines if Langfuse analytics is enabled.
- Default: `1`
- Example: `1`

### `LANGFUSE_SECRET_KEY`

- Type: Required
- Description: Langfuse API secret key. Can be created by signing up for [Langfuse Cloud](https://cloud.langfuse.com) or by self-hosting Langfuse.
- Default: \`\`
- Example: `sk-lf-...`

### `LANGFUSE_PUBLIC_KEY`

- Type: Required
- Description: Langfuse API public key. Can be created by signing up for [Langfuse Cloud](https://cloud.langfuse.com) or by self-hosting Langfuse.
- Default: \`\`
- Example: `pk-lf-...`

### `LANGFUSE_HOST`

- Type: Required
- Description: Langfuse host address. Use `https://us.cloud.langfuse.com` if your Langfuse project is in the US data region.
- Default: `https://cloud.langfuse.com`
- Example: `https://cloud.langfuse.com`
