---
title: LobeChat 环境变量配置指南
description: 了解如何使用环境变量自定义设置 LobeChat 部署，包括访问密码、单点登录、basePath 设置等。
tags:
  - LobeChat
  - 环境变量
  - 配置指南
  - 单点登录
  - 插件服务
  - 助手市场
---

# 环境变量

LobeChat 在部署时提供了一些额外的配置项，你可以使用环境变量进行自定义设置。

## 通用变量

### `ACCESS_CODE`

- 类型：可选
- 描述：添加访问 LobeChat 服务的密码，你可以设置一个长密码以防被爆破
- 默认值：-
- 示例：`awCTe)re_r74` or `rtrt_ewee3@09!`

### `API_KEY_SELECT_MODE`

- 类型：可选
- 描述：用于控制多个 API Keys 时，选择 Key 的模式，当前支持 `random` 和 `turn`
- 默认值：`random`
- 示例：`random` 或 `turn`

使用 `random` 模式下，将在多个 API Keys 中随机获取一个 API Key。

使用 `turn` 模式下，将按照填写的顺序，轮询获取得到 API Key。

### `NEXT_PUBLIC_BASE_PATH`

- 类型：可选
- 描述：为 LobeChat 添加 `basePath`
- 默认值: `-`
- 示例: `/test`

### `DEFAULT_AGENT_CONFIG`

- 类型：可选
- 描述：用于配置 LobeChat 默认助理的默认配置。它支持多种数据类型和结构，包括键值对、嵌套字段、数组值等。
- 默认值：`-`
- 示例：`'model=gpt-4-1106-preview;params.max_tokens=300;plugins=search-engine,lobe-image-designer'`

`DEFAULT_AGENT_CONFIG` 用于配置 LobeChat 默认助理的默认配置。它支持多种数据类型和结构，包括键值对、嵌套字段、数组值等。下表详细说明了 `DEFAULT_AGENT_CONFIG` 环境变量的配置项、示例以及相应解释：

| 配置项类型 | 示例                                                                                                                      | 解释                                                    |
| ----- | ----------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------- |
| 基本键值对 | `model=gpt-4`                                                                                                           | 设置模型为 `gpt-4`。                                        |
| 嵌套字段  | `tts.sttLocale=en-US`                                                                                                   | 设置文本到语音服务的语言区域为 `en-US`。                              |
| 数组    | `plugins=search-engine,lobe-image-designer`                                                                             | 启用 `search-engine` 和 `lobe-image-designer` 插件。        |
| 中文逗号  | `plugins=search-engine，lobe-image-designer`                                                                             | 同上，演示支持中文逗号分隔。                                        |
| 多个配置项 | `model=glm-4;provider=zhipu`                                                                                            | 设置模型为 `glm-4` 且模型服务商为 `zhipu`。                        |
| 数字值   | `params.max_tokens=300`, `chatConfig.historyCount=5`                                                                    | 设置最大令牌数为 `300`，设置历史消息条数为 5。                           |
| 布尔值   | `chatConfig.enableAutoCreateTopic=true`,`chatConfig.enableCompressThreshold=true`, `chatConfig.enableHistoryCount=true` | 启用自动创建主题，历史长度压缩阈值，历史记录条数。                             |
| 特殊字符  | `inputTemplate="Hello; I am a bot;"`                                                                                    | 设置输入模板为 `Hello; I am a bot;`。                         |
| 错误处理  | `model=gpt-4;maxToken`                                                                                                  | 忽略无效条目 `maxToken`，仅解析出 `model=gpt-4`。                 |
| 值覆盖   | `model=gpt-4;model=gpt-4-1106-preview`                                                                                  | 如果键重复，使用最后一次出现的值，此处 `model` 的值为 `gpt-4-1106-preview`。 |

相关阅读：

- [\[RFC\] 022 - 环境变量配置默认助手参数](https://github.com/lobehub/lobe-chat/discussions/913)

### `SYSTEM_AGENT`

- 类型：可选
- 描述：用于配置 LobeChat 系统助手（如主题生成、翻译等功能）的模型和供应商。
- 默认值：`-`
- 示例：`default=ollama/deepseek-v3` 或 `topic=openai/gpt-4,translation=anthropic/claude-1`

`SYSTEM_AGENT` 环境变量支持两种配置方式：

1. 使用 `default=供应商/模型` 为所有系统助手设置相同的默认配置
2. 针对特定的系统助手进行单独配置，格式为 `助手名称=供应商/模型`

配置项说明：

| 配置项  | 格式                                              | 解释                                  |
| ---- | ----------------------------------------------- | ----------------------------------- |
| 默认设置 | `default=ollama/deepseek-v3`                    | 为所有系统助手设置默认模型为 ollama 的 deepseek-v3 |
| 特定设置 | `topic=openai/gpt-4`                            | 为主题生成设置特定的供应商和模型                    |
| 混合配置 | `default=ollama/deepseek-v3,topic=openai/gpt-4` | 先为所有助手设置默认值，然后针对特定助手进行覆盖            |

可配置的系统助手及其作用：

| 系统助手    | 键名                | 作用描述                        |
| ------- | ----------------- | --------------------------- |
| 主题生成    | `topic`           | 根据聊天内容自动生成主题名称和摘要           |
| 翻译      | `translation`     | 文本翻译使用的助手                   |
| 元数据生成   | `agentMeta`       | 为助手生成描述性信息和元数据              |
| 历史记录压缩  | `historyCompress` | 压缩和整理长对话的历史记录，优化上下文管理       |
| 知识库查询重写 | `queryRewrite`    | 将后续问题改写为包含上下文的独立问题，提升对话的连贯性 |
| 分支对话    | `thread`          | 自定生成分支对话的标题                 |

### `FEATURE_FLAGS`

- 类型：可选
- 描述：用于控制 LobeChat 的特性功能，支持多个功能标志，使用 `+` 增加一个功能，使用 `-` 来关闭一个功能，多个功能标志之间使用英文逗号 `,` 隔开，最外层建议添加引号 `"` 以避免解析错误。
- 默认值：`-`
- 示例：`"-welcome_suggest"`

具体的内容可以参见 [特性标志](/zh/docs/self-hosting/advanced/feature-flags) 中的说明。

### `PROXY_URL`

- 类型：可选
- 描述：用于指定连接到外部服务的代理 URL。该变量的值在不同的部署环境中应该有所不同。
- 默认值：-
- 示例：`http://127.0.0.1:7890` 或 `socks5://localhost:7891`

<Callout type="info">
  `Docker Desktop` 在 `Windows `和 `macOS `上走的是虚拟机方案，如果是 `localhost` / `127.0.0.1`
  是走到自身容器的 `localhost`，此时请尝试用 `host.docker.internal` 替代 `localhost`。 使用
  `***********************************` 来连接到带认证的代理服务器。
</Callout>

### `ENABLE_PROXY_DNS`

- 类型：可选
- 描述：用于控制是否将 DNS 发送到代理服务器，配置为 `0` 时所有 DNS 查询在本地完成，当你的网络环境无法访问 API 或访问超时，请尝试将该项配置为 `1`。
- 默认值：`0`
- 示例：`1` or `0`

### `SSRF_ALLOW_PRIVATE_IP_ADDRESS`

- 类型：可选
- 描述：是否允许连接私有 IP 地址。在可信环境中可以设置为 true 来关闭 SSRF 防护。
- 默认值：`0`
- 示例：`1` or `0`

### `SSRF_ALLOW_IP_ADDRESS_LIST`

- 类型：可选
- 说明：允许的私有 IP 地址列表，多个 IP 地址用逗号分隔。仅在 `SSRF_ALLOW_PRIVATE_IP_ADDRESS` 为 `0` 时生效。
- 默认值：-
- 示例：`***********,*********`

### `ENABLE_AUTH_PROTECTION`

- 类型：可选
- 说明：控制是否启用路由保护。当设置为 `1` 时，除了公共路由（如 `/api/auth`、`/next-auth/*`、`/login`、`/signup`）外，所有路由都需要认证。当设置为 `0` 或未设置时，只有特定的受保护路由（如 `/settings`、`/files` 等）需要认证。
- 默认值：`0`
- 示例：`1` 或 `0`

## 插件服务

### `PLUGINS_INDEX_URL`

- 类型：可选
- 描述：LobeChat 插件市场的索引地址，如果你自行部署了插件市场的服务，可以使用该变量来覆盖默认的插件市场地址
- 默认值：`https://chat-plugins.lobehub.com`

### `PLUGIN_SETTINGS`

- 类型：可选
- 描述：用于配置插件的设置，使用 `插件名:设置字段=设置值` 的格式来配置插件的设置，多个设置字段用英文分号 `;` 隔开，多个插件设置使用英文逗号`,`隔开。
- 默认值：`-`
- 示例：`search-engine:SERPAPI_API_KEY=xxxxx,plugin-2:key1=value1;key2=value2`

上述示例表示设置 `search-engine` 插件的 `SERPAPI_API_KEY` 为 `xxxxx`，设置 `plugin-2` 的 `key1` 为 `value1`，`key2` 为 `value2`。生成的插件设置配置如下：

```json
{
  "plugin-2": {
    "key1": "value1",
    "key2": "value2"
  },
  "search-engine": {
    "SERPAPI_API_KEY": "xxxxx"
  }
}
```

## 助手市场

### `AGENTS_INDEX_URL`

- 类型：可选
- 描述：LobeChat 助手市场的索引地址，如果你自行部署了助手市场的服务，可以使用该变量来覆盖默认的市场地址
- 默认值：`https://chat-agents.lobehub.com`
