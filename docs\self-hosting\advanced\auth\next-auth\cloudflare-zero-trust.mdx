---
title: Configuring Cloudflare Zero Trust Authentication Service for LobeChat
description: >-
  Learn how to configure Cloudflare Zero Trust for Single Sign-On (SSO) for LobeChat, including creating an application provider, setting environment variables, and deployment instructions.

tags:
  - Cloudflare Zero Trust
  - Single Sign-On (SSO)
  - LobeChat Authentication
  - Environment Variables
  - Deployment Instructions
---

# Configuring Cloudflare Zero Trust Authentication Service

## Cloudflare Zero Trust Configuration Flow

<Steps>
  ### Creating an Application in Cloudflare Zero Trust

  We assume you are already familiar with using the Cloudflare Zero Trust platform and that your LobeChat instance is deployed at `https://chat.example.com`.

  First, we need to visit `https://one.dash.cloudflare.com/` and navigate to `Access - Applications`.

  ![image](https://github.com/user-attachments/assets/4d671a7c-5d94-4c4b-b4fd-71a5a0e9d227)

  Now, on the current page, click `Add an application` and select `SaaS`.

  ![image](https://github.com/user-attachments/assets/3da4c8c4-88c6-40a9-8005-6a0a44aa3b1f)

  In the `Application` text box, enter the application name, such as `LobeChat SSO`. Then click `Select OIDC`, followed by clicking `Add application`.

  ![image](https://github.com/user-attachments/assets/16cd9aef-c87b-48a4-95c0-b666082e7515)

  At this point, you have successfully created a SaaS application named `LobeChat SSO` in Cloudflare Zero Trust.

  Next, we need to enter `https://chat.example.com/api/auth/callback/cloudflare-zero-trust` in the `Redirect URLs` field (note that `chat.example.com` should be replaced with your instance's address).

  ![image](https://github.com/user-attachments/assets/433fdce4-0af5-417f-b80d-163c2d4f02f6)

  Finally, scroll down the page and record the following three values: `Client secret`, `Client ID`, and `Issuer`. You will need these for setting the environment variables when deploying LobeChat.

  ![image](https://github.com/user-attachments/assets/2dd3cde5-fa0d-4f52-b82b-28d9e89379a0)

  ### Configure Environment Variables

  When deploying LobeChat, you need to configure the following environment variables:

  | Environment Variable                | Type     | Description                                                                                                                                                                                                         |
  | ----------------------------------- | -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
  | `NEXT_AUTH_SECRET`                  | Required | The secret used to encrypt Auth.js session tokens. You can generate a secret using the following command: `openssl rand -base64 32`                                                                                 |
  | `NEXT_AUTH_SSO_PROVIDERS`           | Required | Select the SSO provider for LoboChat. Use `cloudflare-zero-trust` for Cloudflare Zero Trust.                                                                                                                        |
  | `AUTH_CLOUDFLARE_ZERO_TRUST_ID`     | Required | The Client ID from the Cloudflare Zero Trust application provider details page                                                                                                                                      |
  | `AUTH_CLOUDFLARE_ZERO_TRUST_SECRET` | Required | The Client Secret from the Cloudflare Zero Trust application provider details page                                                                                                                                  |
  | `AUTH_CLOUDFLARE_ZERO_TRUST_ISSUER` | Required | The OpenID Configuration Issuer from the Cloudflare Zero Trust application provider details page                                                                                                                    |
  | `NEXTAUTH_URL`                      | Required | This URL is used to specify the callback address for Auth.js when performing OAuth authentication. It only needs to be set when the default generated redirect address is incorrect. `https://example.com/api/auth` |

  <Callout type={'tip'}>
    Go to  [📘 Environment Variables](/docs/self-hosting/environment-variable#Cloudflare%20Zero%20Trust) for details about the variables.
  </Callout>
</Steps>

<Callout type={'info'}>
  After a successful deployment, users will be able to use LobeChat by authenticating with the users
  configured in Cloudflare Zero Trust.
</Callout>
