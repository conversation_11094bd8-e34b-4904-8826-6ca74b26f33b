---
title: Using Gitee AI in LobeChat
description: >-
  Learn how to configure and use Gitee AI's API Key in LobeChat to start conversations and interactions.

tags:
  - LobeChat
  - Gitee AI
  - API Key
  - Web UI
---

# Using Gitee AI in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/f9ccce84-4fd4-48ca-9450-40660112d0d7'} />

[Gitee AI](https://ai.gitee.com/) is an open-source platform based on Git code hosting technology, specifically designed for AI application scenarios. It aims to provide developers and businesses with a one-stop solution for AI application development services, including model experience, inference, fine-tuning, and deployment.

This article will guide you on how to use Gitee AI in LobeChat.

<Steps>
  ### Step 1: Obtain the Gitee AI API Key

  - Register and log in to the [Gitee AI official website](https://ai.gitee.com/)
  - Purchase and recharge `Serverless API` from your dashboard

  <Image alt={'Gitee Serverless API'} inStep src={'https://github.com/user-attachments/assets/c77fcf70-9039-49ff-86e4-f8eaa267bbf6'} />

  - In `Settings`, click on the `Access Tokens` section
  - Create a new access token
  - Save the access token in the pop-up window

  <Image alt={'Gitee Serverless API'} inStep src={'https://github.com/user-attachments/assets/0af85438-ac99-4c95-b888-a17e88ede043'} />

  <Callout type={'warning'}>
    Please keep the access token safe as it will only appear once. If you accidentally lose it, you
    will need to create a new one.
  </Callout>

  ### Step 2: Configure Gitee AI in LobeChat

  - Access the `Settings` page in LobeChat
  - Under `AI Service Provider`, find the settings for `Gitee AI`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/eaa2a1fb-41ad-473d-ac10-a39c05886425'} />

  - Enter the obtained API key
  - Select a Gitee AI model for your AI assistant to begin chatting

  <Image alt={'Select Gitee AI Model and Start Chatting'} inStep src={'https://github.com/user-attachments/assets/ab87120c-15ff-4bc7-bb28-4b0b43cfe91a'} />

  <Callout type={'warning'}>
    During usage, you may need to make payments to the API service provider; please refer to Gitee
    AI's relevant pricing policy.
  </Callout>
</Steps>

Now you can start having conversations using the models provided by Gitee AI in LobeChat!
