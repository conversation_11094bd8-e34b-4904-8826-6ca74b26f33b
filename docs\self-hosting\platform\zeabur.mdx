---
title: Deploy LobeChat on Zeabur
description: >-
  Learn how to deploy LobeChat on Zeabur with ease. Follow the provided steps to set up your chat application seamlessly.

tags:
  - Deploy LobeChat
  - Zeabur Deployment
  - OpenAI API Key
  - Custom Domain Binding
---

# Deploy LobeChat with Zeabur

If you want to deploy LobeChat on Zeabur, you can follow the steps below:

## Zeabur Deployment Process

<Steps>
  ### Prepare your OpenAI API Key

  Go to [OpenAI API Key](https://platform.openai.com/account/api-keys) to get your OpenAI API Key.

  ### Click the button below to deploy

  [![][deploy-button-image]][deploy-link]

  ### Once deployed, you can start using it

  ### Bind a custom domain (optional)

  You can use the subdomain provided by <PERSON><PERSON>bur, or choose to bind a custom domain. Currently, the domains provided by Zeabur have not been contaminated, and most regions can connect directly.
</Steps>

# Deploy LobeChat with Zeabur as serverless function

> Note: There are still issues with [middlewares and rewrites of next.js on Zeabur](https://github.com/lobehub/lobe-chat/pull/2775?notification_referrer_id=NT_kwDOAdi2DrQxMDkyODQ4MDc2NTozMDk3OTU5OA#issuecomment-**********), use at your own risk!

Since Zeabur does NOT officially support FREE users deploy containerized service, you may wish to deploy LobeChat as a serverless function service. To deploy LobeChat as a serverless function service on Zeabur, you can follow the steps below:

## Zeabur Deployment Process

<Steps>
  ### Fork LobeChat

  ### Add Zeabur pack config file

  Add a `zbpack.json` configuration file with the following content to the root dir of your fork:

  ```json
  {
    "ignore_dockerfile": true,
    "serverless": true
  }
  ```

  ### Prepare your OpenAI API Key

  Go to [OpenAI API Key](https://platform.openai.com/account/api-keys) to get your OpenAI API Key.

  ### Login to your [Zeabur dashboard](https://dash.zeabur.com)

  If you do not already have an account, you will need to register one.

  ### Create a project and service

  Create a project, then create a service under this project.

  ### Link your fork of LobeChat to the just created Zeabur service.

  When adding service, choose github. This may triger a oAuth depend on varies factors like how you login to Zeabur and if you have already authorized Zeabur to access all your repos

  ### Bind a custom domain (optional)

  You can create a subdomain provided by Zeabur, or choose to bind a custom domain. Currently, the domains provided by Zeabur have not been contaminated, and most regions can connect directly.

  ### Zeabur shall start auto build and you should be able to access it by the domain of your choice after a while.
</Steps>

[deploy-button-image]: https://zeabur.com/button.svg
[deploy-link]: https://zeabur.com/templates/VZGGTI
