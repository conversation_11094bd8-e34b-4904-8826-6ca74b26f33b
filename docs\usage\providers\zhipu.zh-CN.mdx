---
title: 在 LobeChat 中使用智谱 ChatGLM API Key
description: 学习如何在 LobeChat 中配置和使用智谱AI的 API Key，开始与智谱AI提供的模型进行对话。
tags:
  - 智谱AI
  - ChatGLM
  - API Key
  - Web UI
---

# 在 LobeChat 中使用智谱 ChatGLM

<Image alt={'在 LobeChat 中使用 Together AI'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/497e3b20-57ca-4963-b6f4-897c9710c16e'} />

[智谱 AI](https://www.zhipuai.cn/) 是一家源自清华大学计算机系技术成果的高科技公司，成立于 2019 年，专注于自然语言处理、机器学习和大数据分析，致力于在认知智能领域拓展人工智能技术的边界。

本文档将指导你如何在 LobeChat 中使用智谱 AI:

<Steps>
  ### 步骤一：获取智谱 AI 的 API 密钥

  - 访问并登录 [智谱 AI 开放平台](https://open.bigmodel.cn/)
  - 初次登录时系统会自动为你创建好 API 密钥并赠送 25M Tokens 的资源包
  - 进入顶部的 `API密钥` 可以查看你的 API

  <Image alt={'获得智谱AI API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/6d6f2bc5-1407-471d-95a8-fb03193edbdb'} />

  ### 步骤二：在 LobeChat 中配置智谱 AI

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`智谱AI`的设置项

  <Image alt={'LobeChat 中填写智谱AI API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/2afffe79-1d37-423c-9363-f09605d5e640'} />

  - 打开智谱 AI 并填入获得的 API 密钥
  - 为你的助手选择一个智谱 AI 的模型即可开始对话

  <Image alt={' 选择并使用智谱AI模型 '} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/b83da559-73d1-4734-87d5-5e22955a9da2'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考智谱 AI 的费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用智谱 AI 提供的模型进行对话了。
