---
title: Using OpenRouter API Key in LobeChat
description: >-
  Learn how to integrate and utilize OpenRouter's language model APIs in LobeChat. Follow these steps to register, create an API key, recharge credit, and configure OpenRouter for seamless conversations.

tags:
  - OpenRouter
  - LobeChat
  - API Key
  - Web UI
---

# Using OpenRouter in LobeChat

<Image alt={'Using OpenRouter in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/********/40520a43-ac03-4954-8a4d-282fbb946066'} />

[OpenRouter](https://openrouter.ai/) is a service that provides a variety of excellent large language model APIs, supporting models such as OpenAI (including GPT-3.5/4), Anthropic (Claude2, Instant), LLaMA 2, and PaLM Bison.

This document will guide you on how to use OpenRouter in LobeChat:

<Steps>
  ### Step 1: Register and Log in to OpenRouter

  - Visit [OpenRouter.ai](https://openrouter.ai/) and create an account
  - You can log in using your Google account or MetaMask wallet

  <Image alt={'Register OpenRouter'} height={457} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/a024af40-e1d9-4df0-b998-0e6e87cebe5b'} />

  ### Step 2: Create an API Key

  - Go to the `Keys` menu or visit [OpenRouter Keys](https://openrouter.ai/keys) directly
  - Click on `Create Key` to start the creation process
  - Name your API key in the pop-up dialog, for example, "LobeChat Key"
  - Leave the `Credit limit` blank to indicate no amount limit

  <Image alt={'Create OpenRouter Key'} height={460} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/094d701f-ce80-464a-bbbc-0a5ecc8d08e3'} />

  - Copy the API key from the pop-up dialog and save it securely

  <Image alt={'Get OpenRouter Key'} height={519} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/7a012a11-87bd-4366-a567-0ebf6d12ae10'} />

  <Callout type={'warning'}>
    Please store the key securely as it will only appear once. If you lose it accidentally, you will
    need to create a new key.
  </Callout>

  ### Step 3: Recharge Credit

  - Go to the `Credit` menu or visit [OpenRouter Credit](https://openrouter.ai/credits) directly
  - Click on `Manage Credits` to recharge your credit, you can check model prices at [https://openrouter.ai/models](https://openrouter.ai/models)
  - OpenRouter provides some free models that can be used without recharging

  <Image alt={'Recharge OpenRouter Credit'} height={385} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/50b73232-01fc-4ef0-939a-3e06354d1b5a'} />

  ### Step 4: Configure OpenRouter in LobeChat

  - Visit the `Settings` interface in LobeChat
  - Find the setting for `OpenRouter` under `AI Service Provider`
  - Enable OpenRouter and enter the API key you obtained

  <Image alt={'Configure OpenRouter in LobeChat'} height={518} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/5c3898ab-23d7-44c2-bbd9-b255e25e400c'} />

  - Choose an OpenRouter model for your assistant to start the conversation

  <Image alt={'Use OpenRouter model'} height={518} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/77b5feee-3f46-486d-9a36-31ff60efa5e9'} />

  <Callout type={'warning'}>
    You may need to pay the API service provider during usage, please refer to OpenRouter's relevant
    fee policies.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by OpenRouter in LobeChat.
