---
title: 在 LobeChat 中使用 Azure AI API Key
description: 学习如何在 LobeChat 中配置和使用 Azure AI 模型，获取 API 密钥并开始对话。
tags:
  - LobeChat
  - Azure AI
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 Azure AI

<Image alt={'在 LobeChat 中使用 Azure AI '} cover src={'https://github.com/user-attachments/assets/81d0349a-44fe-4dfc-bbc4-8e9a1e09567d'} />

[Azure AI](https://azure.microsoft.com) 是一个基于 Microsoft Azure 云平台的开放式人工智能技术平台，提供包括自然语言处理、机器学习、计算机视觉等多种 AI 功能，帮助企业轻松开发和部署 AI 应用。

本文档将指导你如何在 LobeChat 中接入 Azure AI 的模型:

<Steps>
  ### 步骤一：部署 Azure AI 项目以及模型

  - 首先，访问[Azure AI Foundry](https://ai.azure.com/)并完成注册登录
  - 登录后在首页选择`浏览模型`

  <Image alt={'进入 Azure AI Foundry'} inStep src={'https://github.com/user-attachments/assets/1c6a3e42-8e24-4148-b2c3-0bfe60a8cf77'} />

  - 在模型广场中选择你想要模型
  - 进入模型详情，点击`部署`按钮

  <Image alt={'浏览模型'} inStep src={'https://github.com/user-attachments/assets/3ed3226c-3d4c-49ef-b2c0-8953dac8a92e'} />

  - 在弹出的对话框中创建一个新的项目

  <Image alt={'创建新项目'} inStep src={'https://github.com/user-attachments/assets/199b862a-5de4-4a54-83b2-f4dbf69be902'} />

  <Callout type={'note'}>
    Azure AI Foundry
    的详细配置请参考[官方文档](https://learn.microsoft.com/azure/ai-foundry/model-inference/)
  </Callout>

  ### 步骤二：获取模型的 API Key 及 Endpoint

  - 在已部署的模型详情里，可以查询到 Endpoint 以及 API Key 信息
  - 复制并保存好获取的信息

  <Image alt={'获取 API Key'} inStep src={'https://github.com/user-attachments/assets/30c33426-412d-4dec-b096-317fe5880e79'} />

  ### 步骤三：在 LobeChat 中配置 Azure AI

  - 访问 LobeChat 的 `应用设置` 的 `AI 服务供应商` 界面
  - 在供应商列表中找到 `Azure AI` 的设置项

  <Image alt={'填写 Azure AI  API 密钥'} inStep src={'https://github.com/user-attachments/assets/eb41f77f-ccdd-4a48-a8a2-7badac868c03'} />

  - 打开 Azure AI 服务商并填入获取的 Endpoint 以及 API 密钥

  <Callout type={'warning'}>
    Endpoint 只需要填入前面部分 `https://xxxxxx.services.ai.azure.com/models` 即可
  </Callout>

  - 为你的助手选择一个 Azure AI 模型即可开始对话

  <Image alt={'选择 Azure AI 模型'} inStep src={'https://github.com/user-attachments/assets/a1ba8ec0-e259-4da4-8980-0cf82ca5f52b'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Azure AI 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Azure AI 提供的模型进行对话了。
