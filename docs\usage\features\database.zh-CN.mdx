---
title: LobeChat 支持本地 / 云端数据库存储
description: LobeChat 支持本地 / 云端数据存储，既能实现 Local First，同时支持数据云同步。
tags:
  - LobeChat
  - IndexedDB
  - Postgres
  - Local First
  - 数据云同步
  - 数据库
---

# 本地 / 云端数据存储

<Image alt={'本地 / 云端数据存储'} cover src={'https://github.com/user-attachments/assets/f1697c8b-d1fb-4dac-ba05-153c6295d91d'} />

在现代应用开发中，数据存储方案的选择至关重要。为了满足不同用户的需求，LobeChat 提供了同时支持本地数据库和服务端数据库的灵活配置。无论您是注重数据隐私与掌控，还是追求便捷的使用体验，LobeChat 都能为您提供卓越的解决方案。

## 本地数据库：数据掌控与隐私保护

对于希望对数据有更多掌控感和隐私保护的用户，LobeChat 提供了本地数据库支持。采用 IndexedDB 作为存储解决方案，并结合 dexie 作为 ORM（对象关系映射），LobeChat 实现了高效的数据管理。

同时，我们引入了 CRDT（Conflict-Free Replicated Data Type）技术，确保多端同步功能的无缝体验。这一实验性功能旨在为用户提供更高的自主性和数据安全性。

<Callout type={'info'}>LobeChat 默认采取本地数据库方案，以降低新用户的上手成本。</Callout>

此外，我们尝试引入了 CRDT（Conflict-Free Replicated Data Type）技术，在本地数据库基础上实现了跨端同步。这一实验性功能旨在为用户提供更高的自主性和数据安全性。

## 服务端数据库：便捷与高效的使用体验

对于追求便捷使用体验的用户，LobeChat 支持 PostgreSQL 作为服务端数据库。通过 Drizzle ORM 管理数据，结合 Clerk 进行身份验证，LobeChat 能够为用户提供高效、可靠的服务端数据管理方案。

### 服务端数据库技术栈

- **DB**: PostgreSQL（默认使用 Neon）
- **ORM**: Drizzle ORM
- **Auth**: Clerk
- **Server Router**: tRPC

## 部署方案选择指南

### 1. 本地数据库

本地数据库方案适用于那些希望对数据进行严格控制的用户。通过 LobeChat 的本地数据库支持，您可以在不依赖外部服务器的情况下，安全地存储和管理数据。这一方案特别适合对数据隐私有高要求的用户。

### 2. 服务端数据库

服务端数据库方案则适合那些希望简化数据管理流程，享受便捷使用体验的用户。通过服务端数据库与用户身份验证，LobeChat 能够确保数据的安全性与高效性。如果您希望了解如何配置服务端数据库，请参考我们的[详细文档](/zh/docs/self-hosting/advanced/server-database)。

无论选择本地数据库还是服务端数据库，LobeChat 都能为你提供卓越的用户体验。
