---
title: LobeChat Feature Flags Environment Variables Configuration Guide
description: >-
  Learn how to use environment variables to customize LobeChat's feature flags, including controlling whether a feature is enabled or disabled, or enabling or disabling features for specific user groups or environments as needed.

tags:
  - LobeChat
  - Environment Variables
  - Configuration Guide
  - Feature Flags
---

# Feature Flags

In addition to basic environment variable configuration, LobeChat also offers feature flags to control whether a feature is enabled globally, or to enable or disable features for specific user groups or environments as needed.

## Feature Flags Environment Variable `FEATURE_FLAGS`

- Type: Optional
- Description: Used to control LobeChat's feature functionalities. Supports multiple feature flags, using `+` to add a feature and `-` to disable a feature. Separate multiple feature flags with a comma `,` and enclose the entire value in quotes `"` to avoid parsing errors.
- Default: `-`
- Example: `"-welcome_suggest"`

All features are controlled by the `FEATURE_FLAGS` variable as the sole configuration variable.

You can achieve various feature combinations using the above configuration syntax. All feature flags are Boolean values, enabled with `+` and disabled with `-`.

<Callout type={'tip'}>
  Attention: Unlike the `OPENAI_MODEL_LIST` variable, the `FEATURE_FLAGS` variable does not support
  the `all` keyword. You need to manually control all feature flags (otherwise, they will adopt
  their default values).
</Callout>

| Configuration Item        | Description                                     | Default Value |
| ------------------------- | ----------------------------------------------- | ------------- |
| `webrtc_sync`             | Enables WebRTC sync functionality.              | Disabled      |
| `language_model_settings` | Enables language model settings.                | Enabled       |
| `openai_api_key`          | Allows users to customize the OpenAI API Key.   | Enabled       |
| `openai_proxy_url`        | Allows users to customize the OpenAI proxy URL. | Enabled       |
| `create_session`          | Allows users to create sessions.                | Enabled       |
| `edit_agent`              | Allows users to edit assistants.                | Enabled       |
| `dalle`                   | Enables the DALL-E functionality.               | Enabled       |
| `check_updates`           | Allows checking for updates.                    | Enabled       |
| `welcome_suggest`         | Displays welcome suggestions.                   | Enabled       |
| `market`                  | Enables the assistant market functionality.     | Enabled       |
| `speech_to_text`          | Enables speech-to-text functionality.           | Enabled       |
| `knowledge_base`          | Enables the knowledge base functionality.       | Enabled       |
| `clerk_sign_up`           | Enables the Clerk SignUp functionality.         | Enabled       |

You can always check the [featureFlags](https://github.com/lobehub/lobe-chat/blob/main/src/config/featureFlags/schema.ts) to get the latest list of feature flags.
