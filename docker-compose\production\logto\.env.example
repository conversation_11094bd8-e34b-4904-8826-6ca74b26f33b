# Required: LobeChat domain for tRPC calls
# Ensure this domain is whitelisted in your NextAuth providers and S3 service CORS settings
APP_URL=https://lobe.example.com/

# Postgres related environment variables
# Required: Secret key for encrypting sensitive information. Generate with: openssl rand -base64 32
KEY_VAULTS_SECRET=Kix2wcUONd4CX51E/ZPAd36BqM4wzJgKjPtz2sGztqQ=
# Required: Postgres database connection string
# Format: postgresql://username:password@host:port/dbname
# If using Docker, you can use the container name as the host
DATABASE_URL=******************************************************/lobe

# NEXT_AUTH related environment variables
# Supports auth0, Azure AD, GitHub, Authentik, Zitadel, Logto, etc.
# For supported providers, see: https://lobehub.com/docs/self-hosting/advanced/auth#next-auth
# If you have ACCESS_CODE, please remove it. We use NEXT_AUTH as the sole authentication source
# Required: NextAuth secret key. Generate with: openssl rand -base64 32
NEXT_AUTH_SECRET=NX2kaPE923dt6BL2U8e9oSre5RfoT7hg
# Required: Specify the authentication provider (e.g., Logto)
NEXT_AUTH_SSO_PROVIDERS=logto
# Required: NextAuth URL for callbacks
NEXTAUTH_URL=https://lobe.example.com/api/auth

# NextAuth providers configuration (example using Logto)
# For other providers, see: https://lobehub.com/docs/self-hosting/environment-variables/auth
AUTH_LOGTO_ID=YOUR_LOGTO_ID
AUTH_LOGTO_SECRET=YOUR_LOGTO_SECRET
AUTH_LOGTO_ISSUER=https://lobe-auth-api.example.com/oidc

# Proxy settings (if needed, e.g., when using GitHub as an auth provider)
# HTTP_PROXY=http://localhost:7890
# HTTPS_PROXY=http://localhost:7890

# S3 related environment variables (example using MinIO)
# Required: S3 Access Key ID (for MinIO, invalid until manually created in MinIO UI)
S3_ACCESS_KEY_ID=YOUR_S3_ACCESS_KEY_ID
# Required: S3 Secret Access Key (for MinIO, invalid until manually created in MinIO UI)
S3_SECRET_ACCESS_KEY=YOUR_S3_SECRET_ACCESS_KEY
# Required: S3 Endpoint for server/client connections to S3 API
S3_ENDPOINT=https://lobe-s3-api.example.com
# Required: S3 Bucket (invalid until manually created in MinIO UI)
S3_BUCKET=lobe
# Required: S3 Public Domain for client access to unstructured data
S3_PUBLIC_DOMAIN=https://lobe-s3-api.example.com
# Optional: S3 Enable Path Style
# Use 0 for mainstream S3 cloud providers; use 1 for self-hosted MinIO
# See: https://lobehub.com/docs/self-hosting/advanced/s3#s-3-enable-path-style
S3_ENABLE_PATH_STYLE=1

# Other basic environment variables (as needed)
# See: https://lobehub.com/docs/self-hosting/environment-variables/basic
# Note: For server versions, the API must support embedding models (OpenAI text-embedding-3-small) for file processing
# You don't need to specify this model in OPENAI_MODEL_LIST
# OPENAI_API_KEY=sk-xxxx
# OPENAI_PROXY_URL=https://api.openai.com/v1
# OPENAI_MODEL_LIST=...
