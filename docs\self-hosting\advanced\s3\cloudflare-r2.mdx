---
title: Configuring Cloudflare R2 Storage Service
description: Step-by-step guide to configure Cloudflare R2 for S3 storage.
tags:
  - Cloudflare R2
  - S3 Storage
  - Configuration Guide
---

# Configuring Cloudflare R2 Storage Service

We need to configure an S3 storage service in the server-side database to store files.

## Configuration Steps

<Steps>
  ### Configure and Obtain S3 Bucket

  You need to go to your S3 service provider (such as AWS S3, Cloudflare R2, etc.) and create a new storage bucket. The following is an example using Cloudflare R2 to guide you through the creation process.

  The interface of Cloudflare R2 is shown below:

  <Image alt={'Cloudflare R2 Storage Interface'} src={'https://github.com/lobehub/lobe-chat/assets/********/41f7f677-0153-4a96-b849-5ac9b7ebefee'} />

  When creating the bucket, specify its name and then click create.

  <Image alt={'Create Bucket in R2'} src={'https://github.com/lobehub/lobe-chat/assets/********/9c0d184c-3169-40fa-9115-011cfffb9ca7'} />

  ### Obtain Environment Variables for the Bucket

  In the settings of the R2 storage bucket, you can view the bucket configuration information:

  <Image alt={'View relevant information of the bucket'} src={'https://github.com/lobehub/lobe-chat/assets/********/2ceb210c-eca0-4439-ba27-8734d4ebb3ee'} />

  The corresponding environment variables are:

  ```shell
  # Bucket name
  S3_BUCKET=lobechat
  # Request endpoint of the bucket (note that the path in this link includes the bucket name, which must be removed, or use the link provided on the page for applying S3 API token)
  S3_ENDPOINT=https://0b33a03b5c993fd2f453379dc36558e5.r2.cloudflarestorage.com
  # Access domain of the bucket
  S3_PUBLIC_DOMAIN=https://s3-for-lobechat.your-domain.com
  ```

  <Callout type={'warning'}>
    `S3_ENDPOINT` must have its path removed, otherwise uploaded files cannot be accessed.
  </Callout>

  ### Obtain S3 Key Environment Variables

  You need to obtain the access key for S3 so that the LobeChat server has permission to access the S3 storage service. In R2, you can configure the access key in the account details:

  <Image alt={'View access key of the bucket'} src={'https://github.com/lobehub/lobe-chat/assets/********/be0c95c0-6693-44ee-a490-7e8dfaa8b34d'} />

  Click the button in the upper right corner to create an API token and enter the create API Token page.

  <Image alt={'Create corresponding API token'} src={'https://github.com/lobehub/lobe-chat/assets/********/7b0ea46c-5157-40a8-888f-f47664a4884f'} />

  Since our server-side database needs to read and write to the S3 storage service, the permission needs to be set to `Object Read and Write`, then click create.

  <Image alt={'Configure API token permissions'} src={'https://github.com/lobehub/lobe-chat/assets/********/d6f5a918-7b50-4d6e-83a6-3894ab930ddf'} />

  After creation, you can see the corresponding S3 API token.

  <Image alt={'Copy API token'} src={'https://github.com/lobehub/lobe-chat/assets/********/763b18f9-2b5f-44bb-a479-9b56d46f7397'} />

  The corresponding environment variables are:

  ```shell
  S3_ACCESS_KEY_ID=9998d6757e276cf9f1edbd325b7083a6
  S3_SECRET_ACCESS_KEY=55af75d8eb6b99f189f6a35f855336ea62cd9c4751a5cf4337c53c1d3f497ac2
  ```

  ### Configure Cross-Origin Resource Sharing (CORS)

  Since S3 storage services are often on a separate domain, cross-origin access needs to be configured.

  In R2, you can find the CORS configuration in the settings of the storage bucket:

  <Image alt={'Cross-Origin Settings in Cloudflare R2'} src={'https://github.com/lobehub/lobe-chat/assets/********/ab008be7-26b2-4b78-8bd9-24301bf34d23'} />

  Add a CORS rule to allow requests from your domain (in the previous section, it is `https://your-project.vercel.app`):

  <Image alt={'Configure allowed site domain'} src={'https://github.com/lobehub/lobe-chat/assets/********/dfcc2cb3-2958-4498-a8a4-51bec584fe7d'} />

  Example configuration is as follows:

  ```json
  [
    {
      "AllowedOrigins": ["https://your-project.vercel.app"],
      "AllowedMethods": ["GET", "PUT", "HEAD", "POST", "DELETE"],
      "AllowedHeaders": ["*"]
    }
  ]
  ```

  After configuration, click save.
</Steps>

## Overview of Environment Variables

<Callout type={'warning'}>
  The `https://` in the URL is essential and must be included to maintain the integrity of the URL.
</Callout>

```shell
# S3 Keys
S3_ACCESS_KEY_ID=9998d6757e276cf9f1edbd325b7083a6
S3_SECRET_ACCESS_KEY=55af75d8eb6b99f189f6a35f855336ea62cd9c4751a5cf4337c53c1d3f497ac2

# Bucket Name
S3_BUCKET=lobechat
# Bucket Request Endpoint
S3_ENDPOINT=https://0b33a03b5c993fd2f453379dc36558e5.r2.cloudflarestorage.com
# Public Access Domain for the Bucket
S3_PUBLIC_DOMAIN=https://s3-dev.your-domain.com

# Bucket Region, such as us-west-1. Generally not required, but some service providers may need it.
# S3_REGION=us-west-1
```
