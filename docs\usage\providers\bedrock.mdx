---
title: Using Amazon Bedrock API Key in LobeChat
description: >-
  Learn how to integrate Amazon Bedrock models into LobeChat for AI-powered conversations. Follow these steps to grant access, obtain API keys, and configure Amazon Bedrock.

tags:
  - Amazon Bedrock
  - Claude 3.5 sonnect
  - API keys
  - Claude 3 Opus
  - Web UI
---

# Using Amazon Bedrock in LobeChat

<Image alt={'Using Amazon Bedrock in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/74768b36-28ca-4ec3-a42d-b32abe2c7057'} />

Amazon Bedrock is a fully managed foundational model API service that allows users to access models from leading AI companies (such as AI21 Labs, Anthropic, Cohere, Meta, Stability AI) and Amazon's own foundational models.

This document will guide you on how to use Amazon Bedrock in LobeChat:

<Steps>
  ### Step 1: Grant Access to Amazon Bedrock Models in AWS

  - Access and log in to the [AWS Console](https://console.aws.amazon.com/)
  - Search for `bedrock` and enter the `Amazon Bedrock` service

  <Image alt={'Enter Amazon Bedrock service'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/4e0e87d1-4970-45c5-a9ef-287098f6a198'} />

  - Select `Models access` from the left menu

  <Image alt={'Access Amazon Bedrock model permissions'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/fd06c0aa-4bd3-4f4e-bf2b-38374dfe775d'} />

  - Open model access permissions based on your needs

  <Image alt={'Open model access permissions'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/b695f26a-5bcd-477c-af08-bf03adb717c2'} />

  <Callout type={'info'}>Some models may require additional information from you</Callout>

  ### Step 2: Obtain API Access Keys

  - Continue searching for IAM in the AWS console and enter the IAM service

  <Image alt={'Enter IAM service'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/f9a5a394-c8f8-4567-9d51-cf84811418ca'} />

  - In the `Users` menu, create a new IAM user

  <Image alt={'Create a new IAM user'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/750b5cd1-f16a-4330-b899-c27b28b1e837'} />

  - Enter the user name in the pop-up dialog box

  <Image alt={'Enter user name'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/22ce5a72-bc46-41f3-b402-bda6dee90184'} />

  - Add permissions for this user or join an existing user group to ensure access to Amazon Bedrock

  <Image alt={'Add permissions for the user'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/94836b32-7fc5-45ca-8556-7a23f53b15f9'} />

  - Create an access key for the added user

  <Image alt={'Create an access key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/ac2ed716-d270-43f6-856b-3ff81265f4e6'} />

  - Copy and securely store the access key and secret access key, as they will be needed later

  <Image alt={'Enter IAM service'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/3c64b747-f6f1-4ed2-84bc-bfa8e5d90966'} />

  <Callout type={'warning'}>
    Please securely store the keys as they will only be shown once. If you lose them accidentally, you
    will need to create a new access key.
  </Callout>

  ### Step 3: Configure Amazon Bedrock in LobeChat

  - Access the `Settings` interface in LobeChat
  - Find the setting for `Amazon Bedrock` under `AI Service Provider` and open it

  <Image alt={'Enter Amazon Bedrock keys in LobeChat'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/7468594b-3355-4cb9-85bc-c9dace137653'} />

  - Open Amazon Bedrock and enter the obtained access key and secret access key
  - Choose an Amazon Bedrock model for your assistant to start the conversation

  <Image alt={'Select and use Amazon Bedrock model'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/164b34b5-671e-418d-b34a-3b70f1156d06'} />

  <Callout type={'warning'}>
    You may incur charges while using the API service, please refer to Amazon Bedrock's pricing
    policy.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by Amazon Bedrock in LobeChat.
