# LobeChat 域名
APP_URL=http://localhost:3210

# Postgres 相关，也即 DB 必须的环境变量
# 用于加密敏感信息的密钥，可以使用 openssl rand -base64 32 生成
KEY_VAULTS_SECRET=Kix2wcUONd4CX51E/ZPAd36BqM4wzJgKjPtz2sGztqQ=
# Postgres 数据库连接字符串
DATABASE_URL=******************************************************/lobechat

# NEXT_AUTH 相关
NEXTAUTH_URL=http://localhost:3210/api/auth
NEXT_AUTH_SECRET=NX2kaPE923dt6BL2U8e9oSre5RfoT7hg
NEXT_AUTH_SSO_PROVIDERS=zitadel
# ZiTADEL 鉴权服务提供商部分
# 请参考：https://lobehub.com/zh/docs/self-hosting/advanced/auth/next-auth/zitadel
AUTH_ZITADEL_ID=285945938244075523
AUTH_ZITADEL_SECRET=hkbtzHLaCEIeHeFThym14UcydpmQiEB5JtAX08HSqSoJxhAlVVkyovTuNUZ5TNrT
AUTH_ZITADEL_ISSUER=http://localhost:8080

# MinIO S3 配置
S3_ACCESS_KEY_ID=        
S3_SECRET_ACCESS_KEY=
S3_ENDPOINT=http://localhost:9000
S3_BUCKET=lobe 
S3_PUBLIC_DOMAIN=http://localhost:9000
S3_ENABLE_PATH_STYLE=1
LLM_VISION_IMAGE_USE_BASE64=1

# 其他环境变量，视需求而定，可以参照客户端版本的环境变量配置，注意不要有 ACCESS_CODE
# OPENAI_API_KEY=sk-xxxx
# OPENAI_PROXY_URL=https://api.openai.com/v1
# OPENAI_MODEL_LIST=...
