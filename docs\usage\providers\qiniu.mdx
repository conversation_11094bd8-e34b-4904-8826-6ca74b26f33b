---
title: Using Qiniu API Key in LobeChat
description: >-
  Learn how to integrate and utilize powerful language models developed by <PERSON><PERSON> into LobeChat for various tasks. Follow the steps to obtain an API key and configure it for seamless interaction.

tags:
  - API key
  - Web UI
  - 七牛
  - 七牛云
  - 七牛智能
  - Qiniu
  - DeepSeek
---

# Using <PERSON><PERSON>'s AI Models in LobeChat

<Image alt={"Using <PERSON><PERSON>'s AI Models in LobeChat"} cover src={'https://github.com/user-attachments/assets/3ad2655e-dd20-4534-bf6d-080b3677df86'} />

[<PERSON><PERSON>](https://www.qiniu.com), as a long-established cloud service provider, delivers cost-effective and reliable AI inference services for both real-time and batch processing, with a simple and user-friendly experience.

This document will guide you on how to use <PERSON><PERSON>'s AI Models in LobeChat:

<Steps>
  ### Step 1: [Obtain AI Model API Key](https://developer.qiniu.com/aitokenapi/12884/how-to-get-api-key)

  - Method 1: Using Console

    1. [Register a Qiniu account](https://s.qiniu.com/umqq6n?ref=developer.qiniu.com\&s_path=%2Faitokenapi%2F12884%2Fhow-to-get-api-key)
    2. [Go to the console to obtain your API Key](https://portal.qiniu.com/ai-inference/api-key)
       <Image alt={'Obtain your API Key'} inStep src={'https://static.sufy.com/lobehub/*********-a014769f-262c-4ee4-a727-2c3c45111574.png'} />

  - Method 2: Using Mini Program
    1. Open the Qiniu mini program
    2. Quick login to your account
    3. Click the \[Me] tab in the bottom navigation bar
    4. Click \[My Console]
    5. Navigate to \[AI Inference]
    6. View and copy your API key

  ### Step 2: Configure Qiniu's AI Model Service in LobeChat

  - Visit the `Settings` interface in LobeChat
  - Find the setting for `Qiniu` under `AI Service Provider`

  <Image alt={'Enter API key'} inStep src={'https://static.sufy.com/lobehub/*********-40bd5ec0-c2fe-4397-9ae1-f6d0b9e55287.png'} />

  - Open Qiniu and enter the obtained API key.
  - Choose a Qiniu's model for your AI assistant to start the conversation.

  <Image alt={"Select a Qiniu's model and start conversation"} inStep src={'https://static.sufy.com/lobehub/*********-eadae11f-86e8-4a8d-944d-2f984e257356.png'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider. Please refer to [Qiniu's relevant pricing policies](https://developer.qiniu.com/aitokenapi/12898/ai-token-api-pricing).
  </Callout>
</Steps>

You can now engage in conversations using the models provided by Qiniu in LobeChat.
