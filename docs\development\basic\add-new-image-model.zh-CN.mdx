# 添加新的图像模型

> 了解更多关于 AI 绘画模态的设计，请参考 [AI 绘画模态设计讨论](https://github.com/lobehub/lobe-chat/discussions/7442)

## 参数标准化

所有图像生成模型都必须使用 `src/libs/standard-parameters/index.ts` 中定义的标准参数。这确保了不同 Provider 之间的参数一致性，让用户体验更加统一。

**支持的标准参数**：

- `prompt` (必需)：生成图像的提示词
- `aspectRatio`：宽高比（如 "16:9", "1:1"）
- `width` / `height`：图像宽高
- `size`：预设尺寸（如 "1024x1024"）
- `seed`：随机种子
- `steps`：生成步数
- `cfg`：引导缩放
- 其他参数请查看源文件

## 兼容 OpenAI 请求格式的模型

指的是可以使用 openai SDK 进行请求，并且请求参数和和返回值和 dall-e 以及 gpt-image-x 系列一致。

以智谱的 CogView-4 为例，它是一个兼容 openai 请求格式的模型。你只需要在对应的 ai models 文件 `src/config/aiModels/zhipu.ts` 中，添加模型配置，例如：

```ts
const zhipuImageModels: AIImageModelCard[] = [
  // 添加模型配置
  // https://bigmodel.cn/dev/howuse/image-generation-model/cogview-4
  {
    description:
      'CogView-4 是智谱首个支持生成汉字的开源文生图模型，在语义理解、图像生成质量、中英文字生成能力等方面全面提升，支持任意长度的中英双语输入，能够生成在给定范围内的任意分辨率图像。',
    displayName: 'CogView-4',
    enabled: true,
    id: 'cogview-4',
    parameters: {
      prompt: {
        default: '',
      },
      size: {
        default: '1024x1024',
        enum: ['1024x1024', '768x1344', '864x1152', '1344x768', '1152x864', '1440x720', '720x1440'],
      },
    },
    releasedAt: '2025-03-04',
    type: 'image',
  },
];
```

## 不兼容 OpenAI 请求格式的模型

对于不兼容 OpenAI 格式的图像生成模型，需要实现自定义的 `createImage` 方法。有两种主要实现方式：

### 方式一：使用 OpenAI Compatible Factory

大部分 Provider 都使用 `openaiCompatibleFactory` 来兼容 OpenAI，可以通过传入自定义的 `createImage` 函数（参考 [PR #8534](https://github.com/lobehub/lobe-chat/pull/8534)）。

**实现步骤**：

1. **阅读 Provider 官方文档和标准参数定义**
   - 查看 Provider 的图像生成 API 文档，了解请求格式和响应格式
   - 阅读 `src/libs/standard-parameters/index.ts`，了解支持的参数
   - 在对应的 ai models 文件中增加 image model 配置

2. **实现自定义的 createImage 方法**
   - 创建独立的图像生成函数，接受标准生图参数
   - 将标准参数转换为 Provider 特定的格式
   - 调用 Provider 的生图接口
   - 返回统一格式的响应（imageUrl 和可选的宽高）

3. **补充测试**
   - 编写单元测试覆盖成功场景
   - 测试各种错误情况和边界条件

**代码示例**：

```ts
// src/libs/model-runtime/provider-name/createImage.ts
export const createProviderImage = async (
  payload: ImageGenerationPayload,
  options: any,
): Promise<ImageGenerationResponse> => {
  const { model, prompt, ...params } = payload;

  // 调用 Provider 的原生 API
  const result = await callProviderAPI({
    model,
    prompt,
    // 转换参数格式
    custom_param: params.width,
    // ...
  });

  // 返回统一格式
  return {
    created: Date.now(),
    data: [{ url: result.imageUrl }],
  };
};
```

```ts
// src/libs/model-runtime/provider-name/index.ts
export const LobeProviderAI = openaiCompatibleFactory({
  constructorOptions: {
    // ... 其他配置
  },
  createImage: createProviderImage, // 传入自定义实现
  provider: ModelProvider.ProviderName,
});
```

### 方式二：在 Provider 类中直接实现

如果你的 Provider 有独立的类实现，可以直接在类中添加 `createImage` 方法（参考 [PR #8503](https://github.com/lobehub/lobe-chat/pull/8503)）。

**实现步骤**：

1. **阅读 Provider 官方文档和标准参数定义**
   - 查看 Provider 的图像生成 API 文档
   - 阅读 `src/libs/standard-parameters/index.ts`
   - 在对应的 ai models 文件中增加 image model 配置

2. **在 Provider 类中实现 createImage 方法**
   - 直接在类中添加 `createImage` 方法
   - 处理参数转换和 API 调用
   - 返回统一格式的响应

3. **补充测试**
   - 为新方法编写完整的测试用例

**代码示例**：

```ts
// src/libs/model-runtime/provider-name/index.ts
export class LobeProviderAI {
  async createImage(
    payload: ImageGenerationPayload,
    options?: ChatStreamCallbacks,
  ): Promise<ImageGenerationResponse> {
    const { model, prompt, ...params } = payload;

    // 调用原生 API 并处理响应
    const result = await this.client.generateImage({
      model,
      prompt,
      // 参数转换
    });

    return {
      created: Date.now(),
      data: [{ url: result.url }],
    };
  }
}
```

### 重要注意事项

- **测试要求**：为自定义实现添加完整的单元测试，确保覆盖成功场景和各种错误情况
- **错误处理**：统一使用 `AgentRuntimeError` 进行错误封装，保持错误信息的一致性
