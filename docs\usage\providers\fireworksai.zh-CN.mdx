---
title: 在 LobeChat 中使用 Fireworks AI
description: 学习如何在 LobeChat 中配置和使用 Fireworks AI 的API Key，以便开始对话和交互。
tags:
  - LobeChat
  - Fireworks AI
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 Fireworks AI

<Image cover src={'https://github.com/user-attachments/assets/facdc83c-e789-4649-8060-7f7a10a1b1dd'} />

[Fireworks.ai](https://fireworks.ai/) 是一个高性能的生成式 AI 模型推理平台，允许用户通过其 API 访问和使用各种模型。该平台支持多种模态，包括文本和视觉语言模型，并提供函数调用和 JSON 模式等功能，以增强应用开发的灵活性。

本文将指导你如何在 LobeChat 中使用 Fireworks AI。

<Steps>
  ### 步骤一：获得 Fireworks AI 的 API Key

  - 登录 [Fireworks.ai 控制台](https://fireworks.ai/account/api-keys)
  - 进入 `User` 页面，点击 `API Keys`
  - 创建一个新的 API 密钥

  <Image alt={'创建 API 密钥'} inStep src={'https://github.com/user-attachments/assets/eb027093-5ceb-4a9d-8850-b791fbf69a71'} />

  - 复制并保存生成的 API 密钥

  <Image alt={'保存 API 密钥'} inStep src={'https://github.com/user-attachments/assets/28590f7f-bfee-4215-b50b-8feddbf72366'} />

  <Callout type={'warning'}>
    请安全地存储密钥，因为它只会出现一次。如果您意外丢失它，您将需要创建一个新密钥。
  </Callout>

  ### 步骤二：在 LobeChat 中配置 Fireworks AI

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `Fireworks AI` 的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/12c1957d-f050-4235-95da-d55ddedfa6c9'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 Fireworks AI 的模型即可开始对话

  <Image alt={'选择 Fireworks AI 模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/378df8df-8ec4-436e-8451-fbc52705faee'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Fireworks AI 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Fireworks AI 提供的模型进行对话了。
