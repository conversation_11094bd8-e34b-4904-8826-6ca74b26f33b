---
title: Using AI21 Labs in LobeChat
description: >-
  Learn how to integrate and utilize AI21 Labs's language model APIs in LobeChat.

tags:
  - LobeChat
  - AI21 Labs
  - API Key
  - Web UI
---

# Using AI21 Labs in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/ae03eab5-a319-4d2a-a5f6-1683ab7739ee'} />

[AI21 Labs](https://www.ai21.com/) is a company focused on artificial intelligence, offering advanced language models and API services designed to help developers and businesses leverage natural language processing technology. Their flagship product, the "Jamba" series of models, can perform complex language understanding and generation tasks, widely utilized in fields such as content creation and conversational systems.

This article will guide you on how to use AI21 Labs within LobeChat.

<Steps>
  ### Step 1: Obtain the AI21 Labs API Key

  - Register and log in to [AI21 Studio](https://studio.ai21.com)
  - Click on the `User Avatar` menu, then select `API Key`
  - Copy and save the generated API key

  <Image alt={'Copy API Key'} inStep src={'https://github.com/user-attachments/assets/a42ba52b-491e-4993-8e2f-217aa1776e0f'} />

  ### Step 2: Configure AI21 Labs in LobeChat

  - Go to the `Settings` page in LobeChat
  - Under `AI Service Provider`, find the setting for `AI21 Labs`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/9336d6c5-2a83-4aa9-854e-75e245b665cb'} />

  - Enter the API key you obtained
  - Choose an AI21 Labs model for your AI assistant to begin the conversation

  <Image alt={'Select AI21 Labs Model and Start Conversation'} inStep src={'https://github.com/user-attachments/assets/63e5ced7-1d23-44e1-b933-cc3b5df47eab'} />

  <Callout type={'warning'}>
    During use, you may need to pay the API service provider; please refer to the relevant fee policy
    of AI21 Labs.
  </Callout>
</Steps>

Now you are ready to engage in conversations using the models provided by AI21 Labs in LobeChat.
