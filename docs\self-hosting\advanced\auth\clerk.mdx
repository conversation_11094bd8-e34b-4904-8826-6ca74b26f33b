---
title: Configure Clerk Authentication Service - Step-by-Step Guide
description: >-
  Learn how to set up Clerk authentication with environment variables and webhooks.

tags:
  - Clerk Authentication
  - Environment Variables
  - Webhook Configuration
---

# Configure Clerk Authentication Service

Go to [Clerk](https://clerk.com?utm_source=lobehub\&utm_medium=docs) to register and create an application to obtain the corresponding Public Key and Secret Key.

## Get Environment Variables

<Steps>
  ### Add Public and Private Key Environment Variables

  Add `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` and `CLERK_SECRET_KEY` environment variables. You can click on the "API Keys" in the menu and copy the corresponding values to get these environment variables.

  <Image alt={'Find the corresponding public and private key environment variables in Clerk'} src={'https://github.com/lobehub/lobe-chat/assets/28616219/89883703-7a1a-4a11-b944-5d804544e57c'} />

  The environment variables required for this step are as follows:

  ```shell
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_xxxxxxxxxxx
  CLERK_SECRET_KEY=sk_live_xxxxxxxxxxxxxxxxxxxxxx
  NEXT_PUBLIC_ENABLE_NEXT_AUTH=0
  ```

  ### Create and Configure Webhook in Clerk

  Since we let Clerk fully handle user authentication and management, we need Clerk to notify our application and store the changes in the user lifecycle (create, update, delete). We achieve this by using the Webhook provided by Clerk.

  We need to add an endpoint in Clerk's Webhooks to inform Clerk to send notifications to this endpoint when a user's information changes.

  <Image alt={'Add Webhooks endpoint in Clerk'} src={'https://github.com/lobehub/lobe-chat/assets/28616219/f50f47fb-5e8e-4930-bf4e-8cf6f5b8afb9'} />

  Fill in your project URL in the endpoint, such as `https://your-project.com/api/webhooks/clerk`. Then, subscribe to events by checking the three user events (`user.created`, `user.deleted`, `user.updated`), and click create.

  <Callout type={'warning'}>
    The `https://` in the URL is essential to maintain the integrity of the URL.
  </Callout>

  <Image alt={'Configure URL and user events when adding Clerk Webhooks'} src={'https://github.com/lobehub/lobe-chat/assets/28616219/0249ea56-ab17-4aa9-a56c-9ebd556c2645'} />

  ### Add Webhook Secret to Environment Variables

  After creating, you can find the secret of this Webhook in the bottom right corner:

  <Image alt={'View Clerk Webhooks secret'} src={'https://github.com/lobehub/lobe-chat/assets/28616219/fab4abb2-584b-49de-9340-813382951635'} />

  The environment variable corresponding to this secret is `CLERK_WEBHOOK_SECRET`:

  ```shell
  CLERK_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxxx
  ```
</Steps>

By following these steps, you have successfully configured the Clerk authentication service.
