---
title: Deploy LobeChat on RepoCloud
description: >-
  Learn how to deploy LobeChat on RepoCloud with ease. Follow these steps to prepare your OpenAI API Key, deploy the application, and start using it. Optional: Bind a custom domain for a personalized touch.

tags:
  - Deploy LobeChat
  - RepoCloud Deployment
  - OpenAI API Key
  - Custom Domain Binding
---

# Deploy LobeChat with RepoCloud

If you want to deploy LobeChat on RepoCloud, you can follow the steps below:

## RepoCloud Deployment Process

<Steps>
  ### Prepare your OpenAI API Key

  Go to [OpenAI API Key](https://platform.openai.com/account/api-keys) to get your OpenAI API Key.

  ### One-click to deploy

  [![][deploy-button-image]][deploy-link]

  ### Once deployed, you can start using it

  ### Bind a custom domain (optional)

  You can use the subdomain provided by RepoCloud, or choose to bind a custom domain. Currently, the domains provided by RepoCloud have not been contaminated, and most regions can connect directly.
</Steps>

[deploy-button-image]: https://d16t0pc4846x52.cloudfront.net/deploy.svg
[deploy-link]: https://repocloud.io/details/?app_id=248
