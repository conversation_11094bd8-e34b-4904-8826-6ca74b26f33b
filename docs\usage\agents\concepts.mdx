---
title: Improving User Interaction Efficiency with Agents in LobeChat
description: >-
  Discover how LobeChat's innovative approach with Agents enhances user experience by providing dedicated functional modules for efficient task handling and quick access to historical conversations.

tags:
  - LobeChat
  - Agents
  - User Interaction Efficiency
  - Task Handling
  - Historical Conversations
---

# Topics and Assistants

## ChatGPT and "Topics"

In the official ChatGPT application, there is only the concept of "topics." As shown in the image, the user's historical conversation topics are listed in the sidebar.

<Image
  alt={'ChatGPT and Topics'}
  src={
  'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279602474-fe7cb3f3-8eb7-40d3-a69f-6615393bbd4e.png'
}
/>

However, in our usage, we have found that this model has many issues. For example, the information indexing of historical conversations is too scattered. Additionally, when dealing with repetitive tasks, it is difficult to have a stable entry point. For instance, if I want ChatGPT to help me translate a document, in this model, I would need to constantly create new topics and then set up the translation prompt I had previously created. When there are high-frequency tasks, this will result in a very inefficient interaction format.

## Topics and "Agent"

Therefore, in LobeChat, we have introduced the concept of **Agents**. An agent is a complete functional module, each with its own responsibilities and tasks. Assistants can help you handle various tasks and provide professional advice and guidance.

<Image
  alt={'Topics and Agent'}
  src={
  'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279602489-89893e61-2791-4083-9b57-ed80884ad58b.png'
}
/>

At the same time, we have integrated topics into each agent. The benefit of this approach is that each agent has an independent topic list. You can choose the corresponding agent based on the current task and quickly switch between historical conversation records. This method is more in line with users' habits in common chat software, improving interaction efficiency.
