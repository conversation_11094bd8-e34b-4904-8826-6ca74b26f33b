# 新语种添加指南

LobeChat 使用 [lobe-i18n](https://github.com/lobehub/lobe-cli-toolbox/tree/master/packages/lobe-i18n) 作为 i18n 解决方案，可以在应用中快速添加新的语言支持。

## 添加新的语言支持

为了在 LobeChat 中添加新的语言国际化支持，（例如添加越南语 `vi-VN`)，请按照以下步骤操作：

### 步骤 1: 更新国际化配置文件

1. 打开 `.i18nrc.js` 文件。你可以在项目的根目录中找到此文件。
2. 将新的语言代码添加到配置文件中。例如，为了添加越南语，你需要在配置文件中添加 `'vi-VN'`。

```js
module.exports = {
  // ... 其他配置

  outputLocales: [
    'zh-TW',
    'en-US',
    'ru-RU',
    'ja-JP',
    // ...其他语言

    'vi-VN', // 在数组中添加 'vi-VN'
  ],
};
```

### 步骤 2: 自动翻译语言文件

LobeChat 使用 `lobe-i18n` 工具来自动翻译语言文件，因此不需要手动更新 i18n 文件。

运行以下命令来自动翻译并生成越南语的语言文件：

```bash
npm run i18n
```

这将会利用 `lobe-i18n` 工具来处理语言文件。

### 步骤 3: 提交和审查你的更改

一旦你完成了上述步骤，你需要提交你的更改并创建一个 Pull Request。

请确保你遵循了 LobeChat 的贡献指南，并提供必要的描述来说明你的更改。例如，参考之前的类似 Pull Request [#759](https://github.com/lobehub/lobe-chat/pull/759)。

### 附加信息

- 提交你的 Pull Request 后，请耐心等待项目维护人员的审查。
- 如果遇到任何问题，可以联系 LobeChat 社区寻求帮助。
- 为了更精确的结果，确保你的 Pull Request 是基于最新的主分支，并且与主分支保持同步。

通过遵循上述步骤，你可以成功为 LobeChat 添加新的语言支持，并且确保应用能够为更多用户提供本地化的体验。
