---
title: 在 LobeChat 中使用腾讯云 API Key
description: 学习如何在 LobeChat 中配置和使用腾讯云 AI 模型，获取 API 密钥并开始对话。
tags:
  - LobeChat
  - 腾讯云
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用腾讯云

<Image alt={'在 LobeChat 中使用腾讯云'} cover src={'https://github.com/user-attachments/assets/aa91ca54-65fc-4e33-8c76-999f0a5d2bee'} />

[腾讯云（Tencent Cloud）](https://cloud.tencent.com/)是腾讯公司旗下的云计算服务品牌，专门为企业和开发者提供云计算服务。腾讯云提供了一系列 AI 大模型解决方案，通过这些工具可以稳定高效接入 AI 模型。

本文档将指导你如何在 LobeChat 中接入腾讯云的 AI 模型:

<Steps>
  ### 步骤一：获取腾讯云 API 密钥

  - 首先，访问[腾讯云](https://cloud.tencent.com/)并完成注册登录
  - 进入腾讯云控制台并导航至[知识引擎原子能力](https://console.cloud.tencent.com/lkeap)
  - 开通大模型知识引擎，开通过程需要实名认证

  <Image alt={'进入知识引擎原子能力页面'} inStep src={'https://github.com/user-attachments/assets/22e1a039-5e6e-4c40-8266-19821677618a'} />

  - 在`使用OpenAI SDK方式接入`选项中，点击 `创建 API Key` 按钮，创建一个新的 API Key
  - 在 `API key 管理` 中可以查看和管理已创建的 API Key
  - 复制并保存创建好的 API Key

  ### 步骤二：在 LobeChat 中配置腾讯云

  - 访问 LobeChat 的 `应用设置` 的 `AI 服务供应商` 界面
  - 在供应商列表中找到 `腾讯云` 的设置项

  <Image alt={'填写腾讯云 API 密钥'} inStep src={'https://github.com/user-attachments/assets/a9de7780-d0cb-47d5-ad9c-fcbbec14b940'} />

  - 打开腾讯云服务商并填入获取的 API 密钥
  - 为你的助手选择一个腾讯云模型即可开始对话

  <Image alt={'选择腾讯云模型'} inStep src={'https://github.com/user-attachments/assets/162bc64e-0d34-4a4e-815a-028247b73143'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考腾讯云的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用腾讯云提供的模型进行对话了。
