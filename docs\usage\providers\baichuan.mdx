---
title: Using Baichuan API Key in LobeChat
description: >-
  Learn how to integrate Baichuan AI into LobeChat for enhanced conversational experiences. Follow the steps to configure Baichuan AI and start using its models.

tags:
  - LobeChat
  - Baichuan
  - API Key
  - Web UI
---

# Using Baichuan in LobeChat

<Image alt={'Using Baichuan in LobeChat'} cover src={'https://github.com/user-attachments/assets/d961f2af-47b0-4806-8288-b1e8f7ee8a47'} />

This article will guide you on how to use Baichuan in LobeChat:

<Steps>
  ### Step 1: Obtain Baichuan Intelligent API Key

  - Create a [Baichuan Intelligent](https://platform.baichuan-ai.com/homePage) account
  - Create and obtain an [API key](https://platform.baichuan-ai.com/console/apikey)

  <Image alt={'Create API Key'} inStep src={'https://github.com/user-attachments/assets/8787716c-833e-44ab-b506-922ddb6121de'} />

  ### Step 2: Configure Baichu<PERSON> in LobeChat

  - Visit the `Settings` interface in LobeChat
  - Find the setting for `Baichuan` under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/dec6665a-b3ec-4c50-a57f-7c7eb3160e7b'} />

  - Enter the obtained API key
  - Choose a Baichuan model for your AI assistant to start the conversation

  <Image alt={'Select Baichuan model and start conversation'} inStep src={'https://github.com/user-attachments/assets/bfda556a-d3fc-409f-8647-e718788f2fb8'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to Baichuan's relevant
    pricing policies.
  </Callout>
</Steps>

You can now use the models provided by Baichuan for conversation in LobeChat.
