---
title: 在 LobeChat 中使用 PPIO 派欧云 API Key
description: >-
  学习如何将 PPIO 派欧云的 LLM API 集成到 LobeChat 中。跟随以下步骤注册 PPIO 账号、创建 API Key、并在 LobeChat 中进行设置。

tags:
  - PPIO
  - PPInfra
  - DeepSeek
  - Qwen
  - Llama3
  - API key
  - Web UI
---

# 在 LobeChat 中使用 PPIO 派欧云

<Image alt={'在 LobeChat 中使用 PPIO'} cover src={'https://github.com/user-attachments/assets/d0a5e152-160a-4862-8393-546f4e2e5387'} />

[PPIO 派欧云](https://ppinfra.com/user/register?invited_by=RQIMOC\&utm_source=github_lobechat)提供稳定、高性价比的开源模型 API 服务，支持 DeepSeek 全系列、Llama、Qwen 等行业领先大模型。

本文档将指导你如何在 LobeChat 中使用 PPIO:

<Steps>
  ### 步骤一：注册 PPIO 派欧云账号并登录

  - 访问 [PPIO 派欧云](https://ppinfra.com/user/register?invited_by=RQIMOC\&utm_source=github_lobechat) 并注册账号
  - 注册后，PPIO 会赠送 5 元（约 500 万 tokens）的使用额度

  <Image alt={'注册 PPIO'} height={457} inStep src={'https://github.com/user-attachments/assets/7cb3019b-78c1-48e0-a64c-a6a4836affd9'} />

  ### 步骤二：创建 API 密钥

  - 访问 PPIO 派欧云的 [密钥管理页面](https://ppinfra.com/settings/key-management) ，创建并且复制一个 API 密钥.

  <Image alt={'创建 PPIO API 密钥'} inStep src={'https://github.com/user-attachments/assets/5abcf21d-5a6c-4fc8-8de6-bc47d4d2fa98'} />

  ### 步骤三：在 LobeChat 中配置 PPIO 派欧云

  - 访问 LobeChat 的 `设置` 界面
  - 在 `AI 服务商` 下找到 `PPIO` 的设置项
  - 打开 PPIO 并填入获得的 API 密钥

  <Image alt={'在 LobeChat 中输入 PPIO API 密钥'} inStep src={'https://github.com/user-attachments/assets/9b70b292-6c52-4715-b844-ff5df78d16b9'} />

  - 为你的助手选择一个 PPIO 模型即可开始对话

  <Image alt={'选择并使用 PPIO 模型'} inStep src={'https://github.com/user-attachments/assets/b824b741-f2d8-42c8-8cb9-1266862affa7'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，PPIO 的 API 费用参考[这里](https://ppinfra.com/llm-api?utm_source=github_lobe-chat\&utm_medium=github_readme\&utm_campaign=link)。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 PPIO 派欧云提供的模型进行对话了。
