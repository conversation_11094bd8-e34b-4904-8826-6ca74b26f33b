---
title: 会话基本使用指南 - 大型语言模型交互指南
description: 了解如何使用大型语言模型进行基本交互，包括模型选择、文件/图片上传、温度设置、历史记录设置等。
tags:
  - 大型语言模型
  - LLM
  - 模型选择
  - 文件上传
  - 温度设置
  - 历史记录设置
  - 语音输入
  - 插件设置
  - Token 用量
  - 新建话题
  - 发送按钮
---

# 会话基本使用指南

<Image alt={'基本功能'} cover src={'https://github.com/user-attachments/assets/ca6baea8-d782-4611-8555-7c81078028db'} />

通常情况下，与大型语言模型 (LLMs) 的基本交互可以通过此区域（如上图）提供的基础功能进行。

## 基本功能说明

<Image alt={'基本功能说明'} src={'https://github.com/user-attachments/assets/de9f188e-8a3a-49a0-bec6-ab628cdda7e6'} />

1. **模型选择**：选择当前对话所使用的大型语言模型 (LLM)。模型的设置详见[模型服务商](/zh/docs/usage/providers)。
2. **文件 / 图片上传**：当所选模型支持文件或图片识别功能时，用户可以在与模型的对话中上传文件或图片。
3. **温度设置**：调节模型输出的随机性程度。数值越高，输出结果越随机。详细说明请参考[大语言模型指南](/zh/docs/usage/agents/model)。
4. **历史记录设置**：设定本次对话中模型需要记忆的聊天记录数量。历史记录越长，模型能够记忆的对话内容越多，但同时也会消耗更多的上下文 token。
5. **语音输入**：点击该按钮后，可以将语音转换为文字输入。有关详细信息，请参考[语音文字转换](/zh/docs/usage/foundation/tts-stt)。
6. **插件设置**：选择本次对话中需要启用的插件。有关详细信息，请参考[插件使用](/zh/docs/usage/plugins/basic-usage)。
7. **Token 用量**：显示本次对话的上下文长度以及 Token 消耗情况。
8. **新建话题**：结束当前对话并开启一个新的对话主题。有关详细信息，请参考[话题使用](/zh/docs/usage/agents/topics)。
9. **发送按钮**：将当前输入内容发送至模型。下拉菜单提供额外的发送操作选项。

<Image alt={'发送按钮'} inStep src={'https://github.com/user-attachments/assets/0833184e-91df-4b8e-a88e-99a19c20b86a'} />

<Callout type={'info'}>
  - **发送快捷键**：设置使用 Enter 键或 ⌘ + Enter 键发送消息和换行的快捷方式。 -
    **添加一条 AI 消息**：在对话上下文中手动添加并编辑一条由 AI 角色输入的消息，该操作不会触发模型响应。
  -

  **添加一条用户消息**：将当前输入内容作为用户角色输入的消息添加到对话上下文中，该操作不会触发模型响应。
</Callout>
