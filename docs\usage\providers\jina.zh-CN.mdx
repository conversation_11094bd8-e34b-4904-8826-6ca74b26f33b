---
title: 在 LobeChat 中使用 Jina AI API Key
description: 学习如何在 LobeChat 中配置和使用 Jina AI 模型，获取 API 密钥并开始对话。
tags:
  - LobeChat
  - Jina AI
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 Jina AI

<Image alt={'在 LobeChat 中使用 Jina AI'} cover src={'https://github.com/user-attachments/assets/840442b1-bf56-4a5f-9700-b3608b16a8a5'} />

[Jina AI](https://jina.ai/) 是一家成立于 2020 年的开源神经搜索公司，专注于利用深度学习技术处理多模态数据，提供高效的信息检索解决方案，支持文本、图像、视频等多种数据类型的搜索。

本文档将指导你如何在 LobeChat 中使用 Jina AI:

<Steps>
  ### 步骤一：获取 Jina AI API 密钥

  - 访问 [Jina AI 官方网站](https://jina.ai/)，点击首页的 `API` 按钮

  <Image alt={'获取 Jina AI API 密钥'} inStep src={'https://github.com/user-attachments/assets/5ea37821-4ea8-437c-a15e-3b182d10f19e'} />

  - 在下方的 `API Key` 菜单中找到系统为你生成的 API Key
  - 复制并保存生成的 API Key

  <Callout type={'info'}>
    * Jina AI 会为每个用户提供 1M 免费的 API Token，无需注册即可使用 API \* 如果需要管理 API Key，或为
      API 充值，你需要注册并登录 [Jina AI 控制台](https://jina.ai/api-dashboard/)
  </Callout>

  ### 步骤二：在 LobeChat 中配置 Jina AI

  - 访问 LobeChat 的 `应用设置`界面
  - 在 `AI 服务商` 下找到 `Jina AI` 的设置项

  <Image alt={'填写 Jina AI API 密钥'} inStep src={'https://github.com/user-attachments/assets/1077bee5-b379-4063-b7bd-23b98ec146e2'} />

  - 打开 Jina AI 并填入获取的 API 密钥
  - 为你的助手选择一个 Jina AI 模型即可开始对话

  <Image alt={'选择 Jina AI 模型'} inStep src={'https://github.com/user-attachments/assets/be06e348-8d4c-440c-b59f-b71120f21335'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Jina AI 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Jina AI 提供的模型进行对话了。
