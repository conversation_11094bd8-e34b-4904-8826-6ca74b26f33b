---
title: Using the 360AI in LobeChat
description: Learn how to integrate and utilize 360AI's language model APIs in LobeChat.
tags:
  - LobeChat
  - 360AI
  - API Key
  - Web UI
---

# Using the 360AI in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/e617def1-ce50-4acc-974b-12f5ed592a0e'} />

The [360AI](https://ai.360.com/) is a cognitive general model independently developed by 360 Company, aimed at providing powerful natural language processing capabilities for enterprises and developers. This model has been upgraded to version 4.0 and supports various application scenarios, including conversational services, image generation, vector database services, and more.

This article will guide you on how to use the 360AI in LobeChat.

<Steps>
  ### Step 1: Obtain the 360AI API Key

  - Register and log in to the [360AI API Open Platform](https://ai.360.com/platform/keys)
  - Click on the `API Keys` menu on the left
  - Create an API key and copy it

  <Image alt={'Create API Key'} inStep src={'https://github.com/user-attachments/assets/72da7af1-e180-4759-84a5-a6f6ca28392e'} />

  ### Step 2: Configure 360AI in LobeChat

  - Access the `Settings` interface in LobeChat
  - Under `AI Service Provider`, find the option for `360`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/a53deb11-2c14-441a-8a5c-a0f3a74e2a63'} />

  - Enter the API key you obtained
  - Choose a 360AI model for your AI assistant to start chatting

  <Image alt={'Select 360 Model and Start Chatting'} inStep src={'https://github.com/user-attachments/assets/452d0b48-5ff7-4f42-a46e-68a62b87632b'} />

  <Callout type={'warning'}>
    Please note that you may need to pay the API service provider during use, refer to the relevant
    pricing policy of the 360AI.
  </Callout>
</Steps>

You can now use the models provided by the 360AI for conversations in LobeChat.
