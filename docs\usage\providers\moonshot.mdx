---
title: Using Moonshot AI API Key in LobeChat
description: >-
  Learn how to integrate Moonshot AI into LobeChat for AI-powered conversations. Follow the steps to get the API key, configure Moonshot AI, and start engaging with AI models.

tags:
  - Moonshot AI
  - Web UI
  - API Key
---

# Using Moonshot AI in LobeChat

<Image alt={'Using Moonshot AI in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/********/21b94782-875b-4dee-a572-3c5843f3e1e3'} />

The Moonshot AI API is now available for everyone to use. This document will guide you on how to use [Moonshot AI](https://www.moonshot.cn/) in LobeChat:

<Steps>
  ### Step 1: Get Moonshot AI API Key

  - Apply for your [API key](https://platform.moonshot.cn/console/api-keys)

  <Image alt={'Apply for API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/4e56e080-9b8c-42e1-87e1-11123dbb9067'} />

  ### Step 2: Configure Moonshot AI in LobeChat

  - Visit the `Settings` interface in LobeChat
  - Find the setting for `Moonshot AI` under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/e1b5f84f-015e-437c-98cc-a3431fa3b077'} />

  <Callout type={'warning'}>
    If you are using mistral.ai, your account must have a valid subscription for the API key to work
    properly. Newly created API keys may take 2-3 minutes to become active. If the "Test" button
    fails, please retry after 2-3 minutes.
  </Callout>

  - Enter the API key you obtained
  - Choose a Moonshot AI model for your AI assistant to start the conversation

  <Image alt={'Select Moonshot AI model and start conversation'} inStep src={'https://github.com/lobehub/lobe-chat/assets/********/eb7273f8-f0ed-4b9b-884e-96d29c406cb7'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider according to Moonshot AI's related
    pricing policies.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by Moonshot AI in LobeChat.
