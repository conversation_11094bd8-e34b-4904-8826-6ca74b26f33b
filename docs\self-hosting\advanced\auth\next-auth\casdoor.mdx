---
title: Configuring Casdoor Authentication Service in LobeChat
description: >-
  Learn how to configure the Casdoor authentication service in LobeChat, including deployment, creation, permission settings, and environment variables.

tags:
  - Casdoor Authentication
  - Environment Variable Configuration
  - Single Sign-On
  - LobeChat
---

# Configuring Casdoor Authentication Service

[Casdoor](https://github.com/casdoor/casdoor) is an open-source authentication service that is rich in features and easy to use.

<Callout type={'tip'}>
  If you want to privately deploy Casdoor, we recommend using Docker Compose to deploy it together
  with the LobeChat database version, allowing LobeChat to share the same Postgres instance.
</Callout>

## Casdoor Configuration Process

If you are deploying using a local network IP, the following assumptions apply:

- Your LobeChat database version IP/port is `http://LOBECHAT_IP:3210`.
- You privately deploy Casdoor, and its domain is `http://CASDOOR_IP:8000`.

If you are deploying using a public network, the following assumptions apply:

- Your LobeChat database version domain is `https://lobe.example.com`.
- You privately deploy Casdoor, and its domain is `https://lobe-auth-api.example.com`.

<Steps>
  ### Create a Casdoor Application

  Access your privately deployed Casdoor WebUI (default is `http://localhost:8000/`) to enter the console. The default account is `admin`, and the password is `123`.

  Go to `Authentication` -> `Applications`, create a `LobeChat` application or directly modify the built-in `built-in` application. You can explore other fields, but you must configure at least the following fields:

  - Name, Display Name: `LobeChat`
  - Redirect URLs:
    - Local Development Environment: `http://localhost:3210/api/auth/callback/casdoor`
    - Local Network IP Deployment: `http://LOBECHAT_IP:3210/api/auth/callback/casdoor`
    - Public Network Environment: `https://lobe.example.com/api/auth/callback/casdoor`

  There are also some optional fields that can enhance user experience:

  - Logo: `https://lobehub.com/icon-192x192.png`
  - Form CSS, Form CSS (Mobile):

  ```html
  <style>
    .login-panel {
      padding: 40px 70px 0 70px;
      border-radius: 10px;
      background-color: #ffffff;
      box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;
    }
    .panel-logo {
      width: 64px;
    }
    .login-logo-box {
      margin-top: 20px;
    }

    #parent-area
      > main
      > div
      > div.login-content
      > div.login-panel
      > div.login-form
      > div
      > div
      > button {
      box-shadow: none !important;
      border-radius: 10px !important;
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 150ms;
      border: 1px solid #eee !important;
    }

    @media (max-width: 640px) {
      .login-panel {
        padding: 40px 0 0 0;
        box-shadow: none;
      }
    }
  </style>
  ```

  Then, copy the `Client ID` and `Client Secret` and save them.

  ### Disable User Registration

  Go to `Identity` -> `Applications`, select the `LobeChat` application, and set `Allow Register` to `false`.

  <Callout type={'warning'}>
    Disabling user registration is necessary to prevent users from registering through the Casdoor
    login page.
  </Callout>

  ### Configure Webhook (Optional)

  > Available on Casdoor `>=1.843.0`.

  Configure the Casdoor webhook so that LobeChat can receive notifications when user information is updated.

  Go to `Admin` -> `Webhooks`, add a webhook, and fill in the following fields:

  - URL: `https://lobe.example.com/api/webhooks/casdoor`
  - Method: `POST`
  - Content Type: `application/json`
  - Headers: `casdoor-secret`: `Your Webhook Secret`

  > The secret is generated by yourself, you can visit [https://generate-secret.vercel.app/10](https://generate-secret.vercel.app/10) to generate a 10 bit secret.

  - Event: `update-user`

  Save and Exit, then copy the Webhook secret and fill it in the environment variable \`CASDOOR\_WEBHOOK\_SECRET.

  ### Configure Environment Variables

  Set the obtained `Client ID` and `Client Secret` as `AUTH_CASDOOR_ID` and `AUTH_CASDOOR_SECRET` in the LobeChat environment variables.

  Configure `AUTH_CASDOOR_ISSUER` in the LobeChat environment variables as follows:

  - `http://localhost:8000/` if you are in a local development environment.
  - `http://CASDOOR_IP:8000/` if you are privately deploying Casdoor in a local network.
  - `https://lobe-auth-api.example.com/` if you are deploying Casdoor in a public network environment.

  When deploying LobeChat, you need to configure the following environment variables:

  | Environment Variable      | Type     | Description                                                                                                                                                                                        |
  | ------------------------- | -------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
  | `NEXT_AUTH_SECRET`        | Required | A key for encrypting Auth.js session tokens. You can generate a key using the command: `openssl rand -base64 32`.                                                                                  |
  | `NEXT_AUTH_SSO_PROVIDERS` | Required | Select the single sign-on provider for LobeChat. Fill in `casdoor` for using Casdoor.                                                                                                              |
  | `AUTH_CASDOOR_ID`         | Required | The client ID from the Casdoor application details page.                                                                                                                                           |
  | `AUTH_CASDOOR_SECRET`     | Required | The client secret from the Casdoor application details page.                                                                                                                                       |
  | `AUTH_CASDOOR_ISSUER`     | Required | The OpenID Connect issuer for the Casdoor provider.                                                                                                                                                |
  | `NEXTAUTH_URL`            | Required | This URL specifies the callback address for Auth.js during OAuth verification and needs to be set only if the default generated redirect address is incorrect. `https://lobe.example.com/api/auth` |
  | `CASDOOR_WEBHOOK_SECRET`  | Optional | A key used to verify whether the request sent by Casdoor is legal.                                                                                                                                 |

  <Callout type={'tip'}>
    Visit [📘 Environment Variables](/docs/self-hosting/environment-variables/auth#casdoor) for
    details on related variables.
  </Callout>
</Steps>

<Callout type={'info'}>
  Once deployed successfully, users will be able to authenticate via Casdoor and use LobeChat.
</Callout>
