---
title: Deploying Server-Side Database for LobeChat
description: Learn how to deploy LobeChat's server-side database using Postgres.
tags:
  - LobeChat
  - Server-Side Database
  - Postgres
  - Deployment Guide
---

# Deploying Server-Side Database

LobeChat defaults to using a client-side database (IndexedDB) but also supports deploying a server-side database. LobeChat uses Postgres as the backend storage database.

<Callout>
  PostgreSQL is a powerful open-source relational database management system with high scalability
  and standard SQL support. It provides rich data types, concurrency control, data integrity,
  security, and programmability, making it suitable for complex applications and large-scale data
  management.
</Callout>

This guide will introduce the process and principles of deploying the server-side database version of LobeChat on any platform from a framework perspective, so you can understand both the what and the why, and then deploy according to your specific needs.

If you are already familiar with the complete principles, you can quickly get started by checking the deployment guides for each platform:

<PlatformCards urlPrefix={'server-database'} />

---

For the server-side database version of LobeChat, a normal deployment process typically involves configuring three modules:

1. Database configuration;
2. Authentication service configuration;
3. S3 storage service configuration.

## Configure the Database

Before deployment, make sure you have a Postgres database instance ready. You can choose from the following instances:

- `A.` Use Serverless Postgres instances like Vercel/Neon;
- `B.` Use self-deployed Postgres instances like Docker/Railway/Zeabur, collectively referred to as Node Postgres instances;

<Callout>
  There is a slight difference in the way they are configured in terms of environment variables.
</Callout>

Since we support file-based conversations/knowledge base conversations, we need to install the `pgvector` plugin for Postgres. This plugin provides vector search capabilities and is a key component for LobeChat to implement RAG.

<Steps>
  ### `NEXT_PUBLIC_SERVICE_MODE`

  LobeChat supports both client-side and server-side databases, so we provide an environment variable for switching modes, which is `NEXT_PUBLIC_SERVICE_MODE`, with a default value of `client`.

  For server-side database deployment scenarios, you need to set `NEXT_PUBLIC_SERVICE_MODE` to `server`.

  <Callout type={'info'}>
    In the official `lobe-chat-database` Docker image, this environment variable is already set to
    `server` by default. Therefore, if you deploy using the Docker image, you do not need to configure
    this environment variable again.
  </Callout>

  <Callout type={'tip'}>
    Since environment variables starting with `NEXT_PUBLIC` take effect in the front-end code, they cannot be modified through container runtime injection. (Refer to the `next.js` documentation [Configuring: Environment Variables | Next.js (nextjs.org)](https://nextjs.org/docs/pages/building-your-application/configuring/environment-variables)). This is why we chose to create a separate DB version image.

    If you need to modify variables with the `NEXT_PUBLIC` prefix in a Docker deployment, you must build the image yourself and inject your own `NEXT_PUBLIC` prefixed environment variables during the build.
  </Callout>

  ### `DATABASE_URL`

  The core of configuring the database is to add the `DATABASE_URL` environment variable and fill in the Postgres database connection URL you have prepared. The typical format of the database connection URL is `postgres://username:password@host:port/database`.

  <Callout type={'info'}>
    If you want to enable SSL when connecting to the database, please refer to the
    [documentation](https://stackoverflow.com/questions/14021998/using-psql-to-connect-to-postgresql-in-ssl-mode)
    for setup instructions.
  </Callout>

  ### `DATABASE_DRIVER`

  The `DATABASE_DRIVER` environment variable is used to distinguish between the two types of Postgres database instances, with values of `node` or `neon`.

  To streamline deployment, we have set default values based on the characteristics of different platforms:

  - On the Vercel platform, `DATABASE_DRIVER` defaults to `neon`;
  - In our provided Docker image `lobe-chat-database`, `DATABASE_DRIVER` defaults to `node`.

  Therefore, if you follow the standard deployment methods below, you do not need to manually configure the `DATABASE_DRIVER` environment variable:

  - Vercel + Serverless Postgres
  - Docker image + Node Postgres

  ### `KEY_VAULTS_SECRET`

  Considering that users will store sensitive information such as their API Key and baseURL in the database, we need a key to encrypt this information to prevent leakage in case of a database breach. Hence, the `KEY_VAULTS_SECRET` environment variable is used to encrypt sensitive information like user-stored apikeys.

  <Callout type={'info'}>
    You can generate a random 32-character string as the value of `KEY_VAULTS_SECRET` using `openssl
                                                      rand -base64 32`.
  </Callout>
</Steps>

## Configuring Authentication Services

In the server-side database mode, we need an authentication service to distinguish the identities of different users. There are many well-developed authentication solutions in the open-source community. We have integrated two different authentication services to meet the demands of different scenarios, one is Clerk, and the other is NextAuth.

### Clerk

[Clerk](https://clerk.com?utm_source=lobehub\&utm_medium=docs) is an authentication SaaS service that provides out-of-the-box authentication capabilities with high productization, low integration costs, and a great user experience. For those who offer SaaS products, Clerk is a good choice. Our official [LobeChat Cloud](https://lobechat.com) uses Clerk as the authentication service.

The integration of Clerk is relatively simple, requiring only the configuration of these environment variables:

- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` and `CLERK_SECRET_KEY`, which can be obtained from the Clerk console
- `CLERK_WEBHOOK_SECRET`, which is generated by following these instructions: [Configure Clerk Authentication Service](/docs/self-hosting/advanced/auth/clerk#create-and-configure-webhook-in-clerk).

<Callout type={'tip'}>
  In Vercel deployment mode, we recommend using Clerk as the authentication service for a better
  user experience.
</Callout>

However, this type of authentication relies on Clerk's official service, so there may be some limitations in certain scenarios:

- For example, when using Clerk in China, it may be affected by the network environment.
- Clerk is not suitable for scenarios that require complete private deployment.
- It relies on `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`, which may not be readily usable with public Docker images.

Therefore, for the above scenarios, we also provide NextAuth as an alternative solution.

### NextAuth

NextAuth is an open-source authentication library that supports multiple identity providers, including Auth0, Cognito, GitHub, Google, Facebook, Apple, Twitter, and more. NextAuth itself provides a complete authentication solution, including user registration, login, password recovery, integration with various identity providers, and more.

For information on configuring NextAuth, you can refer to the [Authentication](/docs/self-hosting/advanced/authentication) documentation.

<Callout type={'tip'}>
  In the official Docker image `lobe-chat-database`, we recommend using NextAuth as the
  authentication service.
</Callout>

## Configuring S3 Storage Service

LobeChat has supported multimodal AI conversations since [a long time ago](https://x.com/lobehub/status/1724289575672291782), involving the function of uploading images to large models. In the client-side database solution, image files are stored as binary data directly in the browser's IndexedDB database. However, this solution is not feasible in the server-side database. Storing file-like data directly in Postgres will greatly waste valuable database storage space and slow down computational performance.

The best practice in this area is to use a file storage service (S3) to store image files, which is also the storage solution relied upon for subsequent file uploads/knowledge base functions.

<Callout type={'info'}>
  In this documentation, S3 refers to a compatible S3 storage solution, which supports the Amazon S3
  API-compatible object storage system. Common examples include Cloudflare R2, Alibaba Cloud OSS,
  and self-deployable Minio, all of which support the S3-compatible API.
</Callout>

For detailed configuration guidelines on S3, please refer to [S3 Object Storage](/docs/self-hosting/advanced/s3) for more information.

## Getting Started with Deployment

The above is a detailed explanation of configuring LobeChat with a server-side database. You can configure it according to your actual situation and then choose a deployment platform that suits you to start deployment:

<PlatformCards urlPrefix={'server-database'} />
