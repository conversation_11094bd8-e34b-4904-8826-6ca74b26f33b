---
title: 在 LobeChat 中使用 01.AI 零一万物 API Key
description: >-
  学习如何在 LobeChat 中配置并使用 01.AI 零一万物提供的 AI 模型进行对话。获取 API 密钥、填入设置项、选择模型，开始与 AI 助手交流。

tags:
  - LobeChat
  - 01.AI
  - Zero One AI
  - 零一万物
  - Web UI
  - API密钥
  - 配置指南
---

# 在 LobeChat 中使用零一万物

<Image alt={'在 LobeChat 中使用零一万物'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/4485fbc3-c309-4c4e-83ee-cb82392307a1'} />

[零一万物](https://www.01.ai/)是一家致力于 AI 2.0 大模型技术和应用的全球公司，其发布的千亿参数的 Yi-Large 闭源模型，在斯坦福大学的英语排行 AlpacaEval 2.0 上，与 GPT-4 互有第一。

本文档将指导你如何在 LobeChat 中使用零一万物:

<Steps>
  ### 步骤一：获取零一万物 API 密钥

  - 注册并登录 [零一万物大模型开放平台](https://platform.lingyiwanwu.com/)
  - 进入`工作台`并访问`API Key管理`菜单
  - 系统已为你自动创建了一个 API 密钥，你也可以在此界面创建新的 API 密钥

  <Image alt={'创建零一万物 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/72f165f4-d529-4f01-a3ac-163c66e5ea73'} />

  - 初次使用时需要完成账号认证

  <Image alt={'完成账号认证'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/e6058456-8f9d-40c1-9ae5-1e9d5eeb9476'} />

  - 点击创建好的 API 密钥
  - 在弹出的对话框中复制并保存 API 密钥

  <Image alt={'保存 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/f892fe64-c734-4944-91ff-9916a41bd1c9'} />

  ### 步骤二：在 LobeChat 中配置零一万物

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`零一万物`的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/f539d104-6d64-4cc7-8781-3b36b00d32d0'} />

  - 打开零一万物并填入获得的 API 密钥
  - 为你的 AI 助手选择一个 01.AI 的模型即可开始对话

  <Image alt={'选择01.AI模型并开始对话'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/8bf73498-4649-4c4d-a95b-b68447599781'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考零一万物的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用零一万物提供的模型进行对话了。
