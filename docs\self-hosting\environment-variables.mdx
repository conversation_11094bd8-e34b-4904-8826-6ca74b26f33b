---
title: LobeChat Environment Variables - Customizing Guide
description: >-
  Learn how to customize LobeChat configuration using environment variables for additional features and options.

tags:
  - LobeChat
  - Environment Variables
  - Configuration
  - Customization
---

# Environment Variables

LobeChat provides some additional configuration options when deployed, which can be customized using environment variables.

<Cards>
  <Card href={'environment-variables/basic'} title={'Basic Environment Variables'} />

  <Card href={'environment-variables/model-provider'} title={'Model Service Providers'} />

  <Cards href={'environment-variables/auth'} title={'Authentication'} />

  <Cards href={'environment-variables/s3'} title={'S3 Storage Service'} />

  <Cards href={'environment-variables/analytics'} title={'Data Analytics'} />
</Cards>

## Building a Custom Image with Overridden `NEXT_PUBLIC` Variables

If you need to override `NEXT_PUBLIC` environment variables, you can build a custom Docker image using GitHub Actions without forking the entire LobeChat repository. Here's a guide on how to do this:

1. Create a new GitHub repository for your custom build.

2. In your new repository, create a `.github/workflows` directory.

3. Inside the `.github/workflows` directory, create a file named `build-custom-lobe.yml`:

```yaml
name: Build Custom Image

on:
  workflow_dispatch: # Manual trigger

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository_owner }}/lobe-chat-database # Name of your image

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          repository: lobehub/lobe-chat

      - name: Log in to the Container registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile.database # Change dockerfile if needed
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          # List all variables you need to overwrite
          build-args: |
            NEXT_PUBLIC_BASE_PATH=${{ secrets.NEXT_PUBLIC_BASE_PATH }}
            NEXT_PUBLIC_SERVICE_MODE=${{ secrets.NEXT_PUBLIC_SERVICE_MODE }}
```

4. In your GitHub Repository settings > Secrets and variables > Actions > Repository secrets, add any `NEXT_PUBLIC` variables you want to override

5. Set "Read and write" permissions for workflows in Repository settings > Actions > General > Workflow permissions.

6. To build your custom image, go to the "Actions" tab in your GitHub repository and manually trigger the "Build Custom LobeChat Image" workflow.

This approach allows you to create a custom build with your desired `NEXT_PUBLIC` variables without maintaining a full fork of the LobeChat repository. You can trigger a new build whenever you need to update your custom image.
