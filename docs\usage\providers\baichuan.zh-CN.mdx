---
title: 在 LobeChat 中使用百川 API Key
description: 学习如何在 LobeChat 中配置和使用百川的API Key，以便开始对话和交互。
tags:
  - LobeChat
  - 百川
  - 百川智能
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用百川

<Image alt={'在 LobeChat 中使用百川'} cover src={'https://github.com/user-attachments/assets/d961f2af-47b0-4806-8288-b1e8f7ee8a47'} />

本文将指导你如何在 LobeChat 中使用百川：

<Steps>
  ### 步骤一：获取百川智能 API 密钥

  - 创建一个[百川智能](https://platform.baichuan-ai.com/homePage)账户
  - 创建并获取 [API 密钥](https://platform.baichuan-ai.com/console/apikey)

  <Image alt={'创建 API Key'} inStep src={'https://github.com/user-attachments/assets/8787716c-833e-44ab-b506-922ddb6121de'} />

  ### 步骤二：在 LobeChat 中配置百川

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`百川`的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/dec6665a-b3ec-4c50-a57f-7c7eb3160e7b'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个百川的模型即可开始对话

  <Image alt={'选择百川模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/bfda556a-d3fc-409f-8647-e718788f2fb8'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考百川的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用百川提供的模型进行对话了。
