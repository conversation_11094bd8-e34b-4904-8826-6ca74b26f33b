---
title: Deploy LobeChat on Sealos
description: >-
  Learn how to deploy LobeChat on Sealos with ease. Follow the provided steps to set up LobeChat and start using it efficiently.

tags:
  - Deploy LobeChat
  - Sealos Deployment
  - OpenAI API Key
  - Custom Domain Binding
---

# Deploy LobeChat with Sealos

If you want to deploy LobeChat on Sealos, you can follow the steps below:

## Sealos Deployment Process

<Steps>
  ### Prepare your OpenAI API Key

  Go to [OpenAI](https://platform.openai.com/account/api-keys) to get your OpenAI API Key.

  ### Click the button below to deploy

  [![][deploy-button-image]][deploy-link]

  ### After deployment, you can start using it

  ### Bind a custom domain (optional)

  You can use the subdomain provided by Sealos, or choose to bind a custom domain. Currently, the domains provided by Sealos have not been contaminated, and can be directly accessed in most regions.
</Steps>

[deploy-button-image]: https://raw.githubusercontent.com/labring-actions/templates/main/Deploy-on-Sealos.svg
[deploy-link]: https://template.usw.sealos.io/deploy?templateName=lobe-chat
