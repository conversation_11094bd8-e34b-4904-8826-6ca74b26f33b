---
title: 在 Sealos 上部署 LobeChat
description: 学习如何在 Sealos 上部署 LobeChat，包括准备 OpenAI API Key、点击部署按钮、绑定自定义域名等操作。
tags:
  - Sealos
  - LobeChat
  - OpenAI API Key
  - 部署流程
  - 自定义域名
---

# 使用 Sealos 部署

如果想在 Sealos 上部署 LobeChat，可以按照以下步骤进行操作：

## Sealos 部署流程

<Steps>
  ### 准备好你的 OpenAI API Key

  前往 [OpenAI](https://platform.openai.com/account/api-keys) 获取你的 OpenAI API Key

  ### 点击下方按钮进行部署

  [![][deploy-button-image]][deploy-link]

  ### 部署完毕后，即可开始使用

  ### 绑定自定义域名（可选）

  你可以使用 Sealos 提供的子域名，也可以选择绑定自定义域名。目前 Sealos 提供的域名还未被污染，大多数地区都可以直连。
</Steps>

[deploy-button-image]: https://raw.githubusercontent.com/labring-actions/templates/main/Deploy-on-Sealos.svg
[deploy-link]: https://cloud.sealos.io/?openapp=system-template%3FtemplateName%3Dlobe-chat
