---
title: 在 LobeChat 中使用通义千问 API Key
description: 学习如何在 LobeChat 中配置和使用阿里云的通义千问模型，提供强大的自然语言理解和生成能力。
tags:
  - LobeChat
  - 通义千问
  - DashScope
  - DashScope
  - API key
  - Web UI
---

# 在 LobeChat 中使用通义千问

<Image alt={'在 LobeChat 中使用通义千问'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/4e057b43-1e3e-4e96-a948-7cdbff303dcb'} />

[通义千问](https://tongyi.aliyun.com/)是阿里云自主研发的超大规模语言模型，具有强大的自然语言理解和生成能力。它可以回答各种问题、创作文字内容、表达观点看法、撰写代码等，在多个领域发挥作用。

本文档将指导你如何在 LobeChat 中使用通义千问:

<Steps>
  ### 步骤一：开通 DashScope 模型服务

  - 访问并登录阿里云 [DashScope](https://dashscope.console.aliyun.com/) 平台
  - 初次进入时需要开通 DashScope 服务
  - 若你已开通，可跳过该步骤

  <Image alt={'开通 DashScope 服务'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/4f8d0102-7ca7-4f23-b96f-3fc5cf2cd66e'} />

  ### 步骤二：获取 DashScope API 密钥

  - 进入`API-KEY` 界面，并创建一个 API 密钥

  <Image alt={'创建通义千问 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/eee046cb-189b-4635-ac94-19d50b17a18a'} />

  - 在弹出的对话框中复制 API 密钥，并妥善保存

  <Image alt={'复制通义千问 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/cec2e032-54e1-49b1-a212-4d9736927156'} />

  <Callout type={'warning'}>
    请安全地存储密钥，因为它只会出现一次。如果您意外丢失它，您将需要创建一个新密钥。
  </Callout>

  ### 步骤三：在 LobeChat 中配置通义千问

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`通义千问`的设置项

  <Image alt={'填写 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/c2e6a58b-95eb-4f40-8add-83f4316a719b'} />

  - 打开通义千问并填入获得的 API 密钥
  - 为你的 AI 助手选择一个 Qwen 的模型即可开始对话

  <Image alt={'选择 Qwen 模型并开始对话'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/f4a23c2a-503e-4731-bc4d-922bce0b6039'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考通义千问的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用通义千问提供的模型进行对话了。
