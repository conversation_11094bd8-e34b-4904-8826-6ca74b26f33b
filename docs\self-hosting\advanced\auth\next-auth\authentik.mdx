---
title: Configuring Authentik Authentication Service for LobeChat
description: >-
  Learn how to configure Authentik for Single Sign-On (SSO) for LobeChat, including creating an application provider, setting environment variables, and deployment instructions.

tags:
  - Authentik Configuration
  - Single Sign-On (SSO)
  - LobeChat Authentication
  - Environment Variables
  - Deployment Instructions
---

## Configuring Authentik Authentication Service

## Authentik Configuration Flow

<Steps>
  ### Create an Authentik Application Provider

  In your Authentik instance, use the administrator account to go to **Admin Interface** -> **Applications** -> **Providers** and create a new provider.

  Select **OAuth2/OpenID Provider** as the provider type. Fill in the provider name, select the authentication flow and authorization flow.

  In the `Redirect URL/Origin (regex)` field, fill in:

  ```bash
  https://your-domain/api/auth/callback/authentik
  ```

  <Callout type={'info'}>
    - You can fill in or modify the `Redirect URL/Origin (regex)` later, but make sure the filled in
      URL matches the deployed URL. - Replace `your-domain` with your own domain name
  </Callout>

  <Image alt="Create Authentik Provider" inStep src="https://github.com/lobehub/lobe-chat/assets/********/4244634e-5f68-48d5-aac0-e5f4b06d1c4b" />

  Click **Done**

  After the creation is successful, click **Applications** on the left -> **Create**, fill in the name and Slug, select the provider created in the previous step, and click **Create**.

  After the application provider is created, click the corresponding provider to enter the details page, click **Edit**, and save the `Client ID` and `Client Secret`.

  Copy the URL of `OpenID Configuration Issuer` and save it.

  ### Configure Environment Variables

  When deploying LobeChat, you need to configure the following environment variables:

  | Environment Variable      | Type     | Description                                                                                                                                                                                                         |
  | ------------------------- | -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
  | `NEXT_AUTH_SECRET`        | Required | The secret used to encrypt Auth.js session tokens. You can generate a secret using the following command: `openssl rand -base64 32`                                                                                 |
  | `NEXT_AUTH_SSO_PROVIDERS` | Required | Select the SSO provider for LoboChat. Use `authentik` for Authentik.                                                                                                                                                |
  | `AUTH_AUTHENTIK_ID`       | Required | The Client ID from the Authentik application provider details page                                                                                                                                                  |
  | `AUTH_AUTHENTIK_SECRET`   | Required | The Client Secret from the Authentik application provider details page                                                                                                                                              |
  | `AUTH_AUTHENTIK_ISSUER`   | Required | The OpenID Configuration Issuer from the Authentik application provider details page                                                                                                                                |
  | `NEXTAUTH_URL`            | Required | This URL is used to specify the callback address for Auth.js when performing OAuth authentication. It only needs to be set when the default generated redirect address is incorrect. `https://example.com/api/auth` |

  <Callout type={'tip'}>
    Go to  [📘 Environment Variables](/docs/self-hosting/environment-variable#Authentik) for details about the variables.
  </Callout>
</Steps>

<Callout type={'info'}>
  After a successful deployment, users will be able to use LobeChat by authenticating with the users
  configured in Authentik.
</Callout>
