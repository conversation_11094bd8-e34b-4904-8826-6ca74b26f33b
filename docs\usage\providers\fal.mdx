---
title: Using Fal API Key in LobeChat
description: >-
  Learn how to integrate Fal API Key in LobeChat for AI image and video generation using cutting-edge models like FLUX, Kling, and more.

tags:
  - Fal AI
  - Image Generation
  - Video Generation
  - API Key
  - Web UI
---

# Using Fal in LobeChat

<Image alt={'Using Fal in LobeChat'} cover src={'https://hub-apac-1.lobeobjects.space/docs/f253e749baaa2ccac498014178f93091.png'} />

[Fal.ai](https://fal.ai/) is a lightning-fast inference platform specialized in AI media generation, hosting state-of-the-art models for image and video creation including FLUX, <PERSON>ling, HiDream, and other cutting-edge generative models. This document will guide you on how to use Fal in LobeChat:

<Steps>
  ### Step 1: Obtain Fal API Key

  - Register for a [Fal.ai account](https://fal.ai/).
  - Navigate to [API Keys dashboard](https://fal.ai/dashboard/keys) and click **Add key** to create a new API key.
  - Copy the generated API key and keep it secure; it will only be shown once.

  <Image
    alt={'Open the creation window'}
    inStep
    src={
'https://hub-apac-1.lobeobjects.space/docs/3f3676e7f9c04a55603bc1174b636b45.png'
}
  />

  <Image
    alt={'Create API Key'}
    inStep
    src={
'https://hub-apac-1.lobeobjects.space/docs/214cc5019d9c0810951b33215349136e.png'
}
  />

  <Image
    alt={'Retrieve API Key'}
    inStep
    src={
'https://hub-apac-1.lobeobjects.space/docs/499a447e98dcc79407d56495d0305e2a.png'
}
  />

  ### Step 2: Configure Fal in LobeChat

  - Visit the `Settings` page in LobeChat.
  - Under **AI Service Provider**, locate the **Fal** configuration section.

  <Image alt={'Enter API Key'} inStep src={'https://hub-apac-1.lobeobjects.space/docs/fa056feecba0133c76abe1ad12706c05.png'} />

  - Paste the API key you obtained.
  - Choose a Fal model (e.g. `Flux.1 Schnell`, `Flux.1 Kontext Dev`) for image or video generation.

  <Image alt={'Select Fal model for media generation'} inStep src={'https://hub-apac-1.lobeobjects.space/docs/7560502f31b8500032922103fc22e69b.png'} />

  <Callout type={'warning'}>
    During usage, you may incur charges according to Fal's pricing policy. Please review Fal's
    official pricing before heavy usage.
  </Callout>
</Steps>

You can now use Fal's advanced image and video generation models directly within LobeChat to create stunning visual content.
