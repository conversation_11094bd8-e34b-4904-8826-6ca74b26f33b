---
title: 使用 Docker 部署 LobeChat 数据库
description: 详细步骤教你如何在 Docker 中部署 LobeChat 服务端数据库。
tags:
  - Docker
  - LobeChat
  - 数据库部署
  - Postgres
---

# 使用 Docker 部署服务端数据库版

<div style={{display:"flex", gap: 4}}>
  [![][docker-release-shield]][docker-release-link]

  [![][docker-size-shield]][docker-size-link]

  [![][docker-pulls-shield]][docker-pulls-link]
</div>

<Callout type="info">
  本文已经假定你了解了 LobeChat 服务端数据库版本（下简称 DB
  版）的部署基本原理和流程，因此只包含核心环境变量配置的内容。如果你还不了解 LobeChat DB
  版的部署原理，请先查阅 [使用服务端数据库部署](/zh/docs/self-hosting/server-database) 。
  此外，针对国内的腾讯云储存桶用户，可查询[配置腾讯云 COS
  存储服务](/zh/docs/self-hosting/advanced/s3/tencent-cloud)。
</Callout>

<Callout type="warning">
  由于无法使用 Docker 环境变量暴露 `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`，使用 Docker / Docker Compose
  部署 LobeChat 时，你不能使用 Clerk 作为登录鉴权服务。

  如果你确实需要 Clerk 作为登录鉴权服务，你可以考虑使用 Vercel 部署或者自行构建镜像。
</Callout>

## 在 Linux 服务器上部署

以下是在 Linux 服务器上部署 LobeChat DB 版的流程：

<Steps>
  ### 创建 Postgres 数据库实例

  请按照你自己的诉求创建一个带有 PGVector 插件的 Postgres 数据库实例，例如：

  ```sh
  docker network create pg

  docker run --name my-postgres --network pg -e POSTGRES_PASSWORD=mysecretpassword -p 5432:5432 -d pgvector/pgvector:pg16
  ```

  上述指令会创建一个名为 `my-postgres`，并且网络为 `pg` 的 PG 实例，其中 `pgvector/pgvector:pg16` 是一个 Postgres 16 的镜像，且默认安装了 pgvector 插件。

  <Callout type="info">
    pgvector 插件为 Postgres 提供了向量搜索的能力，是 LobeChat 实现 RAG 的重要构件之一。
  </Callout>

  <Callout type="warning">
    以上指令得到的 pg 实例并没有指定持久化存储位置，因此仅用于测试 /
    演示，生产环境请自行配置持久化存储。
  </Callout>

  ### 创建名为 `lobe-chat.env` 文件用于存放环境变量：

  ```shell
  # 网站域名
  APP_URL=https://your-prod-domain.com

  # DB 必须的环境变量
  # 用于加密敏感信息的密钥，可以使用 openssl rand -base64 32 生成
  KEY_VAULTS_SECRET='jgwsK28dspyVQoIf8/M3IIHl1h6LYYceSYNXeLpy6uk='
  # Postgres 数据库连接字符串
  # 格式：postgres://username:password@host:port/dbname，如果你的 pg 实例为 Docker 容器，请使用容器名
  DATABASE_URL=*****************************************************/postgres

  # NEXT_AUTH 相关，可以使用 auth0、Azure AD、GitHub、Authentik、zitadel 等，如有其他接入诉求欢迎提 PR
  NEXT_AUTH_SECRET=3904039cd41ea1bdf6c93db0db96e250
  NEXT_AUTH_SSO_PROVIDERS=auth0
  NEXTAUTH_URL=https://your-prod-domain.com/api/auth
  AUTH_AUTH0_ID=xxxxxx
  AUTH_AUTH0_SECRET=cSX_xxxxx
  AUTH_AUTH0_ISSUER=https://lobe-chat-demo.us.auth0.com

  # S3 相关
  S3_ACCESS_KEY_ID=xxxxxxxxxx
  S3_SECRET_ACCESS_KEY=xxxxxxxxxx
  # 用于 S3 API 访问的域名
  S3_ENDPOINT=https://xxxxxxxxxx.r2.cloudflarestorage.com
  S3_BUCKET=lobechat
  # 用于外网访问 S3 的公共域名，需配置 CORS
  S3_PUBLIC_DOMAIN=https://s3-for-lobechat.your-domain.com
  # S3_REGION=ap-chengdu # 如果需要指定地域

  # 其他环境变量，视需求而定
  # OPENAI_API_KEY=sk-xxxx
  # OPENAI_PROXY_URL=https://api.openai.com/v1
  # OPENAI_MODEL_LIST=...
  # ...
  ```

  ### 启动 lobe-chat-database docker 镜像

  ```sh
  docker run -it -d -p 3210:3210 --network pg --env-file lobe-chat.env --name lobe-chat-database lobehub/lobe-chat-database
  ```

  你可以使用下述指令检查日志：

  ```sh
  docker logs -f lobe-chat-database
  ```

  如果你在容器中看到了以下日志，则说明已经启动成功：

  ```log
  [Database] Start to migration...
  ✅ database migration pass.
  -------------------------------------
    ▲ Next.js 14.x.x
    - Local:        http://localhost:3210
    - Network:      http://0.0.0.0:3210

   ✓ Starting...
   ✓ Ready in 95ms
  ```
</Steps>

<Callout type="tip">
  在我们官方的 Docker 镜像中，会在启动镜像前自动执行数据库 schema 的 migration
  ，我们的官方镜像承诺「空数据库 ->
  完整表」这一段自动建表的稳定性。因此我们建议你的数据库实例使用一个空表实例，进而省去手动维护表结构或者
  migration 的麻烦。
</Callout>

## 在本地（Mac / Windows） 上使用

LobeChat 的 DB 版也支持直接在本地的 Mac/Windows 本地使用。

在此我们已假设你的本地有一个 5432 端口可用，账号为 `postgres` ，密码是 `mysecretpassword` 的 pg 实例，它在 `localhost:5432` 可用。

那么你需要执行的脚本指令为：

```shell
$ docker run -it -d --name lobe-chat-database -p 3210:3210 \
  -e DATABASE_URL=postgres://postgres:<EMAIL>:5432/postgres \
  -e KEY_VAULTS_SECRET=jgwsK28dspyVQoIf8/M3IIHl1h6LYYceSYNXeLpy6uk= \
  -e NEXT_AUTH_SECRET=3904039cd41ea1bdf6c93db0db96e250 \
  -e NEXT_AUTH_SSO_PROVIDERS=auth0 \
  -e AUTH_AUTH0_ID=xxxxxx \
  -e AUTH_AUTH0_SECRET=cSX_xxxxx \
  -e AUTH_AUTH0_ISSUER=https://lobe-chat-demo.us.auth0.com \
  -e APP_URL=http://localhost:3210 \
  -e NEXTAUTH_URL=http://localhost:3210/api/auth \
  -e S3_ACCESS_KEY_ID=xxxxxxxxxx \
  -e S3_SECRET_ACCESS_KEY=xxxxxxxxxx \
  -e S3_ENDPOINT=https://xxxxxxxxxx.r2.cloudflarestorage.com \
  -e S3_BUCKET=lobechat \
  -e S3_PUBLIC_DOMAIN=https://s3-for-lobechat.your-domain.com \
  lobehub/lobe-chat-database
```

<Callout type="tip">
  `Docker` 在 `Windows` 和 `macOS` 上走的是虚拟机方案，如果使用 `localhost` / `127.0.0.1`
  ，将会走到自身容器的 `localhost`，此时请尝试用 `host.docker.internal` 替代 `localhost`
</Callout>

[docker-pulls-link]: https://hub.docker.com/r/lobehub/lobe-chat-database
[docker-pulls-shield]: https://img.shields.io/docker/pulls/lobehub/lobe-chat-database?color=45cc11&labelColor=black&style=flat-square
[docker-release-link]: https://hub.docker.com/r/lobehub/lobe-chat-database
[docker-release-shield]: https://img.shields.io/docker/v/lobehub/lobe-chat-database?color=369eff&label=docker&labelColor=black&logo=docker&logoColor=white&style=flat-square&sort=semver
[docker-size-link]: https://hub.docker.com/r/lobehub/lobe-chat-database
[docker-size-shield]: https://img.shields.io/docker/image-size/lobehub/lobe-chat-database?color=369eff&labelColor=black&style=flat-square&sort=semver
