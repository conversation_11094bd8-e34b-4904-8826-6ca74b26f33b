---
title: LobeChat 文生图模型使用指南
description: 了解如何在 LobeChat 中使用 DALL-E 和 Midjourney 模型生成图片，配置插件并调用 API。
tags:
  - LobeChat
  - 文生图模型
  - DALL-E
  - Midjourney
  - 插件
  - API
---

# 文生图模型使用指南

LobeChat 通过插件机制支持文本生成图片功能。目前，LobeChat 内置了 DALL-E 插件，支持调用 OpenAI 的 DALL-E 模型进行图片生成。此外，用户还可以安装官方提供的 Midjourney 插件，使用 Midjourney 文生图功能。

## DALL-E 模型

如果您已配置 OpenAI API，可以直接在助手界面启用 DALL-E 插件，并在对话中输入提示词，让 AI 为您生成图片。

<Image alt={'DALL-E 插件'} src={'https://github.com/user-attachments/assets/7b11b795-4dd3-4020-a9ba-a3723d5f1f28'} />

如果 DALL-E 插件不可用，请检查 OpenAI API 密钥是否已正确配置。

## Midjourney 模型

LobeChat 还提供 Midjourney 插件，通过 API 调用 Midjourney 生成图片。请提前在插件商店中安装 Midjourney 插件。

<Image alt={'Midjourney 插件'} src={'https://github.com/user-attachments/assets/5f526846-02cd-4dbc-b6e3-a603fa5ac8e7'} />

<Callout type={'info'}>插件安装请参考[插件使用](/zh/docs/usage/plugins/basic-usage)</Callout>

首次使用 Midjourney 插件时，您需要在插件设置中填写您的 Midjourney API 密钥。

<Image alt={'Midjourney 插件设置'} src={'https://github.com/user-attachments/assets/a5d7a543-aec5-457e-a36c-aa1f82f7bc8a'} />
