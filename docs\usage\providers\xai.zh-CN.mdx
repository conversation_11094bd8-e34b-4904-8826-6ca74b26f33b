---
title: 在 LobeChat 中使用 xAI
description: 学习如何在 LobeChat 中配置和使用 xAI 的 API Key，以便开始对话和交互。
tags:
  - LobeChat
  - xAI
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 xAI

<Image cover src={'https://github.com/user-attachments/assets/cf3bfd44-9c13-4026-95cd-67f54f40ce6c'} />

[xAI](https://x.ai/) 是由埃隆・马斯克于 2023 年成立的一家人工智能公司，旨在探索和理解宇宙的真实本质。该公司的目标是通过人工智能技术解决复杂的科学和数学问题，并推动人工智能的发展。

本文将指导你如何在 LobeChat 中使用 xAI。

<Steps>
  ### 步骤一：获取 xAI 的 API 密钥

  - 注册并登录 [xAI 控制台](https://console.x.ai/)
  - 创建一个 API Token
  - 复制并保存 API Token

  <Image alt={'xAI API'} inStep src={'https://github.com/user-attachments/assets/09c994cf-78f8-46ea-9fef-a06022c0f6d7'} />

  <Callout type={'warning'}>
    妥善保存弹窗中的 API 令牌，它只会出现一次，如果不小心丢失了，你需要重新创建一个 API 令牌。
  </Callout>

  ### 步骤二：在 LobeChat 中配置 xAI

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `xAI` 的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/12863a0e-a1ee-406d-8dee-011b20701fd6'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 xAI 的模型即可开始对话

  <Image alt={'选择 xAI 模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/899a4393-db41-45a6-97ec-9813e1f9879d'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 xAI 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 xAI 提供的模型进行对话了。
