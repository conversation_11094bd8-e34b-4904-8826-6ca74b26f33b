---
title: Using SambaNova API Key in LobeChat
description: Learn how to configure and use SambaNova models in LobeChat, obtain an API key, and start a conversation.
tags:
  - LobeChat
  - SambaNova
  - API Key
  - Web UI
---

# Using <PERSON><PERSON><PERSON><PERSON> in LobeChat

<Image alt={'Using <PERSON><PERSON><PERSON><PERSON> in LobeChat'} cover src={'https://github.com/user-attachments/assets/1028aa1a-6c19-4191-b28a-2020e5637155'} />

[Samba<PERSON>ova](https://sambanova.ai/) is a company based in Palo Alto, California, USA, focused on developing high-performance AI hardware and software solutions. It provides fast AI model training, fine-tuning, and inference capabilities, especially suitable for large-scale generative AI models.

This document will guide you on how to use <PERSON><PERSON><PERSON><PERSON> in LobeChat:

<Steps>
  ### Step 1: Obtain a SambaNova API Key

  - First, you need to register and log in to [SambaNova Cloud](https://cloud.sambanova.ai/)
  - Create an API key in the `APIs` page

  <Image alt={'Obtain a SambaNova API Key'} inStep src={'https://github.com/user-attachments/assets/ed6965c8-6884-4adf-a457-573a96755f55'} />

  - Copy the obtained API key and save it securely

  <Callout type={'warning'}>
    Please save the generated API Key securely, as it will only appear once. If you accidentally lose
    it, you will need to create a new API key.
  </Callout>

  ### Step 2: Configure SambaNova in LobeChat

  - Access the `Application Settings` interface of LobeChat
  - Find the `SambaNova` setting item under `AI Service Provider`

  <Image alt={'Fill in the SambaNova API Key'} inStep src={'https://github.com/user-attachments/assets/328e9755-8da9-4849-8569-e099924822fe'} />

  - Turn on SambaNova and fill in the obtained API key
  - Select a SambaNova model for your assistant to start the conversation

  <Image alt={'Select a SambaNova Model'} inStep src={'https://github.com/user-attachments/assets/6dbf4560-3f62-4b33-9f41-96e12b5087b1'} />

  <Callout type={'warning'}>
    You may need to pay the API service provider during use, please refer to SambaNova's related fee policies.
  </Callout>
</Steps>

Now you can use the models provided by SambaNova in LobeChat to conduct conversations.
