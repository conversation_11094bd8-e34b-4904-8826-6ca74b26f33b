---
title: 在 LobeChat 中配置 ZITADEL 身份验证服务
description: 学习如何在 LobeChat 中配置 ZITADEL 身份验证服务，包括创建应用、配置环境变量等步骤。
tags:
  - ZITADEL
  - 身份验证服务
  - 环境变量配置
  - 单点登录
  - LobeChat
---

# 配置 ZITADEL 身份验证服务

<Steps>
  ### 创建 ZITADEL 应用

  使用具有 [`Project Owner` 角色](https://zitadel.com/docs/guides/manage/console/managers#roles)的账户登录到 ZITADEL 实例控制台，进入（或[创建](https://zitadel.com/docs/guides/manage/console/projects#create-a-project)）该应用所属的项目，点击「创建」按钮创建应用。

  <Image alt="创建 ZITADEL 应用 1" inStep src="https://github.com/lobehub/lobe-chat/assets/8692892/3564110d-bef9-47f3-b775-e5f28b4275b2" />

  填写应用名称，应用类型选择「Web」，点击「继续」。

  <Image alt="创建 ZITADEL 应用 2" inStep src="https://github.com/lobehub/lobe-chat/assets/8692892/12451b47-8dcd-40a9-b18d-2806b07efecc" />

  选择「Code」作为身份验证方式。

  <Image alt="创建 ZITADEL 应用 3" inStep src="https://github.com/lobehub/lobe-chat/assets/8692892/9d5cb651-ad10-47c7-8c8b-2256163c5521" />

  在「重定向 URLs」字段中填写：

  ```
  http(s)://your-domain/api/auth/callback/zitadel
  ```

  <Image alt="创建 ZITADEL 应用 4" inStep src="https://github.com/lobehub/lobe-chat/assets/8692892/1699bf46-0c8d-4238-9eb5-34282bfe529a" />

  <Callout type={'important'}>
    - 可以创建应用后再填写或修改重定向 URL，但请确保填写的 URL 与部署的 URL 一致。

    - 请将 `http(s)://your-domain` 替换为 LobeChat 部署的实际 URL。
  </Callout>

  确认配置并点击「创建」。

  <Image alt="创建 ZITADEL 应用 5" inStep src="https://github.com/lobehub/lobe-chat/assets/8692892/375b3d73-6796-465c-9063-f2762093f763" />

  记录下「ClientId」和「ClientSecret」备用。

  <Image alt="创建 ZITADEL 应用 6" inStep src="https://github.com/lobehub/lobe-chat/assets/8692892/40bb6b4c-18e0-4ae5-abae-ae0cf202cf08" />

  在应用设置页面中，切换到「令牌设置」选项卡，勾选「在 ID Token 中包含用户信息」选项，点击「保存」。

  <Image alt="创建 ZITADEL 应用 7" inStep src="https://github.com/lobehub/lobe-chat/assets/8692892/20a257b5-d086-46f3-b5c2-f76394b11f55" />

  切换到「URLs」选项卡，记录下「issuer」URL。

  <Image alt="创建 ZITADEL 应用 8" inStep src="https://github.com/lobehub/lobe-chat/assets/8692892/79c55d44-8dcb-429c-a072-d3eb014bbceb" />

  ### 配置环境变量

  部署 LobeChat 时，您需要配置以下环境变量：

  | 环境变量                      | 类型 | 描述                                                                                  |
  | ------------------------- | -- | ----------------------------------------------------------------------------------- |
  | `NEXT_AUTH_SECRET`        | 必选 | 用于加密 Auth.js 会话令牌的密钥。您可以使用以下命令生成密钥：`openssl rand -base64 32`                        |
  | `NEXT_AUTH_SSO_PROVIDERS` | 必选 | 为 LobeChat 选择单点登录提供程序。对于 ZITADEL，请填写 `zitadel`。                                     |
  | `AUTH_ZITADEL_ID`         | 必选 | ZITADEL 应用的 Client ID（`ClientId`）。                                                  |
  | `AUTH_ZITADEL_SECRET`     | 必选 | ZITADEL 应用的 Client Secret（`ClientSecret`）。                                          |
  | `AUTH_ZITADEL_ISSUER`     | 必选 | ZITADEL 应用的 OpenID Connect 颁发者（issuer）URL。                                          |
  | `NEXTAUTH_URL`            | 必选 | 该 URL 用于指定 Auth.js 中执行 OAuth 认证的回调地址。仅当默认地址不正确时才需要设置。`https://example.com/api/auth` |

  <Callout type={'tip'}>
    您可以在 [📘 环境变量](/zh/docs/self-hosting/environment-variables/auth#zitadel) 中查看相关变量的详细信息。
  </Callout>
</Steps>

<Callout>部署成功后，用户将能够通过 ZITADEL 中配置的用户进行身份验证并使用 LobeChat。</Callout>
