# Required: LobeChat domain for tRPC calls
# Ensure this domain is whitelisted in your NextAuth providers and S3 service CORS settings
APP_URL=http://localhost:3210

# Postgres related environment variables
# Required: Secret key for encrypting sensitive information. Generate with: openssl rand -base64 32
KEY_VAULTS_SECRET=Kix2wcUONd4CX51E/ZPAd36BqM4wzJgKjPtz2sGztqQ=
# Required: Postgres database connection string
DATABASE_URL=******************************************************/lobechat

# NEXT_AUTH related environment variables
NEXTAUTH_URL=http://localhost:3210/api/auth
NEXT_AUTH_SECRET=NX2kaPE923dt6BL2U8e9oSre5RfoT7hg
NEXT_AUTH_SSO_PROVIDERS=zitadel
# ZiTADEL provider configuration
# Please refer to：https://lobehub.com/zh/docs/self-hosting/advanced/auth/next-auth/zitadel
AUTH_ZITADEL_ID=285945938244075523
AUTH_ZITADEL_SECRET=hkbtzHLaCEIeHeFThym14UcydpmQiEB5JtAX08HSqSoJxhAlVVkyovTuNUZ5TNrT
AUTH_ZITADEL_ISSUER=http://localhost:8080

# MinIO S3 configuration
S3_ACCESS_KEY_ID=        
S3_SECRET_ACCESS_KEY=
S3_ENDPOINT=http://localhost:9000
S3_BUCKET=lobe 
S3_PUBLIC_DOMAIN=http://localhost:9000
S3_ENABLE_PATH_STYLE=1
LLM_VISION_IMAGE_USE_BASE64=1

# Other environment variables, as needed. You can refer to the environment variables configuration for the client version, making sure not to have ACCESS_CODE.
# OPENAI_API_KEY=sk-xxxx
# OPENAI_PROXY_URL=https://api.openai.com/v1
# OPENAI_MODEL_LIST=...
