---
title: Translation of Conversation Records - LobeChat
description: >-
  Learn how to translate conversation content in LobeChat with just one click. Customize translation models for accurate results.

tags:
  - Translation
  - Conversation Translation
  - AI Translation Model
---

# Translation of Conversation Records

<Image alt={'Translation'} cover src={'https://github.com/user-attachments/assets/55230f32-b8dd-47db-a2ba-b3fe7e533dc8'} />

## Translating Conversation Content

LobeChat supports users to translate conversation content into a specified language with just one click. After selecting the target language, LobeChat will use a pre-set AI model for translation and display the translated results in real-time in the chat window.

<Image alt={'Displaying Conversation Translation Results'} src={'https://github.com/user-attachments/assets/868df2eb-0c44-4419-a76a-e173094e1e17'} />

## Translation Model Settings

You can specify the model you wish to use as a translation assistant in the settings.

<Image alt={'Setting Translation Model'} src={'https://github.com/user-attachments/assets/530c7c96-bac3-456d-a429-f60e7d2ade66'} />

- Open the `Settings` panel
- Find the `Translation Settings` option under `System Assistants`
- Specify a model for your `Translation Assistant`
