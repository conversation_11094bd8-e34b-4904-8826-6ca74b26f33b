---
title: 'Major Update: LobeChat Enters the Era of Artifacts'
description: >-
  LobeChat v1.19 brings significant updates, including full feature support for Claude Artifacts, a brand new discovery page design, and support for GitHub Models providers, greatly enhancing the capabilities of the AI assistant.

tags:
  - LobeChat
  - AI Assistant
  - Artifacts
  - GitHub Models
  - Interactive Experience
---

# Major Update: LobeChat Enters the Era of Artifacts

We are excited to announce the official release of LobeChat v1.19! This update introduces several important features that elevate the interactive experience of the AI assistant.

## 🎨 Artifacts Support: Unlocking New Creative Dimensions

In this version, we have nearly fully replicated the core features of Claude Artifacts. Now, you can experience the following in LobeChat:

- SVG graphic generation and display
- HTML page generation and real-time rendering
- Document generation in more formats

It is worth mentioning that the Python code execution feature has also been developed and will be available in future versions. At that time, users will be able to utilize both Claude Artifacts and OpenAI Code Interpreter, significantly enhancing the practicality of the AI assistant.

![Artifacts Feature Showcase](https://github.com/user-attachments/assets/639ed70b-abc5-476f-9eb0-10c739e5a115)

## 🔍 New Discovery Page: Explore More Possibilities

The discovery page has undergone a major upgrade, now featuring a richer variety of content categories:

- AI Assistant Marketplace
- Plugin Showcase
- Model List
- Provider Introductions

This redesign not only increases the information density of the page but also opens a new window for users to explore AI capabilities. In the future, we plan to further expand the functionality of the discovery page, potentially adding:

- Knowledge Base Sharing
- Artifacts Showcases
- Curated Conversation Sharing

## 🚀 GitHub Models Support: More Model Choices

Thanks to community member [@CloudPassenger](https://github.com/CloudPassenger) for their contributions, LobeChat now supports GitHub Models providers. Users simply need to:

1. Prepare a GitHub Personal Access Token (PAT)
2. Configure provider information in the settings
3. Start using free models available on GitHub Models

The addition of this feature greatly expands the range of models available to users, providing more options for AI conversations in different scenarios.

## 🔜 Future Outlook

We will continue to focus on enhancing the functionality and user experience of LobeChat. In upcoming versions, we plan to:

- Improve the Python code execution feature
- Add support for more types of Artifacts
- Expand the content dimensions of the discovery page

Thank you to every user for your support and feedback. Let’s look forward to more surprises from LobeChat together!
