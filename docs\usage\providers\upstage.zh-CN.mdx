---
title: 在 LobeChat 中使用 Upstage
description: 学习如何在 LobeChat 中配置和使用 Upstage 的API Key，以便开始对话和交互。
tags:
  - LobeChat
  - Upstage
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 Upstage

<Image cover src={'https://github.com/user-attachments/assets/14696698-03f7-4856-b36c-9a53997eb12c'} />

[Upstage](https://www.upstage.ai/) 是一个提供 AI 模型和服务的平台，专注于自然语言处理和机器学习应用。它允许开发者通过 API 接入其强大的 AI 功能，支持多种任务，如文本生成、对话系统等。

本文将指导你如何在 LobeChat 中使用 Upstage。

<Steps>
  ### 步骤一：获得 Upstage 的 API Key

  - 注册并登录 [Upstage 控制台](https://console.upstage.ai/home)
  - 进入 `API Keys` 页面
  - 创建一个新的 API 密钥
  - 复制并保存生成的 API 密钥

  <Image alt={'保存 API 密钥'} inStep src={'https://github.com/user-attachments/assets/8a0225e0-16ed-40ce-9cd5-553dda561679'} />

  ### 步骤二：在 LobeChat 中配置 Upstage

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `Upstage` 的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/e89d2a56-4bf0-4bff-ac39-0d44789fa858'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 Upstage 的模型即可开始对话

  <Image alt={'选择 Upstage 模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/88e14294-20a6-47c6-981e-fb65453b57cd'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Upstage 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Upstage 提供的模型进行对话了。
