---
title: 在 LobeChat 中使用 Gitee AI
description: 学习如何在 LobeChat 中配置和使用 Gitee AI 的 API Key，以便开始对话和交互。
tags:
  - LobeChat
  - Gitee AI
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 Gitee AI

<Image cover src={'https://github.com/user-attachments/assets/f9ccce84-4fd4-48ca-9450-40660112d0d7'} />

[Gitee AI](https://ai.gitee.com/) 是一个基于 Git 代码托管技术的开源平台，专为人工智能（AI）应用场景设计。它旨在为开发者和企业提供一站式的 AI 应用开发服务，包括模型体验、推理、微调和部署等功能。

本文将指导你如何在 LobeChat 中使用 Gitee AI。

<Steps>
  ### 步骤一：获取 Gitee AI 的 API 密钥

  - 注册并登录 [Gitee AI 官网](https://ai.gitee.com/)
  - 在工作台中购买并充值 `Serverless API`

  <Image alt={'Gitee Serverless API'} inStep src={'https://github.com/user-attachments/assets/c77fcf70-9039-49ff-86e4-f8eaa267bbf6'} />

  - 在 `设置` 中点击 `访问令牌` 界面
  - 创建一个新的访问令牌
  - 在弹出窗口中保存访问令牌

  <Image alt={'Gitee Serverless API'} inStep src={'https://github.com/user-attachments/assets/0af85438-ac99-4c95-b888-a17e88ede043'} />

  <Callout type={'warning'}>
    妥善保存弹窗中的访问令牌，它只会出现一次，如果不小心丢失了，你需要重新创建一个访问令牌。
  </Callout>

  ### 步骤二：在 LobeChat 中配置 Gitee AI

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `Gitee AI` 的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/eaa2a1fb-41ad-473d-ac10-a39c05886425'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 Gitee AI 的模型即可开始对话

  <Image alt={'选择 Gitee AI 模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/ab87120c-15ff-4bc7-bb28-4b0b43cfe91a'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Gitee AI 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Gitee AI 提供的模型进行对话了。
