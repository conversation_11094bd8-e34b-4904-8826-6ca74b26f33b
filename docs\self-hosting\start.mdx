---
title: Build Your Own LobeChat - Choose Your Deployment Platform
description: >-
  Explore multiple deployment platforms like Vercel, Docker, Docker Compose, and more to deploy LobeChat. Choose the platform that best suits your needs.

tags:
  - Lobe Chat
  - Deployment Platform
  - Vercel
  - Docker
  - Docker Compose
  - Alibaba Cloud
---

# Build Your Own Lobe Chat

LobeChat supports various deployment platforms, including Vercel, Docker, and Docker Compose. You can choose a deployment platform that suits you to build your own Lobe Chat.

## Quick Deployment

For users who are new to LobeChat, we recommend using the client-side database mode for quick deployment. The advantage of this mode is that deployment can be quickly completed with just one command/button, making it easy for you to quickly get started and experience LobeChat.

You can follow the guide below for quick deployment of LobeChat:

<PlatformCards urlPrefix={'platform'} />

<Callout>
  In the client-side database mode, data is stored locally on the user's device, without
  cross-device synchronization, and does not support advanced features such as file uploads and
  knowledge base.
</Callout>

## Advanced Mode: Server-Side Database

For users who are already familiar with LobeChat or need cross-device synchronization, you can deploy a version with a server-side database to access a more complete and powerful LobeChat.

<Cards>
  <Card href={'/docs/self-hosting/server-database'} title={'Server-Side Database Deployment Guide'} />
</Cards>
