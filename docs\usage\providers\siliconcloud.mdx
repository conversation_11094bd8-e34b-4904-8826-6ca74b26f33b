---
title: Using SiliconCloud in LobeChat
description: >-
  Learn how to integrate and utilize SiliconCloud's language model APIs in LobeChat.

tags:
  - LobeChat
  - SiliconCloud
  - API Key
  - Web UI
---

# Using SiliconCloud in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/9a78bbb9-7c96-4f32-9b66-e57f92660410'} />

[SiliconCloud](https://siliconflow.cn/) is an AI service platform based on open-source foundational models, offering a variety of generative AI (GenAI) services.

This article will guide you on how to use SiliconCloud in LobeChat.

<Steps>
  ### Step 1: Obtain the API Key from SiliconCloud

  - Sign up and log in to [SiliconCloud](https://cloud.siliconflow.cn/account/ak)
  - Click on the `API Keys` menu on the left side
  - Create an API Key and copy it

  <Image alt={'Create API Key'} inStep src={'https://github.com/user-attachments/assets/872756dc-305e-4e63-9fb7-60550280fc12'} />

  ### Step 2: Configure SiliconCloud in LobeChat

  - Go to the `Settings` page in LobeChat
  - Under `AI Service Provider`, find the setting for `SiliconFlow`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/4c792f62-5203-4f13-8f23-df228f70d67f'} />

  - Enter the API Key you obtained
  - Choose a SiliconCloud model for your AI assistant to start the conversation

  <Image alt={'Select SiliconCloud Model and Start Conversation'} inStep src={'https://github.com/user-attachments/assets/f4dbbadb-7461-4370-a836-09c487fdd206'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, so please refer to SiliconCloud's
    relevant pricing policy.
  </Callout>
</Steps>

At this point, you can start chatting using the models provided by SiliconCloud in LobeChat.
