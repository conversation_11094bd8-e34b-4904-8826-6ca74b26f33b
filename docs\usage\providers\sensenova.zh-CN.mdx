---
title: 在 LobeChat 中使用商汤日日新
description: 学习如何在 LobeChat 中配置和使用商汤日日新的 API Key，以便开始对话和交互。
tags:
  - LobeChat
  - 商汤日日新
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用商汤日日新

<Image cover src={'https://github.com/user-attachments/assets/420379cd-d8a4-4ab3-9a46-75dcc3d56920'} />

[商汤日日新](https://platform.sensenova.cn/home) 是商汤科技（SenseTime）推出的一个大模型体系，旨在推动人工智能（AI）技术的快速迭代和应用落地。

本文将指导你如何在 LobeChat 中使用商汤日日新。

<Steps>
  ### 步骤一：获取商汤日日新的 API 密钥

  - 注册并登录 [万象模型开发平台](https://www.sensecore.cn/product/aistudio)
  - 在产品菜单中找到 `日日新大模型` 并开通服务

  <Image alt={'开通日日新大模型'} inStep src={'https://github.com/user-attachments/assets/c6319e83-c4e7-48cf-9625-2edfc4aa77b3'} />

  - 进入 [AccessKey 访问密钥](https://console.sensecore.cn/iam/Security/access-key) 页面
  - 创建一个访问密钥
  - 在弹出窗口中保存访问密钥 ID 和令牌

  <Image alt={'保存访问密钥'} inStep src={'https://github.com/user-attachments/assets/f9f7ed26-e506-4c52-a118-e0bb5e0918db'} />

  <Callout type={'warning'}>
    妥善保存弹窗中的访问密钥，它只会出现一次，如果不小心丢失了，你需要重新创建一个访问密钥。
  </Callout>

  ### 步骤二：在 LobeChat 中配置商汤日日新

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `商汤日日新` 的设置项

  <Image alt={'填入访问密钥'} inStep src={'https://github.com/user-attachments/assets/0c73c453-6ee3-4f90-bc5d-119c52c38fef'} />

  - 填入获得的 `AccessKey ID` 和 `AccessKey Secret`
  - 为你的 AI 助手选择一个商汤日日新的模型即可开始对话

  <Image alt={'选择商汤日日新模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/aea782b1-27bd-4d9c-b521-c172c2095fe6'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考商汤日日新的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用商汤日日新提供的模型进行对话了。
