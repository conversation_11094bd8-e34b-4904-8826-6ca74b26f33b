---
title: 在 LobeChat 中进行话题与助手的革新
description: 了解 LobeChat 中的话题与助手概念，如何提高用户交互效率并解决历史对话信息索引分散的问题。
tags:
  - LobeChat
  - 话题与助手
  - 交互效率
  - 历史对话记录
  - 信息索引
---

# 话题与助手

## ChatGPT 与「话题」

在 ChatGPT 官方应用中，只存在话题的概念，如图所示，在侧边栏中是用户的历史对话话题列表。

<Image
  alt={'ChatGPT 与「话题」'}
  src={
  'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279602474-fe7cb3f3-8eb7-40d3-a69f-6615393bbd4e.png'
}
/>

但在我们的使用过程中其实会发现这种模式存在很多问题，比如历史对话的信息索引过于分散问题，同时当处理一些重复任务时很难有一个稳定的入口，比如我希望有一个稳定的入口可以让 ChatGPT 帮助我翻译文档，在这个模式下，我需要不断新建新的话题同时再设置我之前创建好的翻译 Prompt 设定，当有高频任务存在时，这将是一个效率很低的交互形式。

## 「话题」与「助手」

因此在 LobeChat 中，我们引入了 **助手** 的概念。助手是一个完整的功能模块，每个助手都有自己的职责和任务。助手可以帮助你处理各种任务，并提供专业的建议和指导。

<Image
  alt={'「话题」与「助手」'}
  src={
  'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279602489-89893e61-2791-4083-9b57-ed80884ad58b.png'
}
/>

与此同时，我们将话题索引到每个助手内部。这样做的好处是，每个助手都有一个独立的话题列表，你可以根据当前任务选择对应的助手，并快速切换历史对话记录。这种方式更符合用户对常见聊天软件的使用习惯，提高了交互的效率。
