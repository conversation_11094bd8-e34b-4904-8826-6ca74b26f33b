---
title: Deploy LobeChat with Railway
description: >-
  Learn how to deploy LobeChat on Railway and follow the step-by-step process. Get your OpenAI API Key, deploy with a click, and start using it. Optionally, bind a custom domain for your deployment.

tags:
  - Deploy LobeChat
  - Railway Deployment
  - OpenAI API Key
  - Custom Domain Binding
---

# Deploy LobeChat with Railway

If you want to deploy LobeChat on Railway, you can follow the steps below:

## Railway Deployment Process

<Steps>
  ### Prepare your OpenAI API Key

  Go to [OpenAI API Key](https://platform.openai.com/account/api-keys) to get your OpenAI API Key.

  ### Click the button below to deploy

  [![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/template/FB6HrV?referralCode=9bD9mT)

  ### Once deployed, you can start using it

  ### Bind a custom domain (optional)

  You can use the subdomain provided by Railway, or choose to bind a custom domain. Currently, the domains provided by Railway have not been contaminated, and most regions can connect directly.
</Steps>
