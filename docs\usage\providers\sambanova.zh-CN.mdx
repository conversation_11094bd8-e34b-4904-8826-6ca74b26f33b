---
title: 在 LobeChat 中使用 SambaNova API Key
description: 学习如何在 LobeChat 中配置和使用 SambaNova 模型，获取 API 密钥并开始对话。
tags:
  - LobeChat
  - SambaNova
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 SambaNova

<Image alt={'在 LobeChat 中使用 SambaNova'} cover src={'https://github.com/user-attachments/assets/1028aa1a-6c19-4191-b28a-2020e5637155'} />

[SambaNova](https://sambanova.ai/) 是一家位于美国加利福尼亚州帕洛阿尔托的公司，专注于开发高性能 AI 硬件和软件解决方案，提供快速的 AI 模型训练、微调和推理能力，尤其适用于大规模生成式 AI 模型。

本文档将指导你如何在 LobeChat 中使用 SambaNova:

<Steps>
  ### 步骤一：获取 SambaNova API 密钥

  - 首先，你需要注册并登录 [SambaNova Cloud](https://cloud.sambanova.ai/)
  - 在 `APIs` 页面中创建一个 API 密钥

  <Image alt={'获取 SambaNova API 密钥'} inStep src={'https://github.com/user-attachments/assets/ed6965c8-6884-4adf-a457-573a96755f55'} />

  - 复制得到的 API 密钥并妥善保存

  <Callout type={'warning'}>
    请妥善保存生成的 API Key，它只会出现一次，如果不小心丢失了，你需要重新创建一个 API key
  </Callout>

  ### 步骤二：在 LobeChat 中配置 SambaNova

  - 访问 LobeChat 的 `应用设置`界面
  - 在 `AI 服务商` 下找到 `SambaNova` 的设置项

  <Image alt={'填写 SambaNova API 密钥'} inStep src={'https://github.com/user-attachments/assets/328e9755-8da9-4849-8569-e099924822fe'} />

  - 打开 SambaNova 并填入获取的 API 密钥
  - 为你的助手选择一个 SambaNova 模型即可开始对话

  <Image alt={'选择 SambaNova 模型'} inStep src={'https://github.com/user-attachments/assets/6dbf4560-3f62-4b33-9f41-96e12b5087b1'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 SambaNova 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 SambaNova 提供的模型进行对话了。
