---
title: Using Google Gemini API Key in LobeChat
description: >-
  Learn how to integrate and utilize Google Gemini AI in LobeChat to enhance your conversational experience. Follow these steps to configure Google Gemini and start leveraging its powerful capabilities.

tags:
  - Google Gemini
  - AI integration
  - Google AI Studio
  - Web UI
---

# Using Google Gemini in LobeChat

<Image alt={'Using Google Gemini in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/9a8dc1d4-152b-415f-a7cd-8f0c8fbb9913'} />

Gemini AI is a set of large language models (LLMs) created by Google AI, known for its cutting-edge advancements in multimodal understanding and processing. It is essentially a powerful artificial intelligence tool capable of handling various tasks involving different types of data, not just text.

This document will guide you on how to use Google Gemini in LobeChat:

<Steps>
  ### Step 1: Obtain Google API Key

  - Visit and log in to [Google AI Studio](https://aistudio.google.com/)
  - Navigate to `Get API Key` in the menu and click on `Create API Key`

  <Image alt={'Generate Google Gemini Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/ba3595e3-d9cb-4d0d-b414-8306b16df186'} />

  - Select a project and create an API key, or create one in a new project

  <Image alt={'Enter API Key Name'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/fa725e49-4c17-4055-82bc-98a31e73fa54'} />

  - Copy the API key from the pop-up dialog

  <Image alt={'Copy API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/28d89add-cb18-4b86-9807-f2a5ed65ceba'} />

  ### Step 2: Configure OpenAI in LobeChat

  - Go to the `Settings` interface in LobeChat
  - Find the setting for `Google Gemini` under `AI Service Provider`

  <Image alt={'Enter Google Gemini API Key in LobeChat'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/11442ce4-a615-49c4-937a-ca2ae93dd27c'} />

  - Enable Google Gemini and enter the obtained API key
  - Choose a Gemini model for your assistant to start the conversation

  <Image alt={'Select and Use Gemini Model'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/ef9ed1b8-6828-4dd6-b86b-bb0b4fa40619'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to Google Gemini's
    pricing policy.
  </Callout>
</Steps>

Congratulations! You can now use Google Gemini in LobeChat.
