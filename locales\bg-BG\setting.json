{"about": {"title": "Относно"}, "agentTab": {"chat": "Предпочитания за чат", "meta": "Информация за асистента", "modal": "Настройки на модела", "opening": "Настройка на откритие", "plugin": "Настройки на добавката", "prompt": "Настройки на ролята", "tts": "Гласова услуга"}, "analytics": {"telemetry": {"desc": "Чрез избора на изпращане на телеметрични данни, можете да ни помогнете да подобрим общото потребителско изживяване на {{appName}}", "title": "Изпращане на анонимни данни за използване"}, "title": "Анали<PERSON>и"}, "danger": {"clear": {"action": "Изчисти сега", "confirm": "Потвърдете изчистването на всички данни от чата?", "desc": "Това ще изчисти всички данни от сесията, включително агент, файлове, съобщения, плъгини и др.", "success": "Всички съобщения от сесията са изчистени", "title": "Изчисти всички съобщения от сесията"}, "reset": {"action": "Нулирай сега", "confirm": "Потвърдете нулирането на всички настройки?", "currentVersion": "Текуща версия", "desc": "Нулирайте всички настройки до стойностите по подразбиране", "success": "Всички настройки са нулирани успешно", "title": "Нулиране на всички настройки"}}, "header": {"desc": "Предпочитания и настройки на модела.", "global": "Глобални настройки", "session": "Настройки на сесията", "sessionDesc": "Задаване на роля и предпочитания за сесия.", "sessionWithName": "Настройки на сесията · {{name}}", "title": "Настройки"}, "hotkey": {"conflicts": "Конфликт с текущите клавишни комбинации", "errors": {"CONFLICT": "Конфликт на клавишната комбинация: тази комбинация вече е заета от друга функция", "INVALID_FORMAT": "Невалиден формат на клавишната комбинация: моля, използвайте правилния формат (например CommandOrControl+E)", "INVALID_ID": "Невалиден идентификатор на клавишната комбинация", "NO_MODIFIER": "Клавишната комбинация трябва да съдържа модификатор (Ctrl, Alt, Shift и др.)", "SYSTEM_OCCUPIED": "Клави<PERSON>ната комбинация е заета от системата или друго приложение", "UNKNOWN": "Актуализацията не бе успешна: неизвестна грешка"}, "group": {"conversation": "Разговор", "desktop": "Настолен", "essential": "Основен"}, "invalidCombination": "Клавишната комбинация трябва да съдържа поне един модификатор (Ctrl, Alt, Shift) и един обикновен клавиш", "record": "Натиснете клавиш, за да запишете клавишна комбинация", "reset": "Нулиране до подразбиращите се клавишни комбинации", "title": "Бързи клавиши", "updateError": "Актуализацията на клавишната комбинация не бе успешна: мрежова или системна грешка", "updateSuccess": "Актуализацията на клавишната комбинация бе успешна"}, "llm": {"aesGcm": "Ваши<PERSON><PERSON> ключ и адрес на агента ще бъдат криптирани с алгоритъма за криптиране <1>AES-GCM</1>", "apiKey": {"desc": "Моля, въведете вашия {{name}} API ключ", "placeholder": "{{name}} <PERSON> ключ", "title": "<PERSON> ключ"}, "checker": {"button": "Провери", "desc": "Проверете дали API ключът и адресът на прокси сървъра са попълнени правилно", "pass": "Проверката е успешна", "title": "Проверка на свързаността"}, "customModelCards": {"addNew": "Създайте и добавете модел {{id}}", "config": "Конфигуриране на модела", "confirmDelete": "Ще бъде изтрит този персонализиран модел и няма да може да бъде възстановен. Моля, бъдете внимателни.", "modelConfig": {"azureDeployName": {"extra": "Полето, използвано за искане на реално име на разгърнатия модел в Azure OpenAI", "placeholder": "Моля, въведете името на разгърнатия модел в Azure", "title": "Име на разгърнатия модел"}, "displayName": {"placeholder": "Моля, въведете името за показване на модела, като например ChatGPT, GPT-4 и други", "title": "Име за показване на модела"}, "files": {"extra": "Текущата функция за качване на файлове е само един хак, предназначен за лични опити. Пълната функционалност за качване на файлове ще бъде налична в бъдеще.", "title": "Поддръжка на качване на файлове"}, "functionCall": {"extra": "Тази конфигурация ще активира само функцията за извикване на функции в приложението. Поддръжката на извиквания на функции зависи изцяло от самия модел, моля, тествайте наличността на функцията за извикване на функции на този модел.", "title": "Поддръжка на извикване на функции"}, "id": {"extra": "Ще бъде използван като етикет на модела", "placeholder": "Моля, въведете идентификатор на модела, като например gpt-4-turbo-preview или claude-2.1", "title": "Идентификатор на модела"}, "modalTitle": "Конфигурация на персонализиран модел", "tokens": {"title": "Максимален брой токени"}, "vision": {"extra": "Тази конфигурация ще активира само настройките за качване на изображения в приложението. Поддръжката на разпознаване зависи изцяло от самия модел, моля, тествайте наличността на визуалната разпознаваемост на този модел.", "title": "Поддръжка на разпознаване на изображения"}}}, "fetchOnClient": {"desc": "Режимът на заявка от клиента стартира заявката директно от браузъра, което може да увеличи скоростта на отговора", "title": "Използване на режим на заявка от клиента"}, "fetcher": {"clear": "Изчисти получената модел", "fetch": "Изтегляне на списъка с модели", "fetching": "Изтегляне на списъка с модели...", "latestTime": "Последно актуализирано: {{time}}", "noLatestTime": "В момента няма наличен списък"}, "helpDoc": "Настройки за документация", "modelList": {"desc": "Изберете модел, който да се показва по време на разговор. Избраният модел ще бъде показан в списъка с модели.", "placeholder": "Моля, изберете модел от списъка", "title": "Списък с модели", "total": "Общо {{count}} налични модела"}, "proxyUrl": {"desc": "Включващ адреса по подразбиране, трябва да включва http(s)://", "title": "Адрес на API прокси"}, "waitingForMore": "Още модели са <1>планирани да бъдат добавени</1>, очаквайте"}, "plugin": {"addMCPPlugin": "Добавяне на MCP плъгин", "addTooltip": "Персонализ<PERSON><PERSON><PERSON><PERSON> плъгин", "clearDeprecated": "Премахване на остарели плъгини", "empty": "Все още няма инсталирани плъгини, не се колебайте да разгледате <1>магазина за плъгини</1>", "installStatus": {"deprecated": "Деин<PERSON>та<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"hint": "Моля, попълнете следните конфигурации въз основа на описанието", "title": "Конфигурация на плъгина {{id}}", "tooltip": "Конфигурация на плъгина"}, "store": "Магазин за плъгини"}, "settingAgent": {"avatar": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "backgroundColor": {"title": "Цвят на фона"}, "description": {"desc": "Кратко представяне на вашия асистент, не като роля", "placeholder": "Въведете описание на агента", "title": "Описание на агента"}, "name": {"placeholder": "Въведете име на агента", "title": "Име"}, "prompt": {"placeholder": "Въведете дума за подкана за роля", "title": "Настройка на ролята"}, "submit": "Актуализиране на информацията за асистента", "tag": {"desc": "Етикетите на асистента ще се показват на пазара за асистенти", "placeholder": "Въведете таг", "title": "Таг"}, "title": "Информация за агента"}, "settingAppearance": {"neutralColor": {"desc": "Персонализиране на сивата скала с различни цветови нюанси", "title": "Неутрални цветове"}, "preview": {"title": "Цветова палитра"}, "primaryColor": {"desc": "Персонализиране на основния цвят на темата", "title": "Основен цвят"}, "title": "Външен вид на приложението"}, "settingChat": {"autoCreateTopicThreshold": {"desc": "Автоматично създайте тема, когато текущият брой съобщения надвиши тази стойност", "title": "Праг на съобщенията"}, "chatStyleType": {"title": "Стил на прозореца за чат", "type": {"chat": "Режим на разговор", "docs": "Режим на документ"}}, "compressThreshold": {"desc": "Когато некомпресираните съобщения в историята надвишат тази стойност, ще се приложи компресия", "title": "Праг на компресия на дължината на съобщенията в историята"}, "enableAutoCreateTopic": {"desc": "Дали да се създава автоматично тема по време на разговора, ефективно само във временни теми", "title": "Автоматично създаване на тема"}, "enableCompressHistory": {"title": "Активиране на автоматично обобщаване на историята на съобщенията"}, "enableHistoryCount": {"alias": "Неограничен", "limited": "Включете само {{number}} съобщения от разговора", "setlimited": "Задайте ограничение за използване на брой исторически съобщения", "title": "Ограничаване на броя на съобщенията в историята", "unlimited": "Неограничен брой съобщения в историята"}, "historyCount": {"desc": "Брой исторически съобщения, носени с всяка заявка", "title": "Брой прикачени съобщения в историята"}, "inputTemplate": {"desc": "Последното съобщение на потребителя ще бъде попълнено в този шаблон", "placeholder": "Шаблонът за предварителна обработка {{text}} ще бъде заменен с информация за въвеждане в реално време", "title": "Предварителна обработка на потребителския вход"}, "submit": "Актуализиране на предпочитанията за чат", "title": "Настройки на чата"}, "settingChatAppearance": {"fontSize": {"desc": "Размер на шрифта на съдържанието на чата", "marks": {"normal": "Стан<PERSON><PERSON><PERSON><PERSON><PERSON>н"}, "title": "Размер на шрифта"}, "highlighterTheme": {"title": "Тема за синтактично оцветяване"}, "mermaidTheme": {"title": "Тема русалка"}, "title": "Вън<PERSON>ен вид на чата", "transitionMode": {"desc": "Анимация на прехода на съобщенията в чата", "options": {"fadeIn": "Постепенно появяване", "none": {"desc": "Зависи от начина на отговор на модела, моля, тествайте сами.", "value": "Без"}, "smooth": "Плавно"}, "title": "Анимация на прехода"}}, "settingCommon": {"lang": {"autoMode": "Следвай системата", "title": "Език"}, "themeMode": {"auto": "Автоматичен", "dark": "Тъм<PERSON>н", "light": "Светъл", "title": "Тема"}, "title": "Общи настройки"}, "settingModel": {"enableMaxTokens": {"title": "Активиране на ограничението за максимален брой токени"}, "enableReasoningEffort": {"title": "Активиране на настройката за интензивност на разсъжденията"}, "frequencyPenalty": {"desc": "Колкото по-голяма е стойността, толкова по-богат и разнообразен е речникът; колкото по-ниска е стойността, толкова по-прост и обикновен е речникът.", "title": "Богатство на речника"}, "maxTokens": {"desc": "Максималният брой токени, използвани за всяко взаимодействие", "title": "Ограничение за максимален брой токени"}, "model": {"desc": "{{provider}} модел", "title": "<PERSON>о<PERSON><PERSON><PERSON>"}, "params": {"title": "Разширени параметри"}, "presencePenalty": {"desc": "Колкото по-голяма е стойността, толкова по-склонен е към различни изрази, избягвайки повторение на концепции; колкото по-ниска е стойността, толкова по-склонен е да използва повторение на концепции или разкази, изразявайки по-голяма последователност.", "title": "Разнообразие на изразите"}, "reasoningEffort": {"desc": "Колкото по-висока е стойността, толкова по-силна е способността за разсъждение, но това може да увеличи времето за отговор и консумацията на токени", "options": {"high": "Високо", "low": "Ниско", "medium": "Средно"}, "title": "Интензивност на разсъжденията"}, "submit": "Актуализиране на настройките на модела", "temperature": {"desc": "Колкото по-голямо е числото, толкова по-креативни и въображаеми са отговорите; колкото по-малко е числото, толкова по-строги са отговорите", "title": "Креа<PERSON>и<PERSON>на активност", "warning": "Ако стойността на креативната активност е твърде голяма, изходът може да съдържа грешки"}, "title": "Настройки на модела", "topP": {"desc": "Колко възможности да се вземат предвид, по-голямата стойност приема повече възможни отговори; по-малката стойност предпочита най-вероятния отговор. Не се препоръчва да се променя заедно с креативната активност", "title": "Отвореност на мисленето"}}, "settingOpening": {"openingMessage": {"desc": "Съобщение за откритие при стартиране на сесия, използвано за представяне на функциите на асистента", "placeholder": "Здрав<PERSON><PERSON>, аз съм Персонализи<PERSON><PERSON><PERSON> асистент, можеш веднага да започнеш разговор с мен или да отидеш в Настройки на асистента, за да попълниш информацията ми.", "title": "Съобщение за откритие"}, "openingQuestions": {"desc": "Водещи въпроси, показвани в началото на сесията", "empty": "Няма въпроси", "placeholder": "Въведете въпрос", "repeat": "Въпросът вече съществува", "title": "Въпроси за откритие"}, "title": "Настройка на откритие"}, "settingPlugin": {"title": "Списък с плъгини"}, "settingSystem": {"accessCode": {"desc": "Достъпът с криптиране е активиран от администратора", "placeholder": "Въведете парола за достъп", "title": "Парола за достъп"}, "oauth": {"info": {"desc": "Вля<PERSON><PERSON>л", "title": "Информация за акаунта"}, "signin": {"action": "Вход", "desc": "Влезте с SSO, за да отключите приложението", "title": "Влезте в акаунта си"}, "signout": {"action": "Изход", "confirm": "Потвърдете излизането?", "success": "Изходът е успешен"}}, "title": "Системни настройки"}, "settingTTS": {"openai": {"sttModel": "Модел за преобразуване на реч в текст на OpenAI", "title": "OpenAI", "ttsModel": "Модел за преобразуване на текст в реч на OpenAI"}, "showAllLocaleVoice": {"desc": "Ако е затворено, ще се показват само гласове на текущия език", "title": "Показване на всички локални гласове"}, "stt": "Настройки за разпознаване на реч", "sttAutoStop": {"desc": "Когато е затворено, разпознаването на реч няма да приключи автоматично и изисква ръчно щракване, за да спре", "title": "Автоматично спиране на разпознаването на реч"}, "sttLocale": {"desc": "Езикът на въвеждане на реч, тази опция може да подобри точността на разпознаването на реч", "title": "Език за разпознаване на реч"}, "sttService": {"desc": "Където „браузър“ е родната услуга за разпознаване на реч на браузъра", "title": "Услуга за разпознаване на реч"}, "submit": "Актуализиране на услугата за синтез на реч", "title": "Услуга за реч", "tts": "Настройки за преобразуване на текст в реч", "ttsService": {"desc": "Ако използвате услугата за преобразуване на текст в реч на OpenAI, уверете се, че услугата на модела OpenAI е активирана", "title": "Услуга за преобразуване на текст в реч"}, "voice": {"desc": "Изберете глас за текущия агент, различните TTS услуги поддържат различни гласове", "preview": "Преглед на гласа", "title": "Глас за преобразуване на текст в реч"}}, "storage": {"actions": {"export": {"button": "Експортиране", "exportType": {"agent": "Експортиране на настройки на асистента", "agentWithMessage": "Експортиране на асистента и съобщенията", "all": "Експортиране на глобалните настройки и всички данни на асистентите", "allAgent": "Експортиране на всички настройки на асистентите", "allAgentWithMessage": "Експортиране на всички асистенти и съобщения", "globalSetting": "Експортиране на глобалните настройки"}, "title": "Експортиране на данни"}, "import": {"button": "Импортиране", "title": "Импортиране на данни"}, "title": "Разширени операции"}, "desc": "Използване на хранилището в текущия браузър", "embeddings": {"used": "Векторно хранилище"}, "title": "Данни за хранилище", "used": "Използване на хранилището"}, "submitAgentModal": {"button": "Изпрати агент", "identifier": "Идентификатор на агент", "metaMiss": "Моля, попълнете информацията за агента, преди да го изпратите. Тя трябва да включва име, описание и тагове", "placeholder": "Въведете уникален идентификатор за агента, напр. web-development", "tooltips": "Споделяне на пазара на агенти"}, "submitFooter": {"reset": "Нулиране", "submit": "Запазване", "unSaved": "Непознати промени", "unSavedWarning": "В момента има непознати промени"}, "sync": {"device": {"deviceName": {"hint": "Добавете име за лесна идентификация", "placeholder": "Въведете име на устройството", "title": "Име на устройството"}, "title": "Информация за устройството", "unknownBrowser": "Неизвестен браузър", "unknownOS": "Неизвестна операционна система"}, "warning": {"tip": "След дълъг период на обществено тестване, синхронизацията на WebRTC може да не бъде стабилна за общите изисквания за синхронизация на данни. Моля, <1>инсталирайте сигналния сървър</1> и го използвайте след това."}, "webrtc": {"channelName": {"desc": "WebRTC ще използва това име, за да създаде канал за синхронизиране. Уверете се, че името на канала е уникално.", "placeholder": "Въведете име на канал за синхронизиране", "shuffle": "Генерирай произволно", "title": "Име на канал за синхронизиране"}, "channelPassword": {"desc": "Добавете парола, за да осигурите поверителност на канала. Само устройства с правилната парола могат да се присъединят към канала.", "placeholder": "Въведете парола за канал за синхронизиране", "title": "Парола за канал за синхронизиране"}, "desc": "Комуникацията на данни в реално време между партньори изисква всички устройства да бъдат онлайн за синхронизиране.", "enabled": {"invalid": "Моля, попълнете адреса на сигналния сървър и името на синхронизиращия канал, преди да го активирате.", "title": "Активиране на синхронизиране"}, "signaling": {"desc": "WebRTC ще използва този адрес за синхронизация", "placeholder": "Моля, въведете адреса на сигналния сървър", "title": "<PERSON>иг<PERSON><PERSON><PERSON>н сървър"}, "title": "WebRTC синхронизиране"}}, "systemAgent": {"agentMeta": {"label": "Модел за генериране на помощни метаданни", "modelDesc": "Моде<PERSON>, определен за генериране на име, описание, профилна снимка и етикети на помощник", "title": "Автоматично генериране на информация за помощник"}, "customPrompt": {"addPrompt": "Добавяне на персонализиран подканва", "desc": "След попълване, системният асистент ще използва персонализираната подканва при генериране на съдържание", "placeholder": "Моля, въведете персонализирана подканва", "title": "Персонализирана подканва"}, "generationTopic": {"label": "Модел за именуване на теми за AI рисуване", "modelDesc": "Мо<PERSON><PERSON><PERSON>, предназначен за автоматично именуване на теми за AI рисуване", "title": "Автоматично именуване на теми за AI рисуване"}, "helpInfo": "Когато създавате нов асистент, настройките по подразбиране ще се използват като предварителни стойности.", "historyCompress": {"label": "Модел на история на сесията", "modelDesc": "Определете модела, използван за компресиране на историята на сесията", "title": "Автоматично обобщаване на историята на сесията"}, "queryRewrite": {"label": "Модел за пренаписване на запитвания", "modelDesc": "Определя модел за оптимизиране на запитванията на потребителите", "title": "Пренаписване на въпроси от базата данни"}, "thread": {"label": "Модел за именуване на подтеми", "modelDesc": "Модел, предназначен за автоматично преименуване на подтеми", "title": "Автоматично именуване на подтеми"}, "title": "Системен асистент", "topic": {"label": "Модел за именуване на теми", "modelDesc": "Моде<PERSON>, определен за автоматично преименуване на теми", "title": "Автоматично именуване на теми"}, "translation": {"label": "Модел за превод", "modelDesc": "Определя модела, използван за превод", "title": "Настройки на преводния асистент"}}, "tab": {"about": "Относно", "agent": "Агент по подразбиране", "common": "Общи настройки", "experiment": "Експеримент", "hotkey": "Бързи клавиши", "llm": "Езиков модел", "provider": "AI доставчик", "proxy": "Мрежов прокси", "storage": "Данни за хранилище", "sync": "Синхронизиране в облака", "system-agent": "Системен асистент", "tts": "Текст към реч"}, "tools": {"builtins": {"groupName": "Вградени"}, "disabled": "Текущият модел не поддържа извиквания на функции и не може да използва плъгина", "plugins": {"enabled": "Активирани: {{num}}", "groupName": "Плъгини", "noEnabled": "Няма активирани плъгини", "store": "Магазин за плъгини"}, "title": "Инструменти за разширение"}}