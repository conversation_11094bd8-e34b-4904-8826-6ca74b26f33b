{"about": {"title": "حو<PERSON>"}, "agentTab": {"chat": "تفضيلات الدردشة", "meta": "معلومات المساعد", "modal": "إعدادات النموذج", "opening": "إعداد الافتتاح", "plugin": "إعدادات الإضافة", "prompt": "تعيين الشخصية", "tts": "خدمة النص إلى كلام"}, "analytics": {"telemetry": {"desc": "من خلال اختيار إرسال بيانات القياس عن بُعد، يمكنك مساعدتنا في تحسين تجربة المستخدم العامة لـ {{appName}}", "title": "إرسال بيانات الاستخدام المجهولة"}, "title": "تحليلات"}, "danger": {"clear": {"action": "<PERSON><PERSON><PERSON> ال<PERSON>ن", "confirm": "هل تؤكد مسح جميع بيانات المحادثات؟", "desc": "سيتم مسح جميع بيانات الجلسة بما في ذلك المساعد والملفات والرسائل والإضافات", "success": "تم مسح جميع رسائل الجلسة", "title": "مسح جميع رسائل الجلسة"}, "reset": {"action": "إعادة تعيين الآن", "confirm": "هل تؤكد إعادة تعيين جميع الإعدادات؟", "currentVersion": "الإصدار الحالي", "desc": "إعادة تعيين جميع عناصر الإعدادات إلى القيم الافتراضية", "success": "تمت إعادة ضبط جميع الإعدادات", "title": "إعادة تعيين جميع الإعدادات"}}, "header": {"desc": "إعدادات التفضيلات والنماذج.", "global": "إعدادات عامة", "session": "إعدادات الجلسة", "sessionDesc": "إعداد الشخصية وتفضيلات الجلسة.", "sessionWithName": "إعدادات الجلسة · {{name}}", "title": "إعدادات"}, "hotkey": {"conflicts": "يتعارض مع اختصارات لوحة المفاتيح الحالية", "errors": {"CONFLICT": "تعارض في اختصار لوحة المفاتيح: هذا الاختصار مستخدم بالفعل من قبل وظيفة أخرى", "INVALID_FORMAT": "تنسيق اختصار لوحة المفاتيح غير صالح: يرجى استخدام التنسيق الصحيح (مثل CommandOrControl+E)", "INVALID_ID": "معرف اختصار لوحة المفاتيح غير صالح", "NO_MODIFIER": "يجب أن يحتوي اختصار لوحة المفاتيح على مفتاح تعديل (Ctrl، Alt، Shift، إلخ)", "SYSTEM_OCCUPIED": "اختصار لوحة المفاتيح مستخدم من قبل النظام أو تطبيقات أخرى", "UNKNOWN": "فشل التحديث: خطأ غير معروف"}, "group": {"conversation": "المحادثة", "desktop": "سط<PERSON> المكتب", "essential": "أساسي"}, "invalidCombination": "يجب أن تحتوي اختصارات لوحة المفاتيح على مفتاح تعديل واحد على الأقل (Ctrl، Alt، Shift) ومفتاح عادي واحد", "record": "اضغط على المفتاح لتسجيل اختصار لوحة المفاتيح", "reset": "إعادة تعيين إلى اختصارات لوحة المفاتيح الافتراضية", "title": "اختصارات لوحة المفاتيح", "updateError": "فشل تحديث اختصار لوحة المفاتيح: خطأ في الشبكة أو النظام", "updateSuccess": "تم تحديث اختصار لوحة المفاتيح بنجاح"}, "llm": {"aesGcm": "سيتم استخدام خوارزمية التشفير <1>AES-GCM</1> لتشفير مفتاحك وعنوان الوكيل", "apiKey": {"desc": "يرجى ملء مفتاح API الخاص بك {{name}}", "placeholder": "{{name}} مفتاح API", "title": "مفتاح API"}, "checker": {"button": "فحص", "desc": "اختبار ما إذا كان مفتاح واجهة البرمجة وعنوان الوكيل مملوء بشكل صحيح", "pass": "تمت المراقبة", "title": "فحص الاتصال"}, "customModelCards": {"addNew": "إنشاء وإضافة نموذج {{id}}", "config": "تكوين النموذج", "confirmDelete": "سيتم حذف النموذج المخصص هذا، وبمجرد الحذف لا يمكن استعادته، يرجى التحلي بالحذر.", "modelConfig": {"azureDeployName": {"extra": "الحقل الفعلي المطلوب في طلب Azure OpenAI", "placeholder": "الرجاء إدخال اسم نشر النموذج في Azure", "title": "اسم نشر النموذج"}, "displayName": {"placeholder": "الرجاء إدخال اسم العرض للنموذج، مثل ChatGPT، GPT-4، إلخ", "title": "اسم العرض للنموذج"}, "files": {"extra": "تنفيذ تحميل الملفات الحالي هو مجرد حل Hack، ومخصص للتجربة الذاتية فقط. يرجى الانتظار حتى يتم تنفيذ القدرة الكاملة على تحميل الملفات لاحقًا", "title": "دعم تحميل الملفات"}, "functionCall": {"extra": "ستفتح هذه الإعدادات فقط القدرة على استدعاء الوظائف داخل التطبيق، وما إذا كانت الوظائف مدعومة يعتمد تمامًا على النموذج نفسه، يرجى اختبار قابلية استخدام استدعاء الوظائف لهذا النموذج بنفسك", "title": "دعم استدعاء الوظائف"}, "id": {"extra": "سيتم عرضه كعلامة للنموذج", "placeholder": "الرجاء إدخال معرف النموذج، مثل gpt-4-turbo-preview أو claude-2.1", "title": "معرف النموذج"}, "modalTitle": "تكوين النموذج المخصص", "tokens": {"title": "<PERSON><PERSON><PERSON><PERSON> عدد من الرموز"}, "vision": {"extra": "ستفتح هذه الإعدادات فقط القدرة على تحميل الصور داخل التطبيق، وما إذا كانت القدرة على التعرف مدعومة يعتمد تمامًا على النموذج نفسه، يرجى اختبار قابلية استخدام التعرف البصري لهذا النموذج بنفسك", "title": "دعم التعرف على الصور"}}}, "fetchOnClient": {"desc": "طريقة طلب العميل ستبدأ طلب الجلسة مباشرة من المتصفح، مما يمكن أن يعزز سرعة الاستجابة", "title": "استخدام طريقة طلب العميل"}, "fetcher": {"clear": "مس<PERSON> النموذج المستخرج", "fetch": "احصل على قائمة النماذج", "fetching": "جاري الحصول على قائمة النماذج...", "latestTime": "آخر تحديث: {{time}}", "noLatestTime": "لم يتم الحصول على قائمة بعد"}, "helpDoc": "دليل التكوين", "modelList": {"desc": "اختيار النموذج الذي سيتم عرضه في الجلسة، سيتم عرض النموذج المحدد في قائمة النماذج", "placeholder": "الرجاء اختيار نموذج من القائمة", "title": "قائمة النماذج", "total": "متاح {{count}} نموذج"}, "proxyUrl": {"desc": "يجب أن يتضمن عنوان الوكيل API بالإضافة إلى العنوان الافتراضي http(s)://", "title": "عنوان وكيل API"}, "waitingForMore": "يتم <1>التخطيط لتوفير</1> المزيد من النماذج، ترقبوا المزيد"}, "plugin": {"addMCPPlugin": "إضافة مكون MCP", "addTooltip": "إضافة البرنامج المساعد", "clearDeprecated": "مسح البرامج المساعدة الغير صالحة", "empty": "لا توجد برامج مساعدة مثبتة حاليًا، نرحب بك لزيارة <1>متجر البرامج المساعدة</1> للاستكشاف", "installStatus": {"deprecated": "تم إلغاء التثبيت"}, "settings": {"hint": "يرجى ملء الإعدادات التالية وفقًا للوصف", "title": "إعدادات البرنامج المساعد {{id}}", "tooltip": "إعدادات البرنامج المساعد"}, "store": "متجر البرامج المساعد"}, "settingAgent": {"avatar": {"title": "الصورة الرمزية"}, "backgroundColor": {"title": "لون الخلفية"}, "description": {"desc": "مقدمة بسيطة عن مساعدك، لا تستخدم كإعداد شخصية", "placeholder": "الرجاء إدخال وصف المساعد", "title": "وصف المساعد"}, "name": {"placeholder": "الرجاء إدخال اسم المساعد", "title": "الاسم"}, "prompt": {"placeholder": "الرجاء إدخال كلمة الإشارة للشخصية", "title": "ضبط الشخصية"}, "submit": "تحديث معلومات المساعد", "tag": {"desc": "سيتم عرض علامة المساعد في سوق المساعدين", "placeholder": "الرجاء إدخال العلامة", "title": "العلامة"}, "title": "معلومات المساعد"}, "settingAppearance": {"neutralColor": {"desc": "تخصيص تدرجات الرمادي ذات الاتجاهات اللونية المختلفة", "title": "<PERSON>و<PERSON> محايد"}, "preview": {"title": "لوحة الألوان"}, "primaryColor": {"desc": "تخصيص لون الموضوع", "title": "لون الموضوع"}, "title": "مظهر التطبيق"}, "settingChat": {"autoCreateTopicThreshold": {"desc": "عند تجا<PERSON>ز عدد الرسائل الحالي هذا القيمة، سيتم إنشاء موضوع تلقائيًا", "title": "عتبة إنشاء الموضوع التلقائي"}, "chatStyleType": {"title": "نوع نافذة الدردشة", "type": {"chat": "نمط المحادثة", "docs": "نمط الوثائق"}}, "compressThreshold": {"desc": "عندما يتجا<PERSON>ز عدد الرسائل التاريخية غير المضغوطة هذه القيمة، سيتم ضغطها", "title": "عتبة ضغط طول الرسائل التاريخية"}, "enableAutoCreateTopic": {"desc": "هل يجب إنشاء موضوع تلقائيًا أثناء الدردشة، يسري ذلك فقط في المواضيع المؤقتة", "title": "تمكين إنشاء الموضوع تلقائيًا"}, "enableCompressHistory": {"title": "تفعيل تلخيص الرسائل التاريخية تلقائيًا"}, "enableHistoryCount": {"alias": "<PERSON>ير محدود", "limited": "يحتوي فقط على {{number}} رسالة محادثة", "setlimited": "تعيين عدد الرسائل التاريخية", "title": "تحديد عدد الرسائل التاريخية", "unlimited": "<PERSON>ير محدود"}, "historyCount": {"desc": "عدد الرسائل التي يتم إرفاقها في كل طلب (تشمل الأسئلة والأجوبة الجديدة. يُحسب كل سؤال وجواب كرسالة واحدة)", "title": "عد<PERSON> الرسائل المرفقة"}, "inputTemplate": {"desc": "سيتم ملء أحدث رسالة من المستخدم في هذا القالب", "placeholder": "القالب المُعالج مسبقًا {{text}} سيتم استبداله بالمعلومات المُدخلة في الوقت الحقيقي", "title": "معالجة مُدخلات المستخدم"}, "submit": "تحديث تفضيلات الدردشة", "title": "إعدادات الدردشة"}, "settingChatAppearance": {"fontSize": {"desc": "حج<PERSON> <PERSON><PERSON> محتوى الدردشة", "marks": {"normal": "عادي"}, "title": "حج<PERSON> الخط"}, "highlighterTheme": {"title": "موضوع تمييز الكود"}, "mermaidTheme": {"title": "موضوع حورية البحر"}, "title": "مظهر الدردشة", "transitionMode": {"desc": "رسوم انتقال رسائل الدردشة", "options": {"fadeIn": "تلاشي", "none": {"desc": "يعتمد هذا على طريقة إخراج استجابة النموذج، يرجى الاختبار بنفسك.", "value": "بدون"}, "smooth": "سلس"}, "title": "رسوم الانتقال"}}, "settingCommon": {"lang": {"autoMode": "اتباع النظام", "title": "اللغة"}, "themeMode": {"auto": "تلقائي", "dark": "دا<PERSON>ن", "light": "فاتح", "title": "الموضوع"}, "title": "الإعدادات العامة"}, "settingModel": {"enableMaxTokens": {"title": "تم<PERSON>ين الحد الأقصى للردود"}, "enableReasoningEffort": {"title": "تمكين ضبط قوة الاستدلال"}, "frequencyPenalty": {"desc": "كلما زادت القيمة، كانت المفردات أكثر تنوعًا؛ وكلما انخفضت القيمة، كانت المفردات أكثر بساطة ووضوحًا", "title": "تنوع المفردات"}, "maxTokens": {"desc": "<PERSON><PERSON><PERSON> الرموز الأقصى المستخدمة في التفاعل الواحد", "title": "الح<PERSON> الأقصى للردود"}, "model": {"desc": "{{provider}} نموذج", "title": "النموذج"}, "params": {"title": "إعدادات متقدمة"}, "presencePenalty": {"desc": "كلما زادت القيمة، زادت الميل إلى استخدام تعبيرات مختلفة، مما يتجنب تكرار المفاهيم؛ وكلما انخفضت القيمة، زادت الميل إلى استخدام المفاهيم أو السرد المتكرر، مما يجعل التعبير أكثر اتساقًا", "title": "تنوع التعبير"}, "reasoningEffort": {"desc": "كلما زادت القيمة، زادت قوة الاستدلال، ولكن قد يؤدي ذلك إلى زيادة وقت الاستجابة واستهلاك الرموز", "options": {"high": "عالي", "low": "من<PERSON><PERSON>ض", "medium": "متوسط"}, "title": "قوة الاستدلال"}, "submit": "تحديث إعدادات النموذج", "temperature": {"desc": "كلما زادت القيمة، كانت الإجابات أكثر إبداعًا وخيالًا؛ وكلما انخفضت القيمة، كانت الإجابات أكثر دقة", "title": "مستوى الإبداع", "warning": "إذا كانت قيمة مستوى الإبداع مرتفعة جدًا، قد تحتوي المخرجات على تشويش"}, "title": "إعدادات النموذج", "topP": {"desc": "عدد الاحتمالات التي يتم أخذها في الاعتبار، كلما زادت القيمة، زادت احتمالية قبول إجابات متعددة؛ وكلما انخفضت القيمة، زادت الميل لاختيار الإجابة الأكثر احتمالًا. لا يُنصح بتغييرها مع مستوى الإبداع", "title": "مستوى الانفتاح الفكري"}}, "settingOpening": {"openingMessage": {"desc": "رسالة الافتتاح عند بدء المحادثة، تستخدم لتعريف وظائف المساعد", "placeholder": "مرحبًا، أنا المساعد المخصص، يمكنك بدء المحادثة معي على الفور، أو يمكنك الذهاب إلى إعدادات المساعد لإكمال معلوماتي.", "title": "رسالة الافتتاح"}, "openingQuestions": {"desc": "الأسئلة الإرشادية المعروضة عند بدء المحادثة", "empty": "لا توجد أسئلة حالياً", "placeholder": "أدخل السؤال", "repeat": "السؤال موجود بالفعل", "title": "أسئلة الافتتاح"}, "title": "إعداد الافتتاح"}, "settingPlugin": {"title": "قائمة الإضافات"}, "settingSystem": {"accessCode": {"desc": "قام المسؤول بتمكين الوصول المشفر", "placeholder": "الرجاء إدخال كلمة المرور", "title": "كلمة المرور"}, "oauth": {"info": {"desc": "تم تسجيل الدخول", "title": "معلومات الحساب"}, "signin": {"action": "تسجيل الدخول", "desc": "قم بتسجيل الدخول باستخدام SSO لفتح التطبيق", "title": "تسجيل الدخول إلى الحساب"}, "signout": {"action": "تسجيل الخروج", "confirm": "هل ترغب في تأكيد الخروج؟", "success": "تم تسجيل الخروج بنجاح"}}, "title": "إعدادات النظام"}, "settingTTS": {"openai": {"sttModel": "نموذج تحويل النص إلى كلام من OpenAI", "title": "OpenAI", "ttsModel": "نموذج توليد الكلام من OpenAI"}, "showAllLocaleVoice": {"desc": "إذا تم إيقافه، سيتم عرض مصادر الصوت الخاصة باللغة الحالية فقط", "title": "عرض جميع مصادر الصوت للغات"}, "stt": "إعدادات التحويل من الصوت إلى نص", "sttAutoStop": {"desc": "عند الإيقاف، لن يتم إيقاف تحويل الصوت إلى نص تلقائيًا، وسيتطلب الأمر النقر على زر الإيقاف يدويًا", "title": "إيقا<PERSON> تحويل الصوت إلى نص تلقائيًا"}, "sttLocale": {"desc": "لغة الصوت المدخلة، يمكن أن يساعد هذا الخيار في زيادة دقة تحويل الصوت إلى نص", "title": "لغة تحويل الصوت إلى نص"}, "sttService": {"desc": "حيث يكون المتصفح هو خدمة التحويل الصوتي الأصلية", "title": "خدمة تحويل الصوت إلى نص"}, "submit": "تحديث خدمة الصوت", "title": "خدمة الصوت", "tts": "إعدادات توليد الكلام", "ttsService": {"desc": "إذا كنت تستخدم خدمة توليد الكلام من OpenAI، يجب التأكد من تمكين خدمة نموذج OpenAI", "title": "خدمة توليد الكلام"}, "voice": {"desc": "حدد صوتًا للمساعد الحالي، تختلف مصادر الصوت المدعومة بحسب خدمة توليد الكلام", "preview": "معاينة الصوت", "title": "مصدر توليد الكلام"}}, "storage": {"actions": {"export": {"button": "تصدير", "exportType": {"agent": "تصدير إعدادات المساعد", "agentWithMessage": "تصدير المساعد والرسائل", "all": "تصدير الإعدادات العالمية وجميع بيانات المساعدين", "allAgent": "تصدير جميع إعدادات المساعدين", "allAgentWithMessage": "تصدير جميع المساعدين والرسائل", "globalSetting": "تصدير الإعدادات العالمية"}, "title": "تصدير البيانات"}, "import": {"button": "استيراد", "title": "استيراد البيانات"}, "title": "عمليات متقدمة"}, "desc": "حجم التخزين في المتصفح الحالي", "embeddings": {"used": "تخزين المتجهات"}, "title": "تخزين البيانات", "used": "حجم التخزين"}, "submitAgentModal": {"button": "تقديم المساعد", "identifier": "معرف المسا<PERSON>د", "metaMiss": "يرجى استكمال معلومات المساعد قبل التقديم، يجب أن تتضمن الاسم والوصف والعلامة", "placeholder": "الرجاء إدخال معرف المساعد، يجب أن يكون فريدًا، مثل تطوير الويب", "tooltips": "مشاركة في سوق المساعدين"}, "submitFooter": {"reset": "إعادة تعيين", "submit": "<PERSON><PERSON><PERSON>", "unSaved": "تغييرات غير محفوظة", "unSavedWarning": "هناك تغييرات غير محفوظة حالياً"}, "sync": {"device": {"deviceName": {"hint": "أضف اسمًا للتعرف بشكل أفضل", "placeholder": "الرجاء إدخال اسم الجهاز", "title": "اسم الجهاز"}, "title": "معلومات الجهاز", "unknownBrowser": "متصفح غير معروف", "unknownOS": "نظام التشغيل غير معروف"}, "warning": {"tip": "بعد فترة اختبار عامة طويلة، قد لا يكون تزامن WebRTC مستقرًا بما يكفي لتلبية احتياجات التزامن العامة. يرجى <1>نشر خادم الإشارة</1> بنفسك قبل الاستخدام."}, "webrtc": {"channelName": {"desc": "سيستخدم WebRTC هذا الاسم لإنشاء قناة مزامنة، يرجى التأكد من فرادة اسم القناة", "placeholder": "الرجاء إدخال اسم قناة المزامنة", "shuffle": "توليف عشوائي", "title": "اسم قناة المزامنة"}, "channelPassword": {"desc": "إضافة كلمة مرور لضمان خصوصية القناة، يمكن للأجهزة الانضمام إلى القناة فقط عند إدخال كلمة المرور الصحيحة", "placeholder": "الرجاء إدخال كلمة مرور قناة المزامنة", "title": "كلمة مرور قناة المزامنة"}, "desc": "اتصال البيانات النقطي الفوري يتطلب تواجد الأجهزة معًا للمزامنة", "enabled": {"invalid": "الرجاء ملء اسم خادم الإشارة واسم القناة المتزامنة قبل تمكينها", "title": "تمكين المزامنة"}, "signaling": {"desc": "سيستخدم WebRTC هذا العنوان للتزامن", "placeholder": "الرجاء إدخال عنوان خادم الإشارة", "title": "<PERSON>ادم الإشارة"}, "title": "WebRTC مزامنة"}}, "systemAgent": {"agentMeta": {"label": "نموذج إنشاء بيانات المساعد", "modelDesc": "يحد<PERSON> النموذج المستخدم لإنشاء اسم المساعد ووصفه وصورته وعلامته", "title": "توليد معلومات المساعد تلقائيًا"}, "customPrompt": {"addPrompt": "إضافة موجه مخصص", "desc": "بعد ملئه، سيستخدم المساعد النظامي الموجه المخصص عند إنشاء المحتوى", "placeholder": "أدخل كلمة الموجه المخصصة", "title": "كلمة الموجه المخصصة"}, "generationTopic": {"label": "نموذج تسمية موضوع الرسم بالذكاء الاصطناعي", "modelDesc": "نموذج مخصص لتسمية موضوعات الرسم بالذكاء الاصطناعي تلقائيًا", "title": "تسمية موضوعات الرسم بالذكاء الاصطناعي تلقائيًا"}, "helpInfo": "عند إنشاء مساعد جديد، سيتم استخدام إعدادات المساعد الافتراضية كقيم افتراضية.", "historyCompress": {"label": "نموذج تاريخ المحادثة", "modelDesc": "حد<PERSON> النموذج المستخدم لضغط تاريخ المحادثة", "title": "تلخيص تلقائي لتاريخ المحادثة"}, "queryRewrite": {"label": "نموذج إعادة صياغة الأسئلة", "modelDesc": "نموذج مخصص لتحسين أسئلة المستخدمين", "title": "إعادة صياغة سؤال قاعدة المعرفة"}, "thread": {"label": "نموذج تسمية الموضوعات الفرعية", "modelDesc": "نموذج مخصص لإعادة تسمية الموضوعات الفرعية تلقائيًا", "title": "تسمية الموضوعات الفرعية تلقائيًا"}, "title": "م<PERSON><PERSON><PERSON><PERSON> النظام", "topic": {"label": "نموذج تسمية الموضوع", "modelDesc": "يحدد النموذج المستخدم لإعادة تسمية الموضوع تلقائيًا", "title": "إعادة تسمية الموضوع"}, "translation": {"label": "نموذج الترجمة", "modelDesc": "النموذج المحدد للاستخدام في الترجمة", "title": "إعدادات مساعد الترجمة"}}, "tab": {"about": "حو<PERSON>", "agent": "المسا<PERSON>د الافتراضي", "common": "إعدادات عامة", "experiment": "تجربة", "hotkey": "اختصارات لوحة المفاتيح", "llm": "نموذج اللغة", "provider": "مزو<PERSON> خدمة الذكاء الاصطناعي", "proxy": "وكيل الشبكة", "storage": "تخزين البيانات", "sync": "مزامنة السحابة", "system-agent": "م<PERSON><PERSON><PERSON><PERSON> النظام", "tts": "خدمة الكلام"}, "tools": {"builtins": {"groupName": "الامتدادات المدمجة"}, "disabled": "النموذج الحالي لا يدعم استدعاء الوظائف، ولا يمكن استخدام الإضافة", "plugins": {"enabled": "ممكّنة {{num}}", "groupName": "الإضافات", "noEnabled": "لا توجد إضافات ممكّنة حاليًا", "store": "متجر الإضافات"}, "title": "أدوات الامتداد"}}