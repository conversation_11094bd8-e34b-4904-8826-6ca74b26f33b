---
title: 在 LobeChat 中使用 AI21 Labs
description: 学习如何在 LobeChat 中配置和使用 AI21 Labs 的API Key，以便开始对话和交互。
tags:
  - LobeChat
  - AI21 Labs
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 AI21 Labs

<Image cover src={'https://github.com/user-attachments/assets/ae03eab5-a319-4d2a-a5f6-1683ab7739ee'} />

[AI21 Labs](https://www.ai21.com/) 是一家专注于人工智能的公司，提供先进的语言模型和 API 服务，旨在帮助开发者和企业利用自然语言处理技术。其旗舰产品 "Jamba" 系列模型能够进行复杂的语言理解和生成任务，广泛应用于内容创作、对话系统等领域。

本文将指导你如何在 LobeChat 中使用 AI21 Labs。

<Steps>
  ### 步骤一：获得 AI21 Labs 的 API Key

  - 注册并登录 [AI21 Studio](https://studio.ai21.com)
  - 点击 `用户头像` 菜单，点击 `API Key`
  - 复制并保存生成的 API 密钥

  <Image alt={'复制 API 密钥'} inStep src={'https://github.com/user-attachments/assets/a42ba52b-491e-4993-8e2f-217aa1776e0f'} />

  ### 步骤二：在 LobeChat 中配置 AI21 Labs

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `AI21labs` 的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/9336d6c5-2a83-4aa9-854e-75e245b665cb'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 AI21 Labs 的模型即可开始对话

  <Image alt={'选择 AI21 Labs 模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/63e5ced7-1d23-44e1-b933-cc3b5df47eab'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 AI21 Labs 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 AI21 Labs 提供的模型进行对话了。
