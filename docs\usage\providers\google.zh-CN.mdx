---
title: 在 LobeChat 中使用 Google Gemini API Key
description: 本文将指导你如何在 LobeChat 中配置并使用 Google Gemini，一个由 Google AI 创建的强大语言模型。
tags:
  - Google Gemini
  - Google AI
  - API 密钥
  - Web UI
---

# 在 LobeChat 中使用 Google Gemini

<Image alt={'在 LobeChat 中使用 Google Gemini'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/9a8dc1d4-152b-415f-a7cd-8f0c8fbb9913'} />

Gemini AI 是由 Google AI 创建的一组大型语言模型（LLM），以其在多模式理解和处理方面的尖端进步而闻名。它本质上是一个强大的人工智能工具，可以处理涉及不同类型数据的各种任务，而不仅仅是文本。

本文档将指导你如何在 LobeChat 中使用 Google Gemini:

<Steps>
  ### 步骤一：获取 Google 的 API 密钥

  - 访问并登录 [Google AI Studio](https://aistudio.google.com/)
  - 在 `获取 API 密钥` 菜单中 `创建 API 密钥`

  <Image alt={'生成 Google Gemini 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/ba3595e3-d9cb-4d0d-b414-8306b16df186'} />

  - 选择一个项目并创建 API 密钥，或者在新项目中创建 API 密钥

  <Image alt={'输入 API 密钥名称'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/fa725e49-4c17-4055-82bc-98a31e73fa54'} />

  - 在弹出的对话框中复制 API 密钥

  <Image alt={'复制 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/28d89add-cb18-4b86-9807-f2a5ed65ceba'} />

  ### 步骤二：在 LobeChat 中配置 OpenAI

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`Google Gemini`的设置项

  <Image alt={'LobeChat 中填写 Google Gemini API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/11442ce4-a615-49c4-937a-ca2ae93dd27c'} />

  - 打开 Google Gemini 并填入获得的 API 密钥
  - 为你的助手选择一个 Gemini 的模型即可开始对话

  <Image alt={' 选择并使用 Gemini 模型 '} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/ef9ed1b8-6828-4dd6-b86b-bb0b4fa40619'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Google Gemini 的费用政策。
  </Callout>
</Steps>

至此，你已经可以在 LobeChat 中使用 Google Gemini 啦。
