---
title: 在 LobeChat 中使用 Azure OpenAI API Key
description: 学习如何在 LobeChat 中配置和使用 Azure OpenAI 模型进行对话，包括获取 API 密钥和选择模型。
tags:
  - Azure OpenAI
  - API Key
  - Web UI
---

# 在 LobeChat 中使用 Azure OpenAI

<Image alt={'在 LobeChat 中使用 Azure OpenAI'} cover src={'https://github.com/lobehub/lobe-chat/assets/17870709/5efa34c2-6523-43e6-9ade-70ab5d802e13'} />

本文档将指导你如何在 LobeChat 中使用 [Azure OpenAI](https://oai.azure.com/):

<Steps>
  ### 步骤一：获取 Azure OpenAI API 密钥

  - 如果尚未注册，则必须注册 [Azure OpenAI 帐户](https://oai.azure.com/)。

  <Image alt={'注册 Azure OpenAI 帐户'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/a77b0fb2-87d7-4527-a804-2f7ad3634aa5'} />

  - 注册完毕后，转到 `Deployments` 页面，然后使用您选择的模型创建新部署。

  <Image alt={'选择的模型创建新部署'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/4fae3e6f-e680-4471-93c4-987c19d7170a'} />

  - 转到 `Chat` 页面，然后单击 `View Code` 以获取您的终结点和密钥。

  <Image alt={'转到 Chat 页面'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/ac10d9dd-a977-43fb-8397-b2bbdee6a1a1'} />

  <Image alt={'获取终结点和密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/ab94a7b5-6bc4-41e0-97bc-724ee8e315db'} />

  ### 步骤二：在 LobeChat 中配置 Azure OpenAI

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`Azure OpenAI`的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/63d9f6d4-5b78-4c65-8cd1-ff8b7f143406'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个 Azure OpenAI 的模型即可开始对话

  <Image alt={'选择 Azure OpenAI 模型并开始对话'} inStep src={'https://github.com/lobehub/lobe-chat/assets/17870709/ddb44517-8696-4492-acd9-25b590f6069c'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Azure OpenAI 的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Azure OpenAI 提供的模型进行对话了。
