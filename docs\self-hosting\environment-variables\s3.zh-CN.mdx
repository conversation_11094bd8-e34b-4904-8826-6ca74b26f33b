---
title: 配置 S3 存储服务
description: 了解如何配置 S3 存储服务以支持多模态 AI 会话和图片上传。
tags:
  - S3 存储
  - 多模态 AI
  - 图片上传
  - 文件存储
---

# 配置 S3 存储服务

LobeChat 支持多模态的 AI 会话，包括将图片、文件等非结构化数据上传给大模型的功能。为了优化存储和性能，我们使用 S3 兼容的文件存储服务来存储图片文件和支持文件上传 / 知识库功能。

## 核心环境变量

### `S3_ACCESS_KEY_ID`

- 类型：必填
- 描述：用于访问 S3 存储服务的访问密钥 ID
- 默认值：-
- 示例：`AKIAIOSFODNN7EXAMPLE`

### `S3_SECRET_ACCESS_KEY`

- 类型：必填
- 描述：用于访问 S3 存储服务的秘密访问密钥
- 默认值：-
- 示例：`wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY`

### `S3_ENDPOINT`

- 类型：必填
- 描述：存储桶的请求端点，对于 `virtual-host` 模式，不应包含前缀存储桶名称
- 默认值：-
- 示例：`https://cos.ap-chengdu.myqcloud.com`

### `S3_BUCKET`

- 类型：必填
- 描述：存储桶的名称
- 默认值：-
- 示例：`my-bucket`

### `S3_REGION`

- 类型：可选
- 描述：存储桶的区域
- 默认值：-
- 示例：`ap-chengdu`

### `S3_SET_ACL`

- 类型：可选
- 描述：是否在上传文件时设置 ACL 为 `public-read`
- 默认值：`1`
- 示例：`0`

### `S3_PUBLIC_DOMAIN`

- 类型：必填
- 描述：存储桶对外的访问域名，用于访问存储桶中的文件
- 默认值：-
- 示例：`https://files.example.com`

### `S3_ENABLE_PATH_STYLE`

- 类型：可选
- 描述：是否启用 S3 的 `path-style` 访问模式
- 默认值：`0`
- 示例：`1`

<Callout type={'info'}>
  `path-style` 和 `virtual-host` 在 S3 中是访问 bucket 和 object 的不同方式，URL 的结构和域名解析不太一样

  假设 S3 服务商的域名是 s3.example.net ，bucket 为 mybucket，object 为 config.env，具体区别如下：

  - path-style : `s3.example.net/mybucket/config.env`
  - virtual-host : `mybucket.s3.example.net/config.env`
</Callout>

### `LLM_VISION_IMAGE_USE_BASE64`

- 类型：可选
- 描述：设置为 1 则使用 base64 编码上传图片
- 默认值：undefined
- 示例：`1`

当设置为 `1` 时，LobeChat 会将图片转换为 base64 编码后上传到 LLM 模型中，当遇到如下错误时请考虑配置该环境变量为 1

```log
Route: [xai] ProviderBizError: Fetching images over plain http:// is not supported.
```
