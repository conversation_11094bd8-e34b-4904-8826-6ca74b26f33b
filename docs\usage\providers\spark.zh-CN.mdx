---
title: 在 LobeChat 中使用讯飞星火
description: 学习如何在 LobeChat 中配置和使用讯飞星火的API Key，以便开始对话和交互。
tags:
  - LobeChat
  - 讯飞星火
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用讯飞星火

<Image cover src={'https://github.com/user-attachments/assets/f3068287-8ade-4eca-9841-ea67d8ff1226'} />

[讯飞星火](https://xinghuo.xfyun.cn/)是科大讯飞推出的一款强大的 AI 大模型，具备跨领域的知识和语言理解能力，能够执行问答、对话和文学创作等多种任务。

本文将指导你如何在 LobeChat 中使用讯飞星火。

<Steps>
  ### 步骤一：获得讯飞星火的 API Key

  - 注册并登录 [讯飞开放平台](https://console.xfyun.cn/)
  - 创建一个应用

  <Image alt={'创建应用'} inStep src={'https://github.com/user-attachments/assets/1bf1a5f0-32ad-418c-a8d1-6c54740f50b9'} />

  - 选择一个大模型查看详情
  - 复制右上角 http 服务接口认证信息中的 `API Password`

  <Image alt={'复制 API 密钥'} inStep src={'https://github.com/user-attachments/assets/7239d611-1989-414b-a51c-444e47096d75'} />

  ### 步骤二：在 LobeChat 中配置讯飞星火

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到 `讯飞星火` 的设置项

  <Image alt={'填入 API 密钥'} inStep src={'https://github.com/user-attachments/assets/d693be02-e08c-43ae-8bde-1294f180aaf6'} />

  - 填入获得的 API 密钥
  - 为你的 AI 助手选择一个讯飞星火的模型即可开始对话

  <Image alt={'选择讯飞星火模型并开始对话'} inStep src={'https://github.com/user-attachments/assets/8910186f-4609-4798-a588-2780dcf8db60'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考讯飞星火的相关费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用讯飞星火提供的模型进行对话了。
