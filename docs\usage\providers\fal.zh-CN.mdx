---
title: 在 LobeChat 中使用 Fal API Key
description: >-
  学习如何在 LobeChat 中配置和使用 Fal API Key，使用 FLUX、Kling 等尖端模型进行 AI 图像和视频生成。

tags:
  - Fal
  - 图像生成
  - 视频生成
  - API Key
  - Web UI
---

# 在 LobeChat 中使用 Fal

<Image alt={'在 LobeChat 中使用 Fal'} cover src={'https://hub-apac-1.lobeobjects.space/docs/f253e749baaa2ccac498014178f93091.png'} />

[Fal.ai](https://fal.ai/) 是一个专门从事 AI 媒体生成的快速推理平台，提供包括 FLUX、Kling、HiDream 等在内的最先进图像和视频生成模型。本文将指导你如何在 LobeChat 中使用 Fal：

<Steps>
  ### 步骤一：获取 Fal API Key

  - 注册 [Fal.ai](https://fal.ai/) 账户；
  - 前往 [API Keys 控制台](https://fal.ai/dashboard/keys)，点击 **Add key** 创建新的 API 密钥；
  - 复制生成的 API Key 并妥善保存，它只会显示一次。

  <Image
    alt={'打开创建窗口'}
    inStep
    src={
'https://hub-apac-1.lobeobjects.space/docs/3f3676e7f9c04a55603bc1174b636b45.png'
}
  />

  <Image
    alt={'创建 API Key'}
    inStep
    src={
'https://hub-apac-1.lobeobjects.space/docs/214cc5019d9c0810951b33215349136e.png'
}
  />

  <Image
    alt={'获取 API Key'}
    inStep
    src={
'https://hub-apac-1.lobeobjects.space/docs/499a447e98dcc79407d56495d0305e2a.png'
}
  />

  ### 步骤二：在 LobeChat 中配置 Fal

  - 访问 LobeChat 的 `设置` 页面；
  - 在 `AI服务商` 下找到 `Fal` 的设置项；

  <Image alt={'填入 API 密钥'} inStep src={'https://hub-apac-1.lobeobjects.space/docs/fa056feecba0133c76abe1ad12706c05.png'} />

  - 粘贴获取到的 API Key；
  - 选择一个 Fal 模型（如 `Flux.1 Schnell`、`Flux.1 Kontext Dev`）用于图像或视频生成。

  <Image alt={'选择 Fal 模型进行媒体生成'} inStep src={'https://hub-apac-1.lobeobjects.space/docs/7560502f31b8500032922103fc22e69b.png'} />

  <Callout type={'warning'}>
    在使用过程中，你可能需要向 Fal 支付相应费用，请在大量调用前查阅 Fal 的官方计费政策。
  </Callout>
</Steps>

至此，你已经可以在 LobeChat 中使用 Fal 提供的先进图像和视频生成模型来创作精美的视觉内容了。
