---
title: 通过 Docker Compose 部署 LobeChat
description: 学习如何使用 Docker Compose 部署 LobeChat 服务，包括各种服务的配置教程。
tags:
  - Docker Compose
  - LobeChat
  - Docker 容器
  - 部署指引
---

# 使用 Docker Compose 部署 LobeChat 服务端数据库版本

<div style={{display:"flex", gap: 4}}>
  [![][docker-release-shield]][docker-release-link]

  [![][docker-size-shield]][docker-size-link]

  [![][docker-pulls-shield]][docker-pulls-link]
</div>

<Callout type="warning">
  **Docker 部署限制说明**
  Docker 及 Docker Compose 部署方案不支持通过环境变量注入 `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`，
  因此无法启用 Clerk 身份验证服务。建议替代方案：

  - 采用 Vercel 托管部署

  - 执行本地镜像构建流程
</Callout>

## 快速启动

<Callout type="info">
  **系统兼容性说明**

  - 支持 Unix 环境（Linux/macOS）的一键部署

  - Windows 用户需通过 [WSL 2](https://aka.ms/wsl) 运行

  - 一键启动脚本为首次部署专用，非首次部署请参考 [自定义部署](#自定义部署) 章节

  - 端口占用检查：确保 `3210`、`8000`、`9000`、`9001` 端口可用
</Callout>

执行以下命令初始化部署环境，目录 `lobe-chat-db` 将用于存放你的配置文件和后续的数据库文件。

```sh
mkdir lobe-chat-db && cd lobe-chat-db
```

获取并执行部署脚本：

```sh
bash <(curl -fsSL https://lobe.li/setup.sh) -l zh_CN
```

脚本支持以下部署模式，请根据你的需求选择相应的模式，并继续阅读文档的剩余部分。

- [本地模式（默认）](#本地模式)：仅能在本地访问，不支持局域网 / 公网访问，适用于初次体验；
- [端口模式](#端口模式)：支持局域网 / 公网的 `http` 访问，适用于无域名或内部办公场景使用；
- [域名模式](#域名模式)：支持局域网 / 公网在使用反向代理下的 `http/https` 访问，适用于个人或团队日常使用；

<Callout type="info">
  在脚本的选项提示 `(选项1/选项2)[选项1]` 中：`(选项1 / 选项2)`
  代表可以选择的选项，`[选项1]`代表默认选项，直接回车即可选择默认选项。
</Callout>

### 本地模式

<Steps>
  ### 在交互式脚本中完成剩余配置

  持续回车采用默认配置。

  ### 查看配置生成报告

  你需要在脚本运行结束后查看配置生成报告，包括 Casdoor 管理员的帐号、用户账号和它们的初始登录密码。

  <Callout type="warning">请使用用户账号登录 LobeChat，管理员账号仅用于管理 Casdoor。</Callout>

  ```log
  安全密钥生成结果如下：
  LobeChat:
    - URL: http://localhost:3210
    - Username: user
    - Password: c66f8c
  Casdoor:
    - URL: http://localhost:8000
    - Username: admin
    - Password: c66f8c
  Minio:
    - URL: http://localhost:9000
    - Username: admin
    - Password: 8c82ea41
  ```

  ### 启动 Docker

  ```sh
  docker compose up -d
  ```

  ### 检查日志

  ```sh
  docker logs -f lobe-chat
  ```

  如果你在容器中看到了以下日志，则说明已经启动成功：

  ```log
  [Database] Start to migration...
  ✅ database migration pass.
  -------------------------------------
    ▲ Next.js 14.x.x
    - Local:        http://localhost:3210
    - Network:      http://0.0.0.0:3210

   ✓ Starting...
   ✓ Ready in 95ms
  ```

  ### 访问应用

  通过 [http://localhost:3210](http://localhost:3210) 访问你的 LobeChat 服务。应用的账号密码在步骤`2`的报告中。
</Steps>

### 端口模式

<Steps>
  ### 在交互式脚本中完成剩余配置

  在端口模式中，你需要根据脚本提示完成：

  - 服务器 IP 地址设置：以便局域网 / 公网访问。
  - 安全密钥重新生成：我们强烈建议你重新生成安全密钥，如果你缺少脚本所需的密钥生成库，我们建议你参考 [自定义部署](#自定义部署) 章节对密钥进行修改。

  ### 查看配置生成报告

  你需要在脚本运行结束后查看配置生成报告，包括 Casdoor 管理员的帐号、用户账号和它们的初始登录密码。

  <Callout type="warning">请使用用户账号登录 LobeChat，管理员账号仅用于管理 Casdoor。</Callout>

  ```log
  安全密钥生成结果如下：
  LobeChat:
    - URL: http://your_server_ip:3210
    - Username: user
    - Password: 837e26
  Casdoor:
    - URL: http://your_server_ip:8000
    - Username: admin
    - Password: 837e26
  Minio:
    - URL: http://your_server_ip:9000
    - Username: admin
    - Password: dbac8440
  ```

  ### 启动 Docker

  ```sh
  docker compose up -d
  ```

  ### 检查日志

  ```sh
  docker logs -f lobe-chat
  ```

  如果你在容器中看到了以下日志，则说明已经启动成功：

  ```log
  [Database] Start to migration...
  ✅ database migration pass.
  -------------------------------------
    ▲ Next.js 14.x.x
    - Local:        http://your_server_ip:3210
    - Network:      http://0.0.0.0:3210
   ✓ Starting...
   ✓ Ready in 95ms
  ```

  ### 访问应用

  你可以通过 `http://your_server_ip:3210` 访问你的 LobeChat 服务。应用的账号密码在步骤`2`的报告中。

  <Callout type="warning">
    请注意，如果你的服务能够被公网访问，我们强烈建议你参考 [文档](https://lobehub.com/docs/self-hosting/advanced/auth/next-auth/casdoor) 关闭注册功能。
  </Callout>
</Steps>

### 域名模式

<Steps>
  ### 完成反向代理配置

  在域名模式中，你需要完成反向代理配置，并确保局域网 / 公网能访问到以下服务。请使用反向代理将以下服务端口映射到域名：

  | 域名                     | 反代端口   | 是否必选 |
  | ---------------------- | ------ | ---- |
  | `lobe.example.com`     | `3210` | 必选   |
  | `auth.example.com`     | `8000` | 必选   |
  | `minio.example.com`    | `9000` | 必选   |
  | `minio-ui.example.com` | `9001` |      |

  <Callout type="important">
    如果你使用如 [宝塔面板](https://www.bt.cn/) 等面板软件进行反向代理配置，
    你需要确保其对 `.well-known` 路径的请求不进行拦截，以确保 Casdoor 的 OAuth2 配置能够正常工作。
    这里提供一份针对 Casdoor 服务的 Nginx server 块的路径白名单配置：

    ```nginx
    location /.well-known/openid-configuration {
      proxy_pass http://localhost:8000;  # 转发到 localhost:8000
      proxy_set_header Host $host;  # 保留原始主机头
      proxy_set_header X-Real-IP $remote_addr;  # 保留客户端真实IP
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;  # 保留转发的IP
      proxy_set_header X-Forwarded-Proto $scheme;  # 保留请求协议
    }
    ```

    ⚠️ 请不要在此类面板软件的反向代理设置中开启任何形式的缓存，以免影响服务的正常运行。
    详情请见 [https://github.com/lobehub/lobe-chat/discussions/5986](https://github.com/lobehub/lobe-chat/discussions/5986)
  </Callout>

  ### 在交互式脚本中完成剩余配置

  在域名模式中，你需要根据脚本提示完成：

  - LobeChat 服务的域名设置：`lobe.example.com`
  - Minio 服务的域名设置：`minio.example.com`
  - Casdoor 服务的域名设置：`auth.example.com`
  - 选择访问协议：`http` 或 `https`
  - 安全密钥重新生成：我们强烈建议你重新生成安全密钥，如果你缺少脚本所需的密钥生成库，我们建议你参考 [自定义部署](#自定义部署) 章节对密钥进行修改。

    <Callout type="warning">
      以下问题可能导致你的服务无法正常访问：

      - 此处的域名配置需要与步骤`1`中的反向代理配置保持一致。

      - 如果你使用 Cloudflare 的域名解析服务并开启了 `全程代理`，请使用 `https` 协议。

      - 如果你使用了 HTTPS 协议，请确保你的域名证书已经正确配置，一键部署默认不支持自签发证书。
    </Callout>

  ### 查看配置生成报告

  你需要在脚本运行结束后查看配置生成报告，包括 Casdoor 管理员的初始登录密码。

  <Callout type="warning">请使用用户账号登录 LobeChat，管理员账号仅用于管理 Casdoor。</Callout>

  ```log
  安全密钥生成结果如下：
  LobeChat:
    - URL: https://lobe.example.com
    - Username: user
    - Password: 837e26
  Casdoor:
    - URL: https://auth.example.com
    - Username: admin
    - Password: 837e26
  Minio:
    - URL: https://minio.example.com
    - Username: admin
    - Password: dbac8440
  ```

  ### 启动 Docker

  ```sh
  docker compose up -d
  ```

  ### 检查日志

  ```sh
  docker logs -f lobe-chat
  ```

  如果你在容器中看到了以下日志，则说明已经启动成功：

  ```log
  [Database] Start to migration...
  ✅ database migration pass.
  -------------------------------------
    ▲ Next.js 14.x.x
    - Local:        https://localhost:3210
    - Network:      http://0.0.0.0:3210
    ✓ Starting...
    ✓ Ready in 95ms
  ```

  ### 访问应用

  你可以通过 `https://lobe.example.com` 访问你的 LobeChat 服务。应用的账号密码在步骤`3`的报告中。

  <Callout type="warning">
    请注意，如果你的服务能够被公网访问，我们强烈建议你参考 [文档](https://lobehub.com/docs/self-hosting/advanced/auth/next-auth/casdoor) 关闭注册功能。
  </Callout>
</Steps>

## 自定义部署

该章节主要为你介绍在不同的网络环境下自定义部署 LobeChat 服务必须要修改的配置。在开始前，你可以先下载 [Docker Compose 配置文件](https://raw.githubusercontent.com/lobehub/lobe-chat/HEAD/docker-compose/local/docker-compose.yml) 以及 [环境变量配置文件](https://raw.githubusercontent.com/lobehub/lobe-chat/HEAD/docker-compose/local/.env.zh-CN.example)。

```sh
curl -O https://raw.githubusercontent.com/lobehub/lobe-chat/HEAD/docker-compose/local/docker-compose.yml
curl -O https://raw.githubusercontent.com/lobehub/lobe-chat/HEAD/docker-compose/local/.env.zh-CN.example
mv .env.zh-CN.example .env
```

<Callout type="info">
  本章节并不包含所有完整变量，剩余的变量可以查阅
  [使用服务端数据库部署](/zh/docs/self-hosting/server-database) 。
</Callout>

### 预备知识

一般来讲，想要完整的运行 LobeChat 数据库版本，你需要至少拥有如下四个服务

- LobeChat 数据库版本自身
- 带有 PGVector 插件的 PostgreSQL 数据库
- 支持 S3 协议的对象存储服务
- 受 LobeChat 支持的 SSO 登录鉴权服务

这些服务可以通过自建或者在线云服务组合搭配，以满足不同层次的部署需求。本文中，我们提供了完全基于开源自建服务的 Docker Compose 配置，你可以直接使用这份配置文件来启动 LobeChat 数据库版本，也可以对之进行修改以适应你的需求。

我们默认使用 [MinIO](https://github.com/minio/minio) 作为本地 S3 对象存储服务，使用 [Casdoor](https://github.com/casdoor/casdoor) 作为本地鉴权服务。

<Callout type="warning">
  如果你的网络拓扑较为复杂，请先确保在你的网络环境中这些服务能够正常通讯。
</Callout>

### 必要配置

以下我们将介绍运行这些服务的必要配置：

1. Casdoor

- LobeChat 需要与 Casdoor 通讯，因此你需要配置 Casdoor 的 Issuer 。

```env
AUTH_CASDOOR_ISSUER=https://auth.example.com
```

该配置会影响 LobeChat 的登录鉴权服务，你需要确保 Casdoor 服务的地址正确。你可以在 [常见问题](#常见问题) 中找到该配置错误的常见现象及解决方案。

- 同时，你也需要在 Casdoor 中允许回调地址为 LobeChat 的地址：

请在 Casdoor 的 Web 面板的 `身份认证 -> 应用` -> `<应用ID，默认为 app-built-in>` -> `重定向URL` 中添加一行：

```
https://auth.example.com/api/auth/callback/casdoor
```

- Casdoor 需要在环境变量中提供访问的 Origin 信息：

```env
origin=https://auth.example.com
```

2. MinIO

- LobeChat 需要为 LLM 服务提供商提供文件对象的公网访问地址，因此你需要配置 MinIO 的 Endpoint 。

```env
S3_PUBLIC_DOMAIN=https://minio.example.com
S3_ENDPOINT=https://minio.example.com
```

3. PostgreSQL

该配置位于 `docker-compose.yml` 文件中，你需要配置数据库的名称和密码：

```yaml
services:
  lobe:
    environment:
      - 'DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgresql:5432/${LOBE_DB_NAME}'
```

## 常见问题

#### 无法正常登陆

请根据容器日志检查是否存在以下错误

```sh
docker logs -f lobe-chat
```

- r3: "response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)

```log
lobe-chat      | [auth][error] r3: "response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)
```

成因：该问题一般是由于你的反向代理配置不正确导致的，你需要确保你的反向代理配置不会拦截 Casdoor 的 OAuth2 配置请求。

解决方案：

- 请参考 [域名模式](#域名模式) 章节中的反向代理配置注意事项。

- 一个直接的排查方式，你可以直接访问 `https://auth.example.com/.well-known/openid-configuration`，如果

  - 返回了非 JSON 格式的数据，则说明你的反向代理配置错误。
  - 如果返回的 JSON 格式数据中的 `"issuer": "URL"` 字段不是你配置的 `https://auth.example.com`，则说明你的环境变量配置错误。

- TypeError: fetch failed

```log
lobe-chat      | [auth][error] TypeError: fetch failed
```

成因：LobeChat 无法访问鉴权服务。

解决方案：

- 请检查你的鉴权服务是否正常运行，以及 LobeChat 所在的网络是否能够访问到鉴权服务。

- 一个直接的排查方式，你可以在 LobeChat 容器的终端中，使用 `curl` 命令访问你的鉴权服务 `https://auth.example.com/.well-known/openid-configuration`，如果返回了 JSON 格式的数据，则说明你的鉴权服务正常运行。

## 拓展配置

为了完善你的 LobeChat 服务，你可以根据你的需求进行以下拓展配置。

### 使用 MinIO 存储 Casdoor 头像

允许用户在 Casdoor 中更换头像

1. 你需要首先在 `buckets` 中创建一个名为 `casdoor` 的桶，选择自定义策略，复制并粘贴如下内容（如果你修改了桶名，请自行查找替换）

   ```json
   {
     "Statement": [
       {
         "Effect": "Allow",
         "Principal": {
           "AWS": ["*"]
         },
         "Action": ["s3:GetBucketLocation"],
         "Resource": ["arn:aws:s3:::casdoor"]
       },
       {
         "Effect": "Allow",
         "Principal": {
           "AWS": ["*"]
         },
         "Action": ["s3:ListBucket"],
         "Resource": ["arn:aws:s3:::casdoor"],
         "Condition": {
           "StringEquals": {
             "s3:prefix": ["files/*"]
           }
         }
       },
       {
         "Effect": "Allow",
         "Principal": {
           "AWS": ["*"]
         },
         "Action": ["s3:PutObject", "s3:DeleteObject", "s3:GetObject"],
         "Resource": ["arn:aws:s3:::casdoor/**"]
       }
     ],
     "Version": "2012-10-17"
   }
   ```

2. 创建一个新的访问密钥，将生成的 `Access Key` 和 `Secret Key` 存储之

3. 在 Casdoor 的 `身份认证 -> 提供商` 中关联 MinIO S3 服务，以下是一个示例配置：

   ![casdoor](https://github.com/user-attachments/assets/71035610-0706-434e-9488-ab5819b55330)

   其中，客户端 ID、客户端密钥为上一步创建的访问密钥中的 `Access Key` 和 `Secret Key`，`**************` 应当被替换为 `your_server_ip`。

4. 在 Casdoor 的 `身份认证 -> 应用` 中，对 `app-built-in` 应用添加提供商，选择 `minio`，保存并退出

5. 你可以在 Casdoor 的 `身份认证 -> 资源` 中，尝试上传文件以测试配置是否正确

### 生产部署下从 `logto` 迁移至 `Casdoor`

适用于已经在生产环境下使用 `logto` 作为登录鉴权服务的用户

<Callout type="info">
  由于使用[Logto](https://logto.io/) 作为登录鉴权服务存在比较大的不稳定性。 因此，下文基于发布到 IP
  模式的教程，实现了使用 Casdoor 作为鉴权服务提供商的域名发布方案。
  本文剩余部分也将以其为例进行说明。如果你使用其他诸如 Logto
  等其他登录鉴权服务，流程应当相近，但请注意不同的登录鉴权服务的端口配置可能有所差异。
</Callout>

在下文中，我们假设在上述服务之外，你还运行了一层 **Nginx** 来进行反向代理、配置 SSL。

域名和配套服务端口说明如下：

- `lobe.example.com`：为你的 LobeChat 服务端域名，需要反向代理到 LobeChat 服务端口，默认为 `3210`
- `auth.example.com`：为你的 Logto UI 域名，需要反向代理到 Logto WebUI 服务端口，默认为 `8000`
- `minio.example.com`：为你的 MinIO API 域名，需要反向代理到 MinIO API 服务端口，默认为 `9000`
- `minio-ui.example.com`：可选，为你的 MinIO UI 域名，需要反向代理到 MinIO WebUI 服务端口，默认为 `9001`

#### 配置文件

```sh
bash <(curl -fsSL https://lobe.li/setup.sh) -l zh_CN
docker compose up -d
```

注意保存此时生成的新密码！

运行后会获得三个文件

- init\_data.json
- docker-compose.yml
- .env

接下来，修改配置文件以实现域名发布

1. 修改 `docker-compose.yml` 文件

   1. 修改 `minio`的`MINIO_API_CORS_ALLOW_ORIGIN`字段。

   ```yaml
   'MINIO_API_CORS_ALLOW_ORIGIN=https://lobe.example.com'
   ```

   2. 修改`casdoor`的`origin`字段。

   ```yaml
   origin: 'https://auth.example.com'
   ```

   3. 修改`lobe`的`environment`字段。

   ```yaml
   # - 'APP_URL=http://localhost:3210'
   - 'APP_URL=https://lobe.example.com'

   - 'NEXT_AUTH_SSO_PROVIDERS=casdoor'
   - 'KEY_VAULTS_SECRET=Kix2wcUONd4CX51E/ZPAd36BqM4wzJgKjPtz2sGztqQ='
   - 'NEXT_AUTH_SECRET=NX2kaPE923dt6BL2U8e9oSre5RfoT7hg'
   # - 'AUTH_URL=http://localhost:${LOBE_PORT}/api/auth'
   - 'AUTH_URL=https://lobe.example.com/api/auth'

   # - 'AUTH_CASDOOR_ISSUER=http://localhost:${CASDOOR_PORT}'
   - 'AUTH_CASDOOR_ISSUER=https://auth.example.com'

   - 'DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgresql:5432/${LOBE_DB_NAME}'
   # - 'S3_ENDPOINT=http://localhost:${MINIO_PORT}'
   - 'S3_ENDPOINT=https://minio.example.com'

   - 'S3_BUCKET=${MINIO_LOBE_BUCKET}'
   # - 'S3_PUBLIC_DOMAIN=http://localhost:${MINIO_PORT}'
   - 'S3_PUBLIC_DOMAIN=https://minio.example.com'

   - 'S3_ENABLE_PATH_STYLE=1'
   - 'LLM_VISION_IMAGE_USE_BASE64=1'
   ```

2. 修改 `.env` 文件

<Callout type="info">为了安全起见，修改 `.env` 文件中的 ROOT USER 的字段</Callout>

```sh
# MinIO S3 configuration
MINIO_ROOT_USER=XXXX
MINIO_ROOT_PASSWORD=XXXX
```

#### Postgres 数据库配置

你可以使用下述指令检查日志：

```sh
docker logs -f lobe-chat
```

<Callout type="tip">
  在我们官方的 Docker 镜像中，会在启动镜像前自动执行数据库 schema 的 migration
  ，我们的官方镜像承诺「空数据库 ->
  完整表」这一段自动建表的稳定性。因此我们建议你的数据库实例使用一个空表实例，进而省去手动维护表结构或者
  migration 的麻烦。
</Callout>

如果你在建表的时候出现了问题，你可以尝试使用如下命令强制移除数据库容器并重新启动：

```sh
docker compose down  # 停止服务
sudo rm -rf ./data   # 移除挂载的数据库数据
docker compose up -d # 重新启动
```

#### 登录鉴权服务配置

你需要首先访问 WebUI 来进行配置：

- 如果你按照前文配置了反向代理，打开 `https://auth.example.com`
- 否则，请在进行端口映射后，打开 `http://localhost:8000`

登录管理员账户

- 默认用户名为 admin
- 默认密码为 下载配置文件时生成的随机密码。如忘记可到 `init_data.json` 文件中找回

登入后执行如下操作

1. 在 `用户管理 -> 组织` 中，添加一个新的组织。名称与显示名称为 `Lobe Users`。其余保持默认即可。
2. 在 `身份认证 -> 应用` 中，添加一个新的应用。

- 名称与显示名称为 `LobeChat`。
- 组织为 `Lobe Users`。
- 重定向 URLS 中添加一行 为 `https://lobe.example.com/api/auth/callback/casdoor`。
- 关闭除密码外的登录方式 。
- 将客户端 ID 和客户端密钥分别填入 `.env`中的 `AUTH_CASDOOR_ID` 和 `AUTH_CASDOOR_SECRET` 中。
- (可选) 仿照`built-in`应用的配置，来设计登录和注册的页面外观。
- 保存并退出。

<Callout type="info">
  通过上述步骤，可以避免默认情况下所有用户均为管理员导致的不安全的情况。
</Callout>

#### S3 对象存储服务配置

本文以 MinIO 为例，解释配置过程，如果你使用的是其他 S3 服务商，请参照其文档进行配置。

<Callout type="warning">
  请记得注意配置对应 S3 服务商的 CORS 跨域配置，以确保 LobeChat 能够正常访问 S3 服务。

  在本文中，你需要允许 `https://lobe.example.com` 的跨域请求。这既可以在 MinIO WebUI 的 `Configuration - API - Cors Allow Origin` 中配置，也可以在 Docker Compose 中的 `minio - environment - MINIO_API_CORS_ALLOW_ORIGIN` 中配置。

  如果你使用第二种方法（这也是默认的方法）进行配置，你将无法再在 MinIO WebUI 中配置。
</Callout>

你需要首先访问 WebUI 来进行配置：

- 如果你按照前文配置了反向代理，打开 `https://minio-ui.example.com`
- 否则，请在进行端口映射后，打开 `http://localhost:9001`

1. 在登录界面输入你设置的 `MINIO_ROOT_USER` 和 `MINIO_ROOT_PASSWORD`，然后点击登录

2. 在左侧面板 User / Access Keys 处，点击 `Create New Access Key`，无需额外修改，将生成的 `Access Key` 和 `Secret Key` 填入你的 `.env` 文件中的 `S3_ACCESS_KEY_ID` 和 `S3_SECRET_ACCESS_KEY` 中

   <Image alt="创建 MinIO 访问密钥" src="https://github.com/user-attachments/assets/72f02ce5-9991-425b-9864-9113ee1ed6bf" />

3. 重启 LobeChat 服务：

   ```sh
   docker compose up -d
   ```

至此，你已经成功部署了 LobeChat 数据库版本，你可以通过 `https://lobe.example.com` 访问你的 LobeChat 服务。

#### 配置文件

为方便一键复制，在此汇总基于 casdoor 鉴权方案的域名方式下生产部署配置服务端数据库所需要的示例配置文件。

- `.env`

```sh
# Proxy, if you need it
# HTTP_PROXY=http://localhost:7890
# HTTPS_PROXY=http://localhost:7890

# Other environment variables, as needed. You can refer to the environment variables configuration for the client version, making sure not to have ACCESS_CODE.
# OPENAI_API_KEY=sk-xxxx
# OPENAI_PROXY_URL=https://api.openai.com/v1
# OPENAI_MODEL_LIST=...

# ===========================
# ====== Preset config ======
# ===========================
# if no special requirements, no need to change
LOBE_PORT=3210
CASDOOR_PORT=8000
MINIO_PORT=9000

# Postgres related, which are the necessary environment variables for DB
LOBE_DB_NAME=lobechat
POSTGRES_PASSWORD=uWNZugjBqixf8dxC

# Casdoor secret
AUTH_CASDOOR_ID=943e627d79d5dd8a22a1
AUTH_CASDOOR_SECRET=6ec24ac304e92e160ef0d0656ecd86de8cb563f1

# MinIO S3 configuration
MINIO_ROOT_USER=Joe
MINIO_ROOT_PASSWORD=Crj1570768

# Configure the bucket information of MinIO
MINIO_LOBE_BUCKET=lobe
S3_ACCESS_KEY_ID=dB6Uq9CYZPdWSZouPyEd
S3_SECRET_ACCESS_KEY=aPBW8CVULkh8bw1GatlT0GjLihcXHLNwRml4pieS
```

- `docker-compose.yml`

```yaml
name: lobe-chat-database
services:
  network-service:
    image: alpine
    container_name: lobe-network
    ports:
      - '${MINIO_PORT}:${MINIO_PORT}' # MinIO API
      - '9001:9001' # MinIO Console
      - '${CASDOOR_PORT}:${CASDOOR_PORT}' # Casdoor
      - '${LOBE_PORT}:3210' # LobeChat
    command: tail -f /dev/null
    networks:
      - lobe-network

  postgresql:
    image: pgvector/pgvector:pg16
    container_name: lobe-postgres
    ports:
      - '5432:5432'
    volumes:
      - './data:/var/lib/postgresql/data'
    environment:
      - 'POSTGRES_DB=${LOBE_DB_NAME}'
      - 'POSTGRES_PASSWORD=${POSTGRES_PASSWORD}'
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5
    restart: always
    networks:
      - lobe-network

  minio:
    image: minio/minio
    container_name: lobe-minio
    network_mode: 'service:network-service'
    volumes:
      - './s3_data:/etc/minio/data'
    environment:
      - 'MINIO_ROOT_USER=${MINIO_ROOT_USER}'
      - 'MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}'
      # - 'MINIO_API_CORS_ALLOW_ORIGIN=http://localhost:${LOBE_PORT}'
      - 'MINIO_API_CORS_ALLOW_ORIGIN=https://lobe.example.com'
    restart: always
    command: >
      server /etc/minio/data --address ":${MINIO_PORT}" --console-address ":9001"


  casdoor:
    image: casbin/casdoor
    container_name: lobe-casdoor
    entrypoint: /bin/sh -c './server --createDatabase=true'
    network_mode: 'service:network-service'
    depends_on:
      postgresql:
        condition: service_healthy
    environment:
      RUNNING_IN_DOCKER: 'true'
      driverName: 'postgres'
      dataSourceName: 'user=postgres password=${POSTGRES_PASSWORD} host=postgresql port=5432 sslmode=disable dbname=casdoor'
      # origin: 'http://localhost:${CASDOOR_PORT}'
      origin: 'https://auth.example.com'
      runmode: 'dev'
    volumes:
      - ./init_data.json:/init_data.json

  lobe:
    image: lobehub/lobe-chat-database
    container_name: lobe-chat-database
    network_mode: 'service:network-service'
    depends_on:
      postgresql:
        condition: service_healthy
      network-service:
        condition: service_started
      minio:
        condition: service_started
      casdoor:
        condition: service_started

    environment:
      # - 'APP_URL=http://localhost:3210'
      - 'APP_URL=https://lobe.example.com'

      - 'NEXT_AUTH_SSO_PROVIDERS=casdoor'
      - 'KEY_VAULTS_SECRET=Kix2wcUONd4CX51E/ZPAd36BqM4wzJgKjPtz2sGztqQ='
      - 'NEXT_AUTH_SECRET=NX2kaPE923dt6BL2U8e9oSre5RfoT7hg'
      # - 'AUTH_URL=http://localhost:${LOBE_PORT}/api/auth'
      - 'AUTH_URL=https://lobe.example.com/api/auth'

      # - 'AUTH_CASDOOR_ISSUER=http://localhost:${CASDOOR_PORT}'
      - 'AUTH_CASDOOR_ISSUER=https://auth.example.com'

      - 'DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgresql:5432/${LOBE_DB_NAME}'
      # - 'S3_ENDPOINT=http://localhost:${MINIO_PORT}'
      - 'S3_ENDPOINT=https://minio.example.com'

      - 'S3_BUCKET=${MINIO_LOBE_BUCKET}'
      # - 'S3_PUBLIC_DOMAIN=http://localhost:${MINIO_PORT}'
      - 'S3_PUBLIC_DOMAIN=https://minio.example.com'

      - 'S3_ENABLE_PATH_STYLE=1'
      - 'LLM_VISION_IMAGE_USE_BASE64=1'
    env_file:
      - .env
    restart: always

volumes:
  data:
    driver: local
  s3_data:
    driver: local

networks:
  lobe-network:
    driver: bridge
```

[docker-pulls-link]: https://hub.docker.com/r/lobehub/lobe-chat-database
[docker-pulls-shield]: https://img.shields.io/docker/pulls/lobehub/lobe-chat-database?color=45cc11&labelColor=black&style=flat-square
[docker-release-link]: https://hub.docker.com/r/lobehub/lobe-chat-database
[docker-release-shield]: https://img.shields.io/docker/v/lobehub/lobe-chat-database?color=369eff&label=docker&labelColor=black&logo=docker&logoColor=white&style=flat-square&sort=semver
[docker-size-link]: https://hub.docker.com/r/lobehub/lobe-chat-database
[docker-size-shield]: https://img.shields.io/docker/image-size/lobehub/lobe-chat-database?color=369eff&labelColor=black&style=flat-square&sort=semver
